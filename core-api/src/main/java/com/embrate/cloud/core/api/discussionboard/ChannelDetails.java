/**
 * 
 */
package com.embrate.cloud.core.api.discussionboard;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ChannelDetails {
	
	private int instituteId;
	
	private ChannelMetaData channelMetaData;

	private List<ThreadMetaData> threadMetaDataList;

	/**
	 * @param instituteId
	 * @param channelMetaData
	 * @param threadMetaDataList
	 */
	public ChannelDetails(int instituteId, ChannelMetaData channelMetaData, List<ThreadMetaData> threadMetaDataList) {
		this.instituteId = instituteId;
		this.channelMetaData = channelMetaData;
		this.threadMetaDataList = threadMetaDataList;
	}

	/**
	 * 
	 */
	public ChannelDetails() {
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the channelMetaData
	 */
	public ChannelMetaData getChannelMetaData() {
		return channelMetaData;
	}

	/**
	 * @param channelMetaData the channelMetaData to set
	 */
	public void setChannelMetaData(ChannelMetaData channelMetaData) {
		this.channelMetaData = channelMetaData;
	}

	/**
	 * @return the threadMetaDataList
	 */
	public List<ThreadMetaData> getThreadMetaDataList() {
		return threadMetaDataList;
	}

	/**
	 * @param threadMetaDataList the threadMetaDataList to set
	 */
	public void setThreadMetaDataList(List<ThreadMetaData> threadMetaDataList) {
		this.threadMetaDataList = threadMetaDataList;
	}

	@Override
	public String toString() {
		return "ChannelDetails [instituteId=" + instituteId + ", channelMetaData=" + channelMetaData
				+ ", threadMetaDataList=" + threadMetaDataList + "]";
	}

}
