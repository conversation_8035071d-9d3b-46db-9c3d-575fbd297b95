package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.institute.Time;

import java.util.UUID;

public class DimensionWiseDatesheetPayload {

    private int dimensionId;

    private Integer courseExamDate;

    private Time startTime;

    private Time endTime;

    private String syllabus;

    public DimensionWiseDatesheetPayload(int dimensionId, Integer courseExamDate, Time startTime, Time endTime, String syllabus) {
        this.dimensionId = dimensionId;
        this.courseExamDate = courseExamDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.syllabus = syllabus;
    }

    public DimensionWiseDatesheetPayload() {
    }

    public int getDimensionId() {
        return dimensionId;
    }

    public void setDimensionId(int dimensionId) {
        this.dimensionId = dimensionId;
    }

    public Integer getCourseExamDate() {
        return courseExamDate;
    }

    public void setCourseExamDate(Integer courseExamDate) {
        this.courseExamDate = courseExamDate;
    }

    public Time getStartTime() {
        return startTime;
    }

    public void setStartTime(Time startTime) {
        this.startTime = startTime;
    }

    public Time getEndTime() {
        return endTime;
    }

    public void setEndTime(Time endTime) {
        this.endTime = endTime;
    }

    public String getSyllabus() {
        return syllabus;
    }

    public void setSyllabus(String syllabus) {
        this.syllabus = syllabus;
    }

    @Override
    public String toString() {
        return "DimensionWiseDatesheetPayload{" +
                "dimensionId=" + dimensionId +
                ", courseExamDate=" + courseExamDate +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", syllabus='" + syllabus + '\'' +
                '}';
    }
}
