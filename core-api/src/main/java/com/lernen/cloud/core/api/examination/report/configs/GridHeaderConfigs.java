package com.lernen.cloud.core.api.examination.report.configs;

public class GridHeaderConfigs {

    private final boolean hideDimensionMaxMarks;
    private final boolean hideDimensionsRow;

    public GridHeaderConfigs(boolean hideDimensionMaxMarks) {
        this.hideDimensionMaxMarks = hideDimensionMaxMarks;
        this.hideDimensionsRow = false;
    }

    public GridHeaderConfigs(boolean hideDimensionMaxMarks, boolean hideDimensionsRow) {
        this.hideDimensionMaxMarks = hideDimensionMaxMarks;
        this.hideDimensionsRow = hideDimensionsRow;
    }

    public boolean isHideDimensionMaxMarks() {
        return hideDimensionMaxMarks;
    }

    public boolean isHideDimensionsRow() {
        return hideDimensionsRow;
    }

    @java.lang.Override
    public java.lang.String toString() {
        return "GridHeaderConfigs{" +
                "hideDimensionMaxMarks=" + hideDimensionMaxMarks +
                '}';
    }
}
