package com.lernen.cloud.core.api.sms.pcexpert;

import com.lernen.cloud.core.api.notification.NotificationStatus;

/**
 * 
 * <AUTHOR>
 *
 */
public enum PCExpertSMSStatus {

	DELIVERED("Delivered", NotificationStatus.DELIVERED), UNDELIVERED(
			"Undelivered", NotificationStatus.FAILED), FAILED("Failed",
					NotificationStatus.FAILED), SENT("Sent",
							NotificationStatus.SENT), INVALID_CONTACT(
									"Unidentified User",
									NotificationStatus.FAILED);

	private final String statusValue;
	private final NotificationStatus notificationStatus;

	private PCExpertSMSStatus(String statusValue,
			NotificationStatus notificationStatus) {
		this.statusValue = statusValue;
		this.notificationStatus = notificationStatus;
	}

	public String getStatusValue() {
		return statusValue;
	}

	public NotificationStatus getNotificationStatus() {
		return notificationStatus;
	}

	public static NotificationStatus getNotificationStatus(String statusValue) {
		for (PCExpertSMSStatus pcExpertSMSStatus : PCExpertSMSStatus.values()) {
			if (pcExpertSMSStatus.getStatusValue()
					.equalsIgnoreCase(statusValue)) {
				return pcExpertSMSStatus.getNotificationStatus();
			}
		}
		return null;
	}
}
