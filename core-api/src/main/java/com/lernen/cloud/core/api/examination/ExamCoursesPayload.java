package com.lernen.cloud.core.api.examination;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamCoursesPayload {
	private UUID courseId;

	private List<ExamDimensionValuesPayload> dimensionValuesPayloads;

	public ExamCoursesPayload() {

	}

	public ExamCoursesPayload(UUID courseId, List<ExamDimensionValuesPayload> dimensionValuesPayloads) {
		this.courseId = courseId;
		this.dimensionValuesPayloads = dimensionValuesPayloads;
	}

	public UUID getCourseId() {
		return courseId;
	}

	public void setCourseId(UUID courseId) {
		this.courseId = courseId;
	}

	public List<ExamDimensionValuesPayload> getDimensionValuesPayloads() {
		return dimensionValuesPayloads;
	}

	public void setDimensionValuesPayloads(List<ExamDimensionValuesPayload> dimensionValuesPayloads) {
		this.dimensionValuesPayloads = dimensionValuesPayloads;
	}

}
