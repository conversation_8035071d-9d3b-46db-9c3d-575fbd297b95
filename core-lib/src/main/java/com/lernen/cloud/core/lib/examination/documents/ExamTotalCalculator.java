package com.lernen.cloud.core.lib.examination.documents;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;

/**
 * Helper class for calculating totals in exam reports
 */
public class ExamTotalCalculator {
    
    private static final Logger logger = LogManager.getLogger(ExamTotalCalculator.class);
    
    /**
     * Calculates total marks for a specific exam-course combination
     * For numeric dimensions: sums up all non-total dimensions, or shows total dimension if no individual dimensions exist
     * For grade dimensions: shows "G" for total dimensions
     */
    public static String calculateTotalForExamCourse(UUID examId, UUID courseId,
                                                    Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap,
                                                    Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap,
                                                    Map<Integer, ExamDimension> examDimensionMap) {
        try {
            Map<UUID, Map<Integer, String>> examCourses = examCourseMaxMarksMap.get(examId);
            if (examCourses == null) {
                return "-";
            }

            Map<Integer, String> courseMaxMarks = examCourses.get(courseId);
            if (courseMaxMarks == null || courseMaxMarks.isEmpty()) {
                return "-";
            }

            Map<UUID, Map<Integer, String>> examCoursesMin = examCourseMinMarksMap.get(examId);
            Map<Integer, String> courseMinMarks = examCoursesMin != null ? examCoursesMin.get(courseId) : null;

            // Separate total and non-total dimensions
            List<Integer> nonTotalDimensions = new ArrayList<>();
            List<Integer> totalDimensions = new ArrayList<>();
            
            for (Integer dimensionId : courseMaxMarks.keySet()) {
                ExamDimension examDimension = examDimensionMap.get(dimensionId);
                if (examDimension != null) {
                    if (examDimension.isTotal()) {
                        totalDimensions.add(dimensionId);
                    } else {
                        nonTotalDimensions.add(dimensionId);
                    }
                }
            }

            // If there are non-total dimensions, calculate sum
            if (!nonTotalDimensions.isEmpty()) {
                return calculateSumOfDimensions(nonTotalDimensions, courseMaxMarks, courseMinMarks, examDimensionMap);
            }
            
            // If no non-total dimensions, use total dimension
            if (!totalDimensions.isEmpty()) {
                Integer totalDimensionId = totalDimensions.get(0); // Use first total dimension
                ExamDimension totalDimension = examDimensionMap.get(totalDimensionId);
                
                if (totalDimension != null && totalDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
                    return "G"; // For grade type total dimensions
                } else {
                    // For numeric total dimensions, show the actual values
                    String maxMarks = courseMaxMarks.get(totalDimensionId);
                    String minMarks = courseMinMarks != null ? courseMinMarks.get(totalDimensionId) : null;
                    
                    String minValue = StringUtils.isBlank(minMarks) ? "-" : minMarks;
                    String maxValue = StringUtils.isBlank(maxMarks) ? "-" : maxMarks;
                    
                    return String.format("(%s/%s)", minValue, maxValue);
                }
            }

            return "-";
            
        } catch (Exception e) {
            logger.error("Error while calculating total for exam {}, course {}", examId, courseId, e);
            return "-";
        }
    }

    /**
     * Calculates sum of non-total dimensions
     */
    public static String calculateSumOfDimensions(List<Integer> dimensionIds,
                                                 Map<Integer, String> courseMaxMarks,
                                                 Map<Integer, String> courseMinMarks,
                                                 Map<Integer, ExamDimension> examDimensionMap) {
        try {
            double totalMaxMarks = 0.0;
            double totalMinMarks = 0.0;
            boolean hasNumericValues = false;
            boolean hasGradeValues = false;

            for (Integer dimensionId : dimensionIds) {
                ExamDimension examDimension = examDimensionMap.get(dimensionId);
                if (examDimension == null) continue;

                String maxMarksStr = courseMaxMarks.get(dimensionId);
                String minMarksStr = courseMinMarks != null ? courseMinMarks.get(dimensionId) : null;

                if (examDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                    // Try to parse numeric values
                    try {
                        if (!StringUtils.isBlank(maxMarksStr)) {
                            totalMaxMarks += Double.parseDouble(maxMarksStr);
                            hasNumericValues = true;
                        }
                        if (!StringUtils.isBlank(minMarksStr)) {
                            totalMinMarks += Double.parseDouble(minMarksStr);
                        }
                    } catch (NumberFormatException e) {
                        // Skip invalid numeric values
                    }
                } else {
                    hasGradeValues = true;
                }
            }

            if (hasGradeValues && !hasNumericValues) {
                return "G"; // All dimensions are grades
            } else if (hasNumericValues) {
                return String.format("(%.1f/%.1f)", totalMinMarks, totalMaxMarks);
            }

            return "-";
            
        } catch (Exception e) {
            logger.error("Error while calculating sum of dimensions", e);
            return "-";
        }
    }
}
