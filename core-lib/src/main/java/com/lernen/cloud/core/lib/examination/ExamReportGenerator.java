package com.lernen.cloud.core.lib.examination;

import com.embrate.cloud.core.api.examination.utility.ExamReportFiltrationCriteria;
import com.embrate.cloud.core.api.examination.utility.MarksDisplayType;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.embrate.cloud.core.api.timetable.*;
import com.embrate.cloud.core.lib.timetable.TimetableManager;
import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseStudents;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.course.StudentCourses;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.embrate.cloud.core.lib.courses.CourseManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.lib.reports.utils.*;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.attendance.AttendanceUtils;
import com.lernen.cloud.core.utils.student.StudentDataUtils;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.examination.ExamReportCardConfigurationUtils;
import com.lernen.clould.core.lib.examination.rankrule.IRankCalculator;
import com.lernen.clould.core.lib.examination.rankrule.RankCalculatorFactory;
import com.lernen.cloud.core.lib.examination.documents.ExamCourseMarksData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.*;

import java.util.*;
import java.util.Map.Entry;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 */
public class ExamReportGenerator extends ReportGenerator {

    private static final Logger logger = LogManager.getLogger(ExamReportGenerator.class);
    private static final String DATE_FORMAT = "dd/MMM/yyyy";
    private static final String EMPTY_TEXT = "";
    private static final String NA = "NA";

    private static final String NO_MARKS_TEXT = "Ab";
    private static final String EMPTY_NO_MARKS_TEXT = EMPTY_TEXT;
    private static final String TOTAL = "Total";
    private static final String STRING = "String";
    private static final String INTEGER = "Integer";
    private static final String DOUBLE = "Double";
    private static final int CONTENT_SIZE = 10;
    private static final double PASSING_THRESHOLD = 0.33d;
    private final StudentManager studentManager;
    private final CourseManager courseManager;
    private final ExaminationManager examinationManager;
    private final InstituteManager instituteManager;
    private final UserPermissionManager userPermissionManager;
    private final TimetableManager timetableManager;
    private final ExamReportCardManager examReportCardManager;
    private final StaffManager staffManager;
    private final RankCalculatorFactory rankCalculatorFactory;

    private final ExamResultCalculatorFactory examResultCalculatorFactory;

    private static final Map<String, List<ReportHeaderAttribute>> REPORT_HEADERS = new HashMap<>();

    private final String TEACHER_WISE_RESULT_ANALYSIS_REPORT_HEADING = "This is to certify that Mr/Mrs %s Designation %s, Institute Id %s taught the class %s and his/her performance and result of the session %s are given below:";

    private final String[] SUBJECT_WISE_RANK_REPORT = {"Subject", "Section", "Roll No", "Student Name", "Admission No"};

    private final String[] OVERALL_RANK_REPORT = {"Section", "Roll No", "Student Name", "Admission No"};

    private final String[] RESULT_SUMMARY_REPORT_HEADER_2 = {EMPTY_TEXT, "Stud.", "Pass", "Fail", "Pass %", "Dist.", "I", "II", "III", "% Avg", "% High"};

    private static final List<String> COLOR_LIST = Arrays.asList("#2e7078", "#43a2ad", "#a5d4d9", "#7fadb2", "#afdbe0", "#e7f9fb");
    private final ReportHeaderAttribute[] EXAM_MARKS_REPORT_HEADER_1 = {ReportHeaderAttribute.EXAM_SR_NO,
            ReportHeaderAttribute.ADMISSION_No, ReportHeaderAttribute.ROLL_NO, ReportHeaderAttribute.NAME,
            ReportHeaderAttribute.FATHER_NAME, ReportHeaderAttribute.DOB, ReportHeaderAttribute.CLASS};

    private final ReportHeaderAttribute[] EXAM_MARKS_REPORT_HEADER_2 = {ReportHeaderAttribute.PERCENTAGE,
            ReportHeaderAttribute.GRADE, ReportHeaderAttribute.DIVISION, ReportHeaderAttribute.RESULT,
            ReportHeaderAttribute.RANK, ReportHeaderAttribute.ATTENDANCE, ReportHeaderAttribute.PARENT_REMARKS};

    static {
        REPORT_HEADERS.put(ExamReportType.EXAM_MARKS_REPORT.name(), Arrays.asList(ReportHeaderAttribute.EXAM_SR_NO,
                ReportHeaderAttribute.ADMISSION_No, ReportHeaderAttribute.ROLL_NO,
                ReportHeaderAttribute.NAME.markMandatory(),
                ReportHeaderAttribute.FATHER_NAME,
                ReportHeaderAttribute.DOB,ReportHeaderAttribute.CLASS,
                ReportHeaderAttribute.PERCENTAGE,ReportHeaderAttribute.GRADE,
                ReportHeaderAttribute.DIVISION, ReportHeaderAttribute.RESULT, ReportHeaderAttribute.RANK,
                ReportHeaderAttribute.ATTENDANCE, ReportHeaderAttribute.PARENT_REMARKS));
    }

    public ExamReportGenerator(StudentManager studentManager, CourseManager courseManager,
                               ExaminationManager examinationManager, InstituteManager instituteManager,
                               UserPermissionManager userPermissionManager,
                               TimetableManager timetableManager,
                               ExamReportCardManager examReportCardManager, StaffManager staffManager, RankCalculatorFactory rankCalculatorFactory) {
        this.studentManager = studentManager;
        this.courseManager = courseManager;
        this.examinationManager = examinationManager;
        this.instituteManager = instituteManager;
        this.userPermissionManager = userPermissionManager;
        this.timetableManager = timetableManager;
        this.examReportCardManager = examReportCardManager;
        this.staffManager = staffManager;
        this.examResultCalculatorFactory = new ExamResultCalculatorFactory();
        this.rankCalculatorFactory = rankCalculatorFactory;
    }

    public List<ReportHeaderAttribute> getReportHeader(ExamReportType reportType) {
        return REPORT_HEADERS.containsKey(reportType.name()) ? REPORT_HEADERS.get(reportType.name()) : new ArrayList<>();
    }

    public Map<String, List<ReportHeaderAttribute>> getReportHeader() {
        return REPORT_HEADERS;
    }

    public ReportDetails getClassExamReport(int instituteId, UUID examId, String classSectionIdStr,
                                            boolean excludeCoScholasticSubjects, boolean showDimensions, boolean showTotalColumnDimension,
                                            boolean showCoScholasticGrade,
                                            boolean showScholasticGrade, String requiredHeaders, ExamReportType reportType, DownloadFormat downloadFormat, UUID userId) {
        String reportName = "ClassExamReport";

        if(downloadFormat == DownloadFormat.EXCEL) {
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EXAM_EXCEL_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download exam reports in excel!"));
            }
        } else if (downloadFormat == DownloadFormat.PDF) {
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EXAM_PDF_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download exam reports in pdf!"));
            }
        }

        try {

            final Institute institute = instituteManager.getInstitute(instituteId);
            if (institute == null) {
                logger.error("Invalid institute for id {}", instituteId);
                return null;
            }
            final ExamDetails examDetails = examinationManager.getExamDetails(examId, instituteId);

            if (examDetails == null) {
                logger.error("Invalid exam for institute {}", instituteId);
                return null;
            }

            final ReportWorkbook workbook = new ReportWorkbook();

            reportName = examDetails.getStandard().getDisplayName() + " " + examDetails.getExamMetaData().getExamName();

            boolean displayRank = requiredHeaders.contains(ReportHeaderAttribute.RANK.getKey());
            boolean showParentRemarks = requiredHeaders.contains(ReportHeaderAttribute.PARENT_REMARKS.getKey());
            boolean displayAttendance = requiredHeaders.contains(ReportHeaderAttribute.ATTENDANCE.getKey());

            final Set<String> requiredHeaderAttributes = convertToRequiredHeaderAttributes(requiredHeaders);
            if(CollectionUtils.isEmpty(requiredHeaderAttributes)) {
                return getReportOutput(workbook, reportName);
            }
            final List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationManager
                    .getResolvedClassMarks(instituteId, examId, null, false);

            Set<Integer> sectionIdSet = convertStrToIntegerSet(classSectionIdStr);

            StringBuilder standardSectionNameStr = new StringBuilder();
            Standard standard = examDetails.getStandard();
            List<StandardSections> standardSectionsList = standard.getStandardSectionList();
            standardSectionNameStr.append(standard.getDisplayName());
            if(!CollectionUtils.isEmpty(sectionIdSet) && !CollectionUtils.isEmpty(standardSectionsList)) {
                standardSectionNameStr.append(" | Section(s): ");
                String delimeter = "";
                for(StandardSections standardSections : standardSectionsList) {
                    if(sectionIdSet.contains(standardSections.getSectionId())) {
                        standardSectionNameStr.append(delimeter).append(standardSections.getSectionName());
                        delimeter = ", ";
                    }
                }
            }

            final ReportSheet sheet = workbook.createSheet("Exam Report");
            final int startRowIndex = 1;
            final List<Integer> columnWidths = createClassExamReportHeader(workbook, sheet, startRowIndex, examDetails,requiredHeaderAttributes,
                    excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, showParentRemarks, displayRank, displayAttendance, showCoScholasticGrade);

            final CellIndexes cellRangeAddress = new CellIndexes(0, 0, 0, columnWidths.size() - 1);
            sheet.addMergedRegion(cellRangeAddress);

            final ReportFont titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 14);
            titleFont.setColor(BLACK_COLOR);

            // Create a CellStyle with the font
            final ReportCellStyle titleCellStyle = workbook.createCellStyle();
            titleCellStyle.setFont(titleFont);
            titleCellStyle.setAlignment(HorizontalAlignment.LEFT);

            final ReportRow secondRow = sheet.createRow(0);
            final ReportCell secondRowCell = secondRow.createCell(0);
            secondRowCell.setCellValue(institute.getInstituteName() + " | Compile sheet for " + examDetails.getExamMetaData().getExamName() + " | Class(s): " + standardSectionNameStr);
            secondRowCell.setCellStyle(titleCellStyle);

            if (CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
                resizeSheet(sheet, columnWidths);
                return getReportOutput(workbook, reportName);
            }

            final Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = getStudentExamMarksDetailMap(studentExamMarksDetailsList);

            List<Student> students = studentManager.getClassStudents(instituteId,
                    examDetails.getAcademicSession().getAcademicSessionId(), examDetails.getStandard().getStandardId());

            HashSet<UUID> studentSet = new HashSet<>();
            StudentDataUtils.sortStudentOnRollNumberAndName(students, studentSet);
            int academicSessionId = examDetails.getAcademicSession().getAcademicSessionId();
            Integer attendanceStartDate = examDetails.getExamMetaData().getAttendanceStartDate();
            Integer attendanceEndDate = examDetails.getExamMetaData().getAttendanceEndDate();

            Map<UUID, Map<AttendanceStatus, Integer>> studentAttendanceDetailsMap = studentManager.getStudentAttendanceDetails(
                    instituteId, academicSessionId, studentSet, attendanceStartDate, attendanceEndDate);

            final Map<UUID, LinkedHashMap<UUID, Course>> studentCourseMap = getStudentCourseMap(instituteId,
                    examDetails.getAcademicSession().getAcademicSessionId(), examDetails.getStandard().getStandardId());

            final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId,
                    examDetails.getAcademicSession().getAcademicSessionId(), examDetails.getStandard().getStandardId());

            int rowNum = startRowIndex + (showDimensions ? 3 : 2);
            int headerRowCount = rowNum;
            int count = 1;
            int startRow = rowNum;
            int endColumn = 0;
            final Map<UUID, Double> classPercentMap = new HashMap<>();

            final ReportCellStyle centerAlignCellStyle = workbook.createCellStyle();
            centerAlignCellStyle.setAlignment(HorizontalAlignment.CENTER);

            final ReportCellStyle leftAlignCellStyle = workbook.createCellStyle();
            leftAlignCellStyle.setAlignment(HorizontalAlignment.LEFT);

            for (final Student student : students) {

                if (!validStudentRow(student, sectionIdSet, studentExamMarksDetailsMap)) {
                    continue;
                }
                // Student session info is not present as part of
                // StudentExamMarksDetails so getting student info
                final StudentExamMarksDetails studentExamMarksDetails = studentExamMarksDetailsMap
                        .get(student.getStudentId());

                Integer studentSectionId = CollectionUtils.isEmpty(studentExamMarksDetails.getStudent()
                        .getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
                        studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse()
                                .getStandard().getStandardSectionList().get(0).getSectionId();

                if(!CollectionUtils.isEmpty(sectionIdSet) && !sectionIdSet.contains(studentSectionId)) {
                    continue;
                }

                final ReportRow row = sheet.createRow(rowNum++);
                int colNum = 0;
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.EXAM_SR_NO.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, String.valueOf(count), columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_No.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle,
                            studentExamMarksDetails.getStudent().getStudentBasicInfo().getAdmissionNumber(), columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ROLL_NO.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, StringUtils.isBlank(studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse()
                            .getRollNumber()) ? "" : studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber(), columnWidths);
                }
                createCell(row, colNum++, leftAlignCellStyle, studentExamMarksDetails.getStudent().getStudentBasicInfo().getName(),
                        columnWidths);
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FATHER_NAME.getKey())) {
                    createCell(row, colNum++, leftAlignCellStyle,
                            studentExamMarksDetails.getStudent().getStudentFamilyInfo() == null ? "" :
                                    StringUtils.isBlank(studentExamMarksDetails.getStudent().getStudentFamilyInfo().getFathersName()) ?
                                            "" : studentExamMarksDetails.getStudent().getStudentFamilyInfo().getFathersName(),
                            columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.DOB.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, studentExamMarksDetails.getStudent().getStudentBasicInfo().getDateOfBirth() == null ?
                            "" : DateUtils.getFormattedDate(studentExamMarksDetails.getStudent().getStudentBasicInfo()
                            .getDateOfBirth()), columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.CLASS.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle,
                            student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
                            columnWidths);
                }

                double maxMarks = 0d;
                double marksObtained = 0d;
                final Map<CourseType, List<ExamCourseMarks>> courseMarksMatrix = studentExamMarksDetails
                        .getCourseMarksMatrix();
                if (!CollectionUtils.isEmpty(courseMarksMatrix.get(CourseType.SCHOLASTIC))) {
                    final MarksRowData marksRowData = createCourseMarksColumns(instituteId,
                            courseMarksMatrix.get(CourseType.SCHOLASTIC), row, colNum, columnWidths, showDimensions,
                            showTotalColumnDimension, studentCourseMap.get(student.getStudentId()),
                            showScholasticGrade, courseTypeExamGrades.get(CourseType.SCHOLASTIC), centerAlignCellStyle);
                    colNum = marksRowData.getColNum();
                    maxMarks += marksRowData.getTotalMaxMarks();
                    marksObtained += marksRowData.getTotalObtainedMarks();
                }

                if (!excludeCoScholasticSubjects
                        && !CollectionUtils.isEmpty(courseMarksMatrix.get(CourseType.COSCHOLASTIC))) {
                    final MarksRowData marksRowData = createCourseMarksColumns(instituteId,
                            courseMarksMatrix.get(CourseType.COSCHOLASTIC), row, colNum, columnWidths, showDimensions,
                            showTotalColumnDimension, studentCourseMap.get(student.getStudentId()),
                            showCoScholasticGrade, courseTypeExamGrades.get(CourseType.COSCHOLASTIC), centerAlignCellStyle);
                    colNum = marksRowData.getColNum();
                }
                Double percentage = null;
                if (maxMarks > 0d) {
                    percentage = marksObtained / maxMarks;
                }

                /**
                 * Assuming if scholastic and coscholastic course are shown
                 * together then grading scheme is same. So using scholastic
                 * grading scheme only
                 */
                final List<ExamGrade> examGrades = courseTypeExamGrades.get(CourseType.SCHOLASTIC);
                final ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGrades, percentage);

                maxMarks = NumberUtils.formatDoubleNumber(maxMarks, 2);
                marksObtained = NumberUtils.formatDoubleNumber(marksObtained, 2);
                percentage = percentage == null ? null : NumberUtils.formatDoubleNumber(percentage * 100, 2);
//                ExamResultStatus examResultStatus = ExamMarksUtils.getExamResultStatus(percentage);

                IExamResultCalculator examResultCalculator = examResultCalculatorFactory.
                        getExamResultCalculator(instituteId, standard.getStandardId());
                ExamResultStatus examResultStatus = examResultCalculator.getExamResult(examId, courseMarksMatrix.get(CourseType.SCHOLASTIC), percentage, new HashSet<>());


                if (percentage != null && Double.compare(percentage, 0d) >= 0) {
                    /**
                     * rank will only be assigned when result status is PASS and PASS_WITH_GRACE
                     */
                    if(examResultStatus == ExamResultStatus.PASS
                            || examResultStatus == ExamResultStatus.PASS_WITH_GRACE) {
                        classPercentMap.put(student.getStudentId(), percentage);
                    }
                }

                createCell(row, colNum++, centerAlignCellStyle, NumberUtils.formatDouble(maxMarks), columnWidths);
                createCell(row, colNum++, centerAlignCellStyle, NumberUtils.formatDouble(marksObtained), columnWidths);
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PERCENTAGE.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, percentage == null ? "" : NumberUtils.formatDouble(percentage), columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.GRADE.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, examGrade == null ? "" : examGrade.getGradeName(), columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.DIVISION.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, ExamMarksUtils.getDivision(examResultStatus, percentage), columnWidths);
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.RESULT.getKey())) {
                    createCell(row, colNum++, centerAlignCellStyle, examResultStatus == null ? "" : examResultStatus.getDisplayName(), columnWidths);
                }

                count++;
                endColumn = colNum;
            }

            /**
             * Creates new column which contains rank of the students
             */
            int totalColumns = endColumn;
            if(displayRank || displayAttendance || showParentRemarks) {
                final Map<Double, Integer> classRankMap = ExamMarksUtils.getClassRankMap(classPercentMap);
                for (final Student student : students) {
                    if (!validStudentRow(student, sectionIdSet, studentExamMarksDetailsMap)) {
                        continue;
                    }
                    int colNum = endColumn;
                    final ReportRow row = sheet.getRow(startRow++);
                    if (displayRank) {
                        IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
                        final Integer rank = rankCalculator.computeRank(classPercentMap, student.getStudentId());
                        createCell(row, colNum++, centerAlignCellStyle, rank == null ? "" : String.valueOf(rank), columnWidths);
                    }

                    if (displayAttendance) {
                        String attendance = AttendanceUtils.getAttendanceDetails(studentAttendanceDetailsMap, student.getStudentId());
                        createCell(row, colNum++, centerAlignCellStyle, attendance, columnWidths);
                    }

                    if(showParentRemarks){
                        createCell(row, colNum++, centerAlignCellStyle, EMPTY_TEXT, columnWidths);
                    }

                    totalColumns = colNum;
                }
            }

            resizeSheet(sheet, columnWidths);
//            return getReportOutput(workbook, reportName, PageSize.A3.rotate());

            return getReportOutput(workbook, reportName, PageSize.A3.rotate(), totalColumns, headerRowCount, institute);

        } catch (final Exception e) {
            logger.error("Error while generating {} report for instituteId {}, examId {}", reportName, instituteId,
                    examId, e);
        }
        return null;
    }

    public ReportDetails generateExamConsolidatedReport(int instituteId, int academicSessionId, UUID standardId, Integer sectionId, UUID examId,
                                                        boolean excludeCoScholasticSubjects, boolean showDimensions, boolean showTotalColumnDimension,
                                                        DownloadFormat downloadFormat, UUID userId) {
        String reportName = "ExamConsolidatedReport";
        if(downloadFormat == DownloadFormat.EXCEL) {
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EXAM_EXCEL_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download exam reports in excel!"));
            }
        } else if (downloadFormat == DownloadFormat.PDF) {
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EXAM_PDF_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download exam reports in pdf!"));
            }
        }

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_EXAM_CONSOLIDATED_REPORT);

        try {
            Institute institute = instituteManager.getInstitute(instituteId);
            AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
            String academicSessionDisplayName = academicSession.getShortYearDisplayName();
            List<Standard> standardList = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
            String standardName = "";
            StringBuilder sectionNames = new StringBuilder();

            for(Standard standard : standardList) {
                if(standard.getStandardId().equals(standardId)) {
                    standardName = standard.getDisplayName();
                    List<StandardSections> standardSectionsList = standard.getStandardSectionList();
                    if(sectionId != null && !CollectionUtils.isEmpty(standardSectionsList)) {
                        String delimeter = "";
                        for(StandardSections standardSections : standardSectionsList) {
                            if(sectionId == standardSections.getSectionId()) {
                                sectionNames.append(delimeter).append(standardSections.getSectionName());
                                delimeter = ", ";
                            }
                        }
                    }
                    break;
                }
            }

            final ReportWorkbook workbook = new ReportWorkbook();
            final ReportSheet sheet = workbook.createSheet("Exam Report");
            ExamNode defaultExamStructureNode = getExamNode(instituteId, academicSessionId, standardId, examId);
            if(defaultExamStructureNode == null) {
                return getReportOutput(workbook, reportName);
            }

            Map<UUID, ExamNode> examForestMap = new HashMap<UUID, ExamNode>();
            getExamForestMap(defaultExamStructureNode, examForestMap);

            String examName = defaultExamStructureNode.getExamMetaData().getExamName();
            final int startRowIndex = 1;
            final ReportRow secondRow = sheet.createRow(0);

            final ReportFont titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 14);
            titleFont.setColor(BLACK_COLOR);

            // Create a CellStyle with the font
            final ReportCellStyle titleCellStyle = workbook.createCellStyle();
            titleCellStyle.setFont(titleFont);
            titleCellStyle.setAlignment(HorizontalAlignment.LEFT);

            final ReportCell secondRowCell = secondRow.createCell(0);

            String heading = "";
            if(!StringUtils.isBlank(academicSessionDisplayName)) {
                heading += " | Session : " + academicSessionDisplayName;
            }
            if(!StringUtils.isBlank(standardName)) {
                heading+= " | Class : " + standardName;
            }
            if(!StringUtils.isBlank(sectionNames)) {
                heading+= " | Section : " + sectionNames;
            }
            if(!StringUtils.isBlank(examName)) {
                heading+= " | Exam : " + examName;
            }

            secondRowCell.setCellValue("Exam Consolidated Marks Report" + heading);
            secondRowCell.setCellStyle(titleCellStyle);

            final List<Integer> columnWidths = new ArrayList<Integer>();
            LinkedHashMap<UUID, List<UUID>> courseExamSequenceList = new LinkedHashMap<>();

            //CourseId Set<ExamId>
            Map<UUID, Set<UUID>> courseExamListMap = fetchCourseExamsValidDatas(instituteId, academicSessionId, standardId);
//            Map<Course, Map<Exam, List<Dimension>>>
            //First element will be rowNumber, second will be column number
            Pair<Integer, Integer> rowAndColNumber = createClassCourseWiseExamReportHeader(workbook, sheet, startRowIndex, defaultExamStructureNode, columnWidths,
                    excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, courseExamSequenceList, courseExamListMap);

            Map<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsMap =
                    examinationManager.getResolvedClassMarksMap(instituteId, examId, null, sectionId == null ? null : new HashSet<>(Arrays.asList(sectionId)), true, false);

            final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId, academicSessionId, standardId);

            ReportFont headerFont = workbook.createFont();
            headerFont.setFontHeightInPoints((short) 12);
            headerFont.setColor(BLACK_COLOR);

            // Create a CellStyle with the font
            ReportCellStyle headerCellStyle = workbook.createCellStyle();
            headerCellStyle.setFont(headerFont);
            int startHeaderRowIndex = rowAndColNumber.getKey();
            startHeaderRowIndex = sheet.getLastRowNum() + 1;

            int headerRowCount = startHeaderRowIndex;
            int totalColumns = rowAndColNumber.getValue();

            final CellIndexes cellRangeAddress = new CellIndexes(0, 0, 0, totalColumns - 1);
            sheet.addMergedRegion(cellRangeAddress);

            //Student, Exam, Marks for one course --> Student, Course, Exam, Marks for one course
            Map<UUID, Student> studentMap = new HashMap<>();
            Map<UUID, Map<UUID, Map<UUID, ExamCourseMarks>>> studentExamMarksDetailsTwoDMap = getStudentCourseExamMarksDetailsMap(studentExamMarksDetailsMap, examForestMap, studentMap);
            int i = 0;
            for (Entry<UUID, Map<UUID, Map<UUID, ExamCourseMarks>>> studentExamMarksDetailMapEntry : studentExamMarksDetailsTwoDMap.entrySet()) {
                if (studentExamMarksDetailMapEntry == null || studentExamMarksDetailMapEntry.getValue() == null ||
                        CollectionUtils.isEmpty(studentExamMarksDetailMapEntry.getValue().entrySet())) {
                    continue;
                }
                i++;
                int cellIndex = 0;
                UUID studentId = studentExamMarksDetailMapEntry.getKey();
                Student student = studentMap.get(studentId);
                final ReportRow row = sheet.createRow(startHeaderRowIndex++);
                createCell(row, cellIndex++, headerCellStyle, i, columnWidths);
                createCell(row, cellIndex++, headerCellStyle, student.getStudentBasicInfo().getName(), columnWidths);
                createCell(row, cellIndex++, headerCellStyle, student.getStudentAcademicSessionInfoResponse() == null ? "-" :
                        StringUtils.isBlank(student.getStudentAcademicSessionInfoResponse().getRollNumber()) ? "-" :
                                student.getStudentAcademicSessionInfoResponse().getRollNumber(), columnWidths);
                createCell(row, cellIndex++, headerCellStyle, student.getStudentBasicInfo() == null ? "-" :
                        StringUtils.isBlank(student.getStudentBasicInfo().getAdmissionNumber()) ? "-" :
                                student.getStudentBasicInfo().getAdmissionNumber(), columnWidths);
                //Map<Course, Map<Exam, StudentMarksDetails>>
                Map<UUID, Map<UUID, ExamCourseMarks>> studentCourseExamMarkMap = studentExamMarksDetailMapEntry.getValue();
                for(Entry<UUID, List<UUID>> courseExamSequenceEntry : courseExamSequenceList.entrySet()) {
                    UUID courseId = courseExamSequenceEntry.getKey();
                    List<UUID> examSequenceList = courseExamSequenceEntry.getValue();
                    Map<UUID, ExamCourseMarks> studentExamCourseMarksMap = studentCourseExamMarkMap.get(courseId);
                    cellIndex = writeCourseMarksRow(studentExamCourseMarksMap, examSequenceList, examForestMap, row, cellIndex, headerCellStyle, columnWidths, courseTypeExamGrades);
                }
            }

//            resizeSheet(sheet, columnWidths);
            return getReportOutput(workbook, reportName, PageSize.A4.rotate(), totalColumns, headerRowCount, institute, true, true, true);

        } catch (final Exception e) {
            logger.error("Error while generating {} report for instituteId {}", reportName, instituteId, e);
        }
        return null;
    }

    private Map<UUID, Set<UUID>> fetchCourseExamsValidDatas(int instituteId, int academicSessionId, UUID standardId) {

        Map<UUID, Set<UUID>> courseExamListMap = new HashMap<>();
        final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(instituteId, academicSessionId, standardId);
        StandardMetadata standardMetadata = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        boolean isScholasticGradingEnabled = standardMetadata.isScholasticGradingEnabled();
        boolean isCoScholasticGradingEnabled = standardMetadata.isCoScholasticGradingEnabled();
        if(CollectionUtils.isEmpty(examCoursesDataList)) {
            return courseExamListMap;
        }
        for(ExamCoursesData examCoursesData : examCoursesDataList) {
            ExamMetaData examMetaData = examCoursesData.getExamMetaData();
            if(examMetaData == null) {
                continue;
            }
            UUID examId = examMetaData.getExamId();
            if(examId == null) {
                continue;
            }
            List<ExamCourse> examCourseList = examCoursesData.getExamCourses();
            if(CollectionUtils.isEmpty(examCourseList)) {
                continue;
            }
            for(ExamCourse examCourse : examCourseList) {
                Course course = examCourse.getCourse();
                if(course == null) {
                    continue;
                }
                CourseType courseType = course.getCourseType();
                List<ExamDimensionValues> examDimensionValuesList = examCourse.getExamDimensionValues();
                if(CollectionUtils.isEmpty(examDimensionValuesList)) {
                    continue;
                }
                for(ExamDimensionValues examDimensionValues : examDimensionValuesList) {
                    /**
                     * filtering total dimension for scholastic and co-scholastic when grading is not enable
                     * & filtering total dimension for scholastic and co-scholastic when grading is enable and evaluation type is number
                     */
                    if(courseType == CourseType.SCHOLASTIC
                            && ((!isScholasticGradingEnabled && examDimensionValues.getExamDimension().isTotal())
                            || (isScholasticGradingEnabled && examDimensionValues.getExamDimension().isTotal()
                            && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER))) {
                        continue;
                    }
                    if(courseType == CourseType.COSCHOLASTIC
                            && ((!isCoScholasticGradingEnabled && examDimensionValues.getExamDimension().isTotal())
                            || (isCoScholasticGradingEnabled && examDimensionValues.getExamDimension().isTotal()
                            && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER))) {
                        continue;
                    }
                    /**
                     * filtering non-total dimension and
                     * those course + exam + dimension set whose marks should be filled (EvaluationType == NUMBER)
                     * but max marks is not filled
                     */
                    if(!examDimensionValues.getExamDimension().isTotal() &&
                            (examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER
                                    && examDimensionValues.getMaxMarks() == null)) {
                        continue;
                    }
                    if(!courseExamListMap.containsKey(course.getCourseId())) {
                        courseExamListMap.put(course.getCourseId(), new HashSet<>());
                    }
                    courseExamListMap.get(course.getCourseId()).add(examId);
                }
            }
        }
        return courseExamListMap;
    }

    private int writeCourseMarksRow(Map<UUID, ExamNode> examForestMap, List<Integer> columnWidths,
                                    Map<UUID, Map<Integer, ExamDimensionValues>> examDimensionValuesMap, List<UUID> examSequenceList,
                                    int cellIndex, ReportRow row, ReportCellStyle headerCellStyle) {

        for (UUID examId : examSequenceList) {

            ExamNode examNode = examForestMap.get(examId);
            boolean leafExam = CollectionUtils.isEmpty(examNode.getChildren()) || examNode.getChildren().size() < 2;
            if (!examDimensionValuesMap.containsKey(examId)) {
                cellIndex = writeCourseMarksColumns(EMPTY_NO_MARKS_TEXT, EMPTY_NO_MARKS_TEXT, leafExam, row, cellIndex, headerCellStyle, columnWidths);
                continue;
            }
            Map<Integer, ExamDimensionValues> dimensionValuesMap = examDimensionValuesMap.get(examId);

            if (dimensionValuesMap == null || CollectionUtils.isEmpty(dimensionValuesMap.entrySet())) {
                cellIndex = writeCourseMarksColumns(EMPTY_NO_MARKS_TEXT, EMPTY_NO_MARKS_TEXT, leafExam, row, cellIndex, headerCellStyle, columnWidths);
                continue;
            }

            for (Entry<Integer, ExamDimensionValues> dimensionValuesEntry : dimensionValuesMap.entrySet()) {
                ExamDimensionValues examDimensionValues = dimensionValuesEntry.getValue();
                if (!examDimensionValues.getExamDimension().isTotal()) {
                    continue;
                }
                String gradeStr = "";
                String marksText = examDimensionValues.getMaxMarks() == null ? EMPTY_NO_MARKS_TEXT : NumberUtils.formatDouble(
                        examDimensionValues.getMaxMarks(), 2);
                cellIndex = writeCourseMarksColumns(marksText, gradeStr, leafExam, row, cellIndex, headerCellStyle, columnWidths);
            }

        }
        return cellIndex;
    }

    private ExamNode getExamNode(int instituteId, int academicSessionId, UUID standardId, UUID examId) {
        final List<ExamNode> examForest = examinationManager.getClassExamsForest(standardId, academicSessionId,
                instituteId, true);
        System.out.println(examForest.size());
        for(ExamNode examNode : examForest) {
            if(examNode == null) {
                continue;
            }
            System.out.println(examNode.toString());
            if(examNode.getExamMetaData().getExamId().equals(examId)) {
                return examNode;
            }
            for(ExamNode childExamNode : examNode.getChildren()) {
                System.out.println("inside child");
                if(childExamNode == null) {
                    continue;
                }
                ExamNode finalExamNode = getExamNode(examId, childExamNode);
                System.out.println("finalExamNode " + finalExamNode);
                if(finalExamNode != null) {
                    System.out.println("returning finalExamNode 716");
                    return finalExamNode;
                }
            }
        }
        System.out.println("returning null 721");
        return null;
    }

    private ExamNode getExamNode(UUID examId, ExamNode examNode) {
        System.out.println("examNode - " + examNode.getExamMetaData().getExamName());
        System.out.println("examId" + examId);
        if(examNode.getExamMetaData().getExamId().equals(examId)) {
            System.out.println("inside if 727");
            return examNode;
        }
        if(!CollectionUtils.isEmpty(examNode.getChildren())) {
            System.out.println("childern not empty");
            for (ExamNode childExamNode : examNode.getChildren()) {
                System.out.println("childExamNode - " + childExamNode.getExamMetaData().getExamName());
                ExamNode finalExamNode = getExamNode(examId, childExamNode);
                if (finalExamNode != null) {
                    System.out.println("returning finalExamNode 736");
                    return finalExamNode;
                }
            }
        }
        System.out.println("returning null 740");
        return null;
    }

    private int writeCourseMarksColumns(String totalText, String gradeText, boolean leafExam, ReportRow row, int cellIndex, ReportCellStyle headerCellStyle, List<Integer> columnWidths) {
        createCell(row, cellIndex++, headerCellStyle, totalText, columnWidths);
        if (!leafExam) {
            createCell(row, cellIndex++, headerCellStyle, gradeText, columnWidths);
        }
        return cellIndex;
    }

    private int writeCourseMarksRow(Map<UUID, ExamCourseMarks> studentExamCourseMarksMap, List<UUID> examSequenceList, Map<UUID, ExamNode> examForestMap,
                                    ReportRow row, int cellIndex, ReportCellStyle headerCellStyle, List<Integer> columnWidths, Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {

        for (UUID examId : examSequenceList) {
            ExamNode examNode = examForestMap.get(examId);
//            boolean leafExam = examNode != null && (CollectionUtils.isEmpty(examNode.getChildren()) || examNode.getChildren().size() < 2);
            boolean leafExam = examNode != null && (CollectionUtils.isEmpty(examNode.getChildren()));
            if (!studentExamCourseMarksMap.containsKey(examId)) {
                cellIndex = writeCourseMarksColumns(EMPTY_NO_MARKS_TEXT, EMPTY_NO_MARKS_TEXT, leafExam, row, cellIndex, headerCellStyle, columnWidths);
                continue;
            }
            ExamCourseMarks examCourseMarks = studentExamCourseMarksMap.get(examId);

            if (CollectionUtils.isEmpty(examCourseMarks.getExamDimensionObtainedValues())) {
                cellIndex = writeCourseMarksColumns(EMPTY_NO_MARKS_TEXT, EMPTY_NO_MARKS_TEXT, leafExam, row, cellIndex, headerCellStyle, columnWidths);
                continue;
            }

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
                if (!examDimensionObtainedValues.getExamDimension().isTotal()) {
                    continue;
                }
                String gradeStr = "";
                if (examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                    Double percentage = null;
                    Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                    Double marksObtained = examDimensionObtainedValues.getObtainedMarks();
                    if (maxMarks != null && marksObtained != null && maxMarks > 0d) {
                        percentage = marksObtained / maxMarks;
                    }
                    /**
                     * Assuming if scholastic and co-scholastic course are shown
                     * together then grading scheme is same. So using scholastic
                     * grading scheme only
                     */
                    final List<ExamGrade> examGrades = courseTypeExamGrades.get(examCourseMarks.getCourse().getCourseType());
                    final ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGrades, percentage);
                    gradeStr = examGrade == null ? EMPTY_NO_MARKS_TEXT : StringUtils.isBlank(examGrade.getGradeName()) ? EMPTY_NO_MARKS_TEXT : examGrade.getGradeName();
                } else {
                    gradeStr = examDimensionObtainedValues.getObtainedGrade() == null ? EMPTY_NO_MARKS_TEXT :
                            StringUtils.isBlank(examDimensionObtainedValues.getObtainedGrade().getGradeName()) ? EMPTY_NO_MARKS_TEXT :
                                    examDimensionObtainedValues.getObtainedGrade().getGradeName();
                }

                String marksText = examDimensionObtainedValues.getObtainedMarks() == null ? EMPTY_NO_MARKS_TEXT : NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 2);
                cellIndex = writeCourseMarksColumns(marksText, gradeStr, leafExam, row, cellIndex, headerCellStyle, columnWidths);
            }
        }
        return cellIndex;
    }

    private void getExamForestMap(ExamNode examNode, Map<UUID, ExamNode> examNodeMap) {
        if (examNode == null || CollectionUtils.isEmpty(examNode.getChildren())) {
            return;
        }
        if (!examNodeMap.containsKey(examNode.getExamMetaData().getExamId())) {
            examNodeMap.put(examNode.getExamMetaData().getExamId(), examNode);
        }
        for (ExamNode childExamNode : examNode.getChildren()) {
            if (childExamNode == null) {
                continue;
            }
            UUID examId = childExamNode.getExamMetaData().getExamId();
            if (!examNodeMap.containsKey(examId)) {
                examNodeMap.put(examId, childExamNode);
            }
            getExamForestMap(childExamNode, examNodeMap);
        }
    }

    private Map<UUID, Map<UUID, Map<UUID, ExamCourseMarks>>> getStudentCourseExamMarksDetailsMap(
            Map<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsMap, Map<UUID, ExamNode> examNodeMap, Map<UUID, Student> studentMap) {
        //ExamId, Student Details --> Student, Course, Exam, Marks for one course
        /**
         *  Map of student, exam and details of students exam marks for one subject
         */
        Map<UUID, Map<UUID, Map<UUID, ExamCourseMarks>>> studentExamMarksDetailsTwoDMapTemp = new HashMap<>();

        for (Entry<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsEntry : studentExamMarksDetailsMap.entrySet()) {
            UUID examId = studentExamMarksDetailsEntry.getKey();
            ExamNode examNode = examNodeMap.get(examId);
            for (StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsEntry.getValue()) {
                UUID studentId = studentExamMarksDetails.getStudent().getStudentId();
                studentMap.put(studentId, studentExamMarksDetails.getStudent());
                if (!studentExamMarksDetailsTwoDMapTemp.containsKey(studentId)) {
                    studentExamMarksDetailsTwoDMapTemp.put(studentId, new HashMap<>());
                }
                for (ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
                    UUID courseId = examCourseMarks.getCourse().getCourseId();
                    List<UUID> courseIdList = examNode == null ? null : getExamNodeCourseIds(examNode.getCourses());
                    if(!CollectionUtils.isEmpty(courseIdList) && !courseIdList.contains(courseId)) {
                        continue;
                    }
                    if(!studentExamMarksDetailsTwoDMapTemp.get(studentId).containsKey(courseId)) {
                        studentExamMarksDetailsTwoDMapTemp.get(studentId).put(courseId, new HashMap<>());
                    }
                    if(!studentExamMarksDetailsTwoDMapTemp.get(studentId).get(courseId).containsKey(examId)) {
                        studentExamMarksDetailsTwoDMapTemp.get(studentId).get(courseId).put(examId, examCourseMarks);
                    }
                }
            }
        }

        List<Student> students = new ArrayList<>(studentMap.values());
        Collections.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student s1, Student s2) {
                return s1.getStudentBasicInfo().getName().compareToIgnoreCase(s2.getStudentBasicInfo().getName());
            }
        });

        Map<UUID, Map<UUID, Map<UUID, ExamCourseMarks>>> studentExamMarksDetailsTwoDMap = new LinkedHashMap<>();
        for (Student student : students) {
            UUID studentId = student.getStudentId();
            if (studentExamMarksDetailsTwoDMapTemp.containsKey(studentId)) {
                studentExamMarksDetailsTwoDMap.put(studentId, studentExamMarksDetailsTwoDMapTemp.get(studentId));
            }
        }

        return studentExamMarksDetailsTwoDMap;
    }

    private Map<UUID, Map<UUID, StudentExamMarksDetails>> getStudentExamMarksDetailsMap(Map<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsMap) {
        /**
         *  Map of student, exam and details of students exam marks for one subject
         */
        Map<UUID, Map<UUID, StudentExamMarksDetails>> studentExamMarksDetailsTwoDMapTemp = new HashMap<UUID, Map<UUID, StudentExamMarksDetails>>();
        Map<UUID, Student> studentMap = new HashMap<>();
        for (Entry<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsEntry : studentExamMarksDetailsMap.entrySet()) {
            UUID examId = studentExamMarksDetailsEntry.getKey();
            for (StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsEntry.getValue()) {
                UUID studentId = studentExamMarksDetails.getStudent().getStudentId();
                studentMap.put(studentId, studentExamMarksDetails.getStudent());
                if (studentExamMarksDetailsTwoDMapTemp.containsKey(studentId)) {
                    Map<UUID, StudentExamMarksDetails> studentExamMarksMap = studentExamMarksDetailsTwoDMapTemp.get(studentId);
                    if (!studentExamMarksMap.containsKey(examId)) {
                        studentExamMarksMap.put(examId, studentExamMarksDetails);
                    }
                } else {
                    Map<UUID, StudentExamMarksDetails> studentExamMarksMap = new HashMap<UUID, StudentExamMarksDetails>();
                    studentExamMarksMap.put(examId, studentExamMarksDetails);
                    studentExamMarksDetailsTwoDMapTemp.put(studentId, studentExamMarksMap);
                }
            }

        }

        List<Student> students = new ArrayList<>(studentMap.values());
        Collections.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student s1, Student s2) {
                return s1.getStudentBasicInfo().getName().compareToIgnoreCase(s2.getStudentBasicInfo().getName());
            }
        });

        Map<UUID, Map<UUID, StudentExamMarksDetails>> studentExamMarksDetailsTwoDMap = new LinkedHashMap<>();
        for (Student student : students) {
            UUID studentId = student.getStudentId();
            if (studentExamMarksDetailsTwoDMapTemp.containsKey(studentId)) {
                studentExamMarksDetailsTwoDMap.put(studentId, studentExamMarksDetailsTwoDMapTemp.get(studentId));
            }
        }

        return studentExamMarksDetailsTwoDMap;
    }

    //    courseExamMap - it will contain map of course and list of exam for which max marks are filled.
    private ImmutablePair<Integer, Integer> createClassCourseWiseExamReportHeader(ReportWorkbook workbook, ReportSheet sheet, int headerRowNumber, ExamNode examNode,
                                                                                  List<Integer> columnWidths, boolean excludeCoScholasticSubjects, boolean showDimensions, boolean showTotalColumnDimension,
                                                                                  LinkedHashMap<UUID, List<UUID>> courseExamSequenceList, Map<UUID, Set<UUID>> courseExamListMap) {
        final ReportFont headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setColor(BLACK_COLOR);

        // Create a CellStyle with the font
        final ReportCellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        final ReportRow headerRow = sheet.createRow(headerRowNumber++);

        int cellIndex = 0;
        createCell(headerRow, cellIndex++, headerCellStyle, "S.No.", columnWidths);
        createCell(headerRow, cellIndex++, headerCellStyle, "Name", columnWidths);
        createCell(headerRow, cellIndex++, headerCellStyle, "Roll No", columnWidths);
        createCell(headerRow, cellIndex++, headerCellStyle, "Admission No", columnWidths);
        for(Course course : examNode.getCourses()) {
            if(excludeCoScholasticSubjects && course.getCourseType() == CourseType.COSCHOLASTIC) {
                continue;
            }
            UUID courseId = course.getCourseId();
            UUID examId = examNode.getExamMetaData().getExamId();

            /**
             * here we are filtering exams for each course for which max marks are not filled
             */
            if(courseExamListMap != null
                    && !CollectionUtils.isEmpty(courseExamListMap.entrySet())
                    && !courseExamListMap.containsKey(courseId)) {
                continue;
            }

            if(courseExamListMap != null
                    && !CollectionUtils.isEmpty(courseExamListMap.entrySet())
                    && !courseExamListMap.get(courseId).contains(examId)) {
                continue;
            }

            int startRow = headerRowNumber;
            int startCol = cellIndex;
            createCell(headerRow, cellIndex++, headerCellStyle, course.getCourseName(), columnWidths);
            cellIndex--;

            ReportRow row = sheet.getRow(headerRowNumber);
            if (sheet.getLastRowNum() < headerRowNumber) {
                row = sheet.createRow(headerRowNumber);
            }
            createCell(row, cellIndex++, headerCellStyle, examNode.getExamMetaData().getExamName(), columnWidths);
            cellIndex--;
            headerRowNumber++;

            List<UUID> examSequenceList = new ArrayList<>();
            cellIndex = generateExamGraph(sheet, cellIndex, headerRowNumber, examNode, headerCellStyle, columnWidths, examSequenceList, courseId, courseExamListMap);

            headerRowNumber--;

            createCell(sheet.getRow(row.getRowNum() + 1), cellIndex++, headerCellStyle, "Total", columnWidths);
            createCell(sheet.getRow(row.getRowNum() + 1), cellIndex++, headerCellStyle, "Grade", columnWidths);

            if(startCol < cellIndex - 1) {
                //Merging for top exam
                sheet.addMergedRegion(new CellIndexes(startRow + 1, startRow + 1, startCol, cellIndex - 1));
                //Merging for course
                sheet.addMergedRegion(new CellIndexes(startRow, startRow, startCol, cellIndex - 1));
            }

            examSequenceList.add(examId);
            courseExamSequenceList.put(courseId, examSequenceList);

        }

        return new ImmutablePair<Integer, Integer>(headerRowNumber, cellIndex);
    }

    private int generateExamGraph(ReportSheet sheet, int cellIndex, int headerRowNumber, ExamNode examNode, ReportCellStyle headerCellStyle, List<Integer> columnWidths,
                                  List<UUID> examSequenceList, UUID courseId, Map<UUID, Set<UUID>> courseExamListMap) {
        ReportRow row = sheet.getRow(headerRowNumber);
        if (sheet.getLastRowNum() < headerRowNumber) {
            row = sheet.createRow(headerRowNumber++);
        } else {
            headerRowNumber++;
        }

        if(examNode == null || CollectionUtils.isEmpty(examNode.getChildren())) {
            return cellIndex;
        }

        List<ExamNode> sortedChildrenExamNodes = new ArrayList<>(examNode.getChildren());
        Collections.sort(sortedChildrenExamNodes);
        for (ExamNode childExamNode : sortedChildrenExamNodes) {
            if (childExamNode == null) {
                continue;
            }
            List<UUID> courseIdList = getExamNodeCourseIds(childExamNode.getCourses());
            if(!courseIdList.contains(courseId)) {
                continue;
            }

            /**
             * here we are filtering exams for each course for which max marks are not filled
             */
            if(courseExamListMap != null
                    && !CollectionUtils.isEmpty(courseExamListMap.entrySet())
                    && !courseExamListMap.containsKey(courseId)) {
                continue;
            }

            if(courseExamListMap != null
                    && !CollectionUtils.isEmpty(courseExamListMap.entrySet())
                    && !courseExamListMap.get(courseId).contains(childExamNode.getExamMetaData().getExamId())) {
                continue;
            }

            int startRow = headerRowNumber;
            int startCol = cellIndex;
            createCell(row, cellIndex++, headerCellStyle, childExamNode.getExamMetaData().getExamName(), columnWidths);
            if (!CollectionUtils.isEmpty(childExamNode.getChildren())) {
                cellIndex--;
                cellIndex = generateExamGraph(sheet, cellIndex, headerRowNumber, childExamNode, headerCellStyle, columnWidths, examSequenceList, courseId, courseExamListMap);

//                if(childExamNode.getChildren().size() > 1) {
                createCell(sheet.getRow(row.getRowNum() + 1), cellIndex++, headerCellStyle, "Total", columnWidths);
                createCell(sheet.getRow(row.getRowNum() + 1), cellIndex++, headerCellStyle, "Grade", columnWidths);
//                } else {
//                    createCell(sheet.getRow(row.getRowNum() + 1), cellIndex++, headerCellStyle, "Total", columnWidths);
//                }

                if(startCol < cellIndex - 1) {
                    //Merging for top exam
                    sheet.addMergedRegion(new CellIndexes(startRow, startRow, startCol, cellIndex - 1));
                }
            }
            examSequenceList.add(childExamNode.getExamMetaData().getExamId());
        }
        return cellIndex;
    }

    private List<UUID> getExamNodeCourseIds(List<Course> courses) {
        if(CollectionUtils.isEmpty(courses)) {
            return new ArrayList<>();
        }
        List<UUID> courseIdList = new ArrayList<>();
        for(Course course : courses) {
            courseIdList.add(course.getCourseId());
        }
        return courseIdList;
    }

    private boolean validStudentRow(Student student, Set<Integer> sectionIdSet,
                                    Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap) {
        if (!CollectionUtils.isEmpty(sectionIdSet)) {
            final List<StandardSections> standardSections = student.getStudentAcademicSessionInfoResponse()
                    .getStandard().getStandardSectionList();
            if (CollectionUtils.isEmpty(standardSections)) {
                return false;
            }
            if (!sectionIdSet.contains(standardSections.get(0).getSectionId())) {
                return false;
            }
        }

        return studentExamMarksDetailsMap.containsKey(student.getStudentId());
    }
    private boolean validStudentRow(Student student, Integer classSectionId,
                                    Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap) {
        if (classSectionId != null && classSectionId > 0) {
            final List<StandardSections> standardSections = student.getStudentAcademicSessionInfoResponse()
                    .getStandard().getStandardSectionList();
            if (CollectionUtils.isEmpty(standardSections)) {
                return false;
            }
            if (standardSections.get(0).getSectionId() != classSectionId.intValue()) {
                return false;
            }
        }

        return studentExamMarksDetailsMap.containsKey(student.getStudentId());
    }

    private Map<UUID, Student> getStudentMap(List<Student> students) {
        final Map<UUID, Student> studentMap = new HashMap<>();
        for (final Student student : students) {
            studentMap.put(student.getStudentId(), student);
        }
        return studentMap;
    }

    private Map<UUID, LinkedHashMap<UUID, Course>> getStudentCourseMap(int instituteId, int academicSessionId, UUID standardId) {
        final Map<UUID, List<Course>> studentCourses = courseManager.getClassStudentsAssignedCourses(instituteId,
                academicSessionId, standardId);

        final Map<UUID, LinkedHashMap<UUID, Course>> studentCourseMap = new HashMap<>();

        for (final Entry<UUID, List<Course>> studentEntry : studentCourses.entrySet()) {

            studentCourseMap.put(studentEntry.getKey(), new LinkedHashMap<>());

            for (final Course course : studentEntry.getValue()) {
                studentCourseMap.get(studentEntry.getKey()).put(course.getCourseId(), course);
            }
        }
        return studentCourseMap;
    }

    private MarksRowData createCourseMarksColumns(int instituteId, List<ExamCourseMarks> examCourseMarksList, ReportRow row,
                                                  int colNum, List<Integer> columnWidths, boolean showDimensions, boolean showTotalColumnDimension,
                                                  final Map<UUID, Course> studentCourseMap,
                                                  boolean showSubjectGrade, List<ExamGrade> examGradeList,
                                                  ReportCellStyle cellStyle) {
        /**
         * Make sure sort header course also
         */
        Collections.sort(examCourseMarksList, new Comparator<ExamCourseMarks>() {
            @Override
            public int compare(ExamCourseMarks s1, ExamCourseMarks s2) {
                return s1.getCourse().compareTo(s2.getCourse());
            }
        });

        double totalMaxMarks = 0d;
        double totalObtainedMarks = 0d;

        for (final ExamCourseMarks examCourseMarks : examCourseMarksList) {

            if (!studentCourseMap.containsKey(examCourseMarks.getCourse().getCourseId())) {
                createCell(row, colNum++, cellStyle, "", columnWidths);
                continue;
            }
            for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                    .getExamDimensionObtainedValues()) {
                final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                final Double obtainedMarks = examDimensionObtainedValues.getObtainedMarks();
                final Double minMarks =  examDimensionObtainedValues.getMinMarks();
                /**
                 * adding check for institute 10045 as they don't want to see subject wise pass or fail in there reports
                 * so if instituteId == 10045, don't show minMarks suffix
                 * ow show
                 */
                String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues
                        .getObtainedMarks() == null ? null : getRoundValueString(examDimensionObtainedValues
                        .getObtainedMarks()), examDimensionObtainedValues.getAttendanceStatus(), minMarks, instituteId != 10045, instituteId == 10295 ? "" : " (Fail)", maxMarks, NO_MARKS_TEXT, false);
                if(showSubjectGrade) {
                    ExamGrade examGrade = examDimensionObtainedValues.getObtainedGrade();
                    ExamEvaluationType examEvaluationType = examDimensionObtainedValues.getExamDimension().getExamEvaluationType();
                    if(examEvaluationType == ExamEvaluationType.NUMBER) {
                        Double percentage = null;
                        if (maxMarks != null && obtainedMarks != null && maxMarks > 0d) {
                            percentage = obtainedMarks / maxMarks;
                        }
                        examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradeList, percentage);
                    }
                    finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examGrade == null ? null : examGrade.getGradeName(),
                            examDimensionObtainedValues.getAttendanceStatus(), null, false, "", null, NO_MARKS_TEXT, false);
                }
                if (showDimensions) {
                    if (!examDimensionObtainedValues.getExamDimension().isTotal()) {
                        /**
                         * skiping dimension for which max marks is not set
                         */
                        if(examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
                                examDimensionObtainedValues.getMaxMarks() == null) {
                            continue;
                        }
                        createCell(row, colNum++, cellStyle, finalObtainedValue == null ? "" : finalObtainedValue,
                                columnWidths);
                    } else if (showTotalColumnDimension) {
                        createCell(row, colNum++, cellStyle, finalObtainedValue == null ? "" : finalObtainedValue,
                                columnWidths);
                    } else if(showSubjectGrade && examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                        createCell(row, colNum++, cellStyle, finalObtainedValue == null ? "" : finalObtainedValue,
                                columnWidths);
                    }
                } else {
                    if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                        createCell(row, colNum++, cellStyle, finalObtainedValue == null ? "" : finalObtainedValue,
                                columnWidths);
                    }
                }

                /**
                 * Add total Marks
                 */
                if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                    if (instituteId != 10001
                            || (!examCourseMarks.getCourse().getCourseName().equalsIgnoreCase("Foundation of IT*"))) {
                        totalMaxMarks += (maxMarks == null || maxMarks < 0d ? 0d : maxMarks);
                        totalObtainedMarks += (obtainedMarks == null || obtainedMarks < 0d ? 0d : obtainedMarks);
                    }
                }
            }

        }
        return new MarksRowData(colNum, totalMaxMarks, totalObtainedMarks);
    }

    private List<Integer> createClassExamReportHeader(ReportWorkbook workbook, ReportSheet sheet, int headerRowNumber,
                                                      ExamDetails examDetails, Set<String> requiredHeaderAttributes, boolean excludeCoScholasticSubjects, boolean showDimensions,
                                                      boolean showTotalColumnDimension,boolean showParentRemarks,boolean displayRank, boolean displayAttendance, boolean showSubjectGrade) {

        final ReportFont headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints(12);
        headerFont.setColor(BLACK_COLOR);

        // Create a CellStyle with the font
        final ReportCellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        final int startHeaderRowIndex = headerRowNumber;
        final ReportRow headerRow = sheet.createRow(headerRowNumber++);
        ReportRow dimensionRow = null;
        if (showDimensions) {
            dimensionRow = sheet.createRow(headerRowNumber++);
        }
        final ReportRow maxMarksRow = sheet.createRow(headerRowNumber++);

        final List<Integer> columnWidths = new ArrayList<>();
        int cellIndex = 0;
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.EXAM_SR_NO.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.EXAM_SR_NO.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_No.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.ADMISSION_No.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ROLL_NO.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.ROLL_NO.getDisplayName(), "", "");
        }
        createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                "Name", "", "");
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FATHER_NAME.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.FATHER_NAME.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.DOB.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.DOB.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.CLASS.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.CLASS.getDisplayName(), "", "");
        }

//        for (int k = 0; k < 7; k++) {
//            final CellIndexes cellRangeAddress = new CellIndexes(startHeaderRowIndex,
//                    startHeaderRowIndex + (showDimensions ? 2 : 1), k, k);
//            sheet.addMergedRegion(cellRangeAddress);
//        }

        final Map<CourseType, List<ExamCourse>> examCourseMap = examDetails.getCourseMarksMatrix();
        final Map<CourseType, List<ExamCourse>> orderedExamCourseMap = new LinkedHashMap<>();
        orderedExamCourseMap.put(CourseType.SCHOLASTIC, examCourseMap.get(CourseType.SCHOLASTIC));
        orderedExamCourseMap.put(CourseType.COSCHOLASTIC, examCourseMap.get(CourseType.COSCHOLASTIC));

        for (final Entry<CourseType, List<ExamCourse>> courseTypeEntry : orderedExamCourseMap.entrySet()) {
            if (excludeCoScholasticSubjects && courseTypeEntry.getKey() == CourseType.COSCHOLASTIC) {
                continue;
            }
            /**
             * Make sure sort student level marks also
             */
            // adding if condition as it was giving null pointer exception
            if (courseTypeEntry.getValue() != null) {
                Collections.sort(courseTypeEntry.getValue(), new Comparator<ExamCourse>() {
                    @Override
                    public int compare(ExamCourse s1, ExamCourse s2) {
                        return s1.getCourse().compareTo(s2.getCourse());
                    }
                });
                for (final ExamCourse examCourse : courseTypeEntry.getValue()) {
                    final int startCellIndex = cellIndex;
                    for (final ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
                        if (showDimensions) {
                            if (!examDimensionValues.getExamDimension().isTotal()) {
                                /**
                                 * skiping dimension for which max marks is not set
                                 */
                                if(examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
                                        examDimensionValues.getMaxMarks() == null) {
                                    continue;
                                }
                                createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++,
                                        headerCellStyle, columnWidths, examCourse.getCourse().getCourseName(),
                                        examDimensionValues.getExamDimension().getDimensionName(),
                                        getRoundValueString(examDimensionValues.getMaxMarks()));
                            } else if (showTotalColumnDimension) {
                                createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++,
                                        headerCellStyle, columnWidths, examCourse.getCourse().getCourseName(),
                                        examDimensionValues.getExamDimension().getDimensionName(),
                                        getRoundValueString(examDimensionValues.getMaxMarks()));
                            } else if(showSubjectGrade && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                                createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++,
                                        headerCellStyle, columnWidths, examCourse.getCourse().getCourseName(),
                                        examDimensionValues.getExamDimension().getDimensionName(),
                                        getRoundValueString(examDimensionValues.getMaxMarks()));
                            }
                        } else {
                            if (examDimensionValues.getExamDimension().isTotal()) {
                                createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++,
                                        headerCellStyle, columnWidths, examCourse.getCourse().getCourseName(),
                                        examDimensionValues.getExamDimension().getDimensionName(),
                                        getRoundValueString(examDimensionValues.getMaxMarks()));
                            }
                        }
                    }
                    if (cellIndex - 1 - startCellIndex > 1) {
                        final CellIndexes cellRangeAddress = new CellIndexes(startHeaderRowIndex,
                                startHeaderRowIndex, startCellIndex, cellIndex - 1);
                        sheet.addMergedRegion(cellRangeAddress);
                    }
                }
            }

        }

        final int grandTotalStartCellIndex = cellIndex;
        createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                "Grand Total", "", "MM");
        createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                "Grand Total", "", "MO");

//        CellIndexes cellRangeAddress = new CellIndexes(startHeaderRowIndex,
//                showDimensions ? startHeaderRowIndex + 1 : startHeaderRowIndex, grandTotalStartCellIndex,
//                cellIndex - 1);
//        sheet.addMergedRegion(cellRangeAddress);

        final int percentStartCellIndex = cellIndex;
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PERCENTAGE.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.PERCENTAGE.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.GRADE.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.GRADE.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.DIVISION.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.DIVISION.getDisplayName(), "", "");
        }
        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.RESULT.getKey())) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    ReportHeaderAttribute.RESULT.getDisplayName(), "", "");
        }
        if (displayRank) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    "Rank", "", "");
        }
        if (displayAttendance) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    "Attendance", "", "");
        }
        if(showParentRemarks) {
            createHeaderGroupCells(headerRow, dimensionRow, maxMarksRow, cellIndex++, headerCellStyle, columnWidths,
                    "Parent Remarks", "", "");
        }

//        for (int k = percentStartCellIndex; k < percentStartCellIndex + (displayRank ? 4 : 3) + (displayAttendance ? 2 : 1); k++) {
//            cellRangeAddress = new CellIndexes(startHeaderRowIndex, startHeaderRowIndex + (showDimensions ? 2 : 1),
//                    k, k);
//            sheet.addMergedRegion(cellRangeAddress);
//        }
        return columnWidths;
    }

    private void createHeaderGroupCells(ReportRow headerRow, ReportRow dimensionRow, ReportRow maxMarksRow, int cellIndex,
                                        ReportCellStyle cellStyle, List<Integer> columnWidths, Object headerValue, Object dimensionValue,
                                        Object maxMarksValue) {
        createCell(headerRow, cellIndex, cellStyle, headerValue, columnWidths);
        if (dimensionRow != null) {
            createCell(dimensionRow, cellIndex, cellStyle, dimensionValue, columnWidths);
        }
        createCell(maxMarksRow, cellIndex, cellStyle, maxMarksValue, columnWidths);
    }

    private void createCell(ReportRow row, int cellIndex, ReportCellStyle cellStyle, Object value, List<Integer> columnWidths) {

        final String valueStr = value == null ? "" : String.valueOf(value);
        final ReportCell cell = row.createCell(cellIndex);
        cell.setCellValue(valueStr);
        cell.setCellStyle(cellStyle);
        if (cellIndex < columnWidths.size()) {
            updateColumnWidth(columnWidths, cellIndex, valueStr);
        } else {
            columnWidths.add(valueStr.length());
        }
    }

    public ReportDetails generateReport(int instituteId, ExamReportType examReportType,
                                        UUID userId, ExamReportFiltrationCriteria examReportFiltrationCriteria) {

        if(examReportFiltrationCriteria == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid Request!."));
        }

        int academicSessionId = examReportFiltrationCriteria.getAcademicSessionId();
        UUID standardId = examReportFiltrationCriteria.getStandardId();
        Integer sectionId = examReportFiltrationCriteria.getSectionId();
        UUID examId = examReportFiltrationCriteria.getExamId();
        UUID courseId = examReportFiltrationCriteria.getCourseId();
        CourseType courseType = examReportFiltrationCriteria.getCourseType();
        DownloadFormat downloadFormat = examReportFiltrationCriteria.getDownloadFormat();
        String reportCardType = examReportFiltrationCriteria.getReportCardType();
        UUID staffId = examReportFiltrationCriteria.getStaffId();
        Set<Integer> sectionIdSet = examReportFiltrationCriteria.getSectionIdSet();
        Set<UUID> examIdSet = examReportFiltrationCriteria.getExamIdSet();
        Set<UUID> courseIdSet = examReportFiltrationCriteria.getCourseIdSet();
        Set<UUID> compareCummulativeWithExamIdSet = examReportFiltrationCriteria.getCompareCummulativeWithExamIdSet();
        Integer rankTill = examReportFiltrationCriteria.getRankTill();
        boolean excludeCoScholasticSubjects = examReportFiltrationCriteria.isExcludeCoScholasticSubjects();
        String requiredHeaders = examReportFiltrationCriteria.getRequiredHeaders();
        boolean isSortStudentOnRank = examReportFiltrationCriteria.isSortStudentOnRank();
        Set<UUID> additionalCoursesSet = examReportFiltrationCriteria.getAdditionalCoursesSet();
        Set<MarksDisplayType> scholasticMarksDisplayTypeSet = examReportFiltrationCriteria.getScholasticMarksDisplayTypeSet();
        Set<MarksDisplayType> coScholasticMarksDisplayTypeSet = examReportFiltrationCriteria.getCoScholasticMarksDisplayTypeSet();
        boolean showClassAverageDetails = examReportFiltrationCriteria.isShowClassAverageDetails();
        boolean showStaffDetails = examReportFiltrationCriteria.isShowStaffDetails();

        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Institute id is invalid."));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER,
                    "User id cannot be null."));
        }

        if (downloadFormat == DownloadFormat.EXCEL) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EXAM_EXCEL_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download exam excel reports!"));
            }
        } else if (downloadFormat == DownloadFormat.PDF) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EXAM_PDF_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download exam pdf reports!"));
            }
        }

        if (examReportType == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
                    "Exam Report Type is invalid."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Academic Session Id is invalid."));
        }

        switch (examReportType) {
            case SUBJECT_WISE_CLASS_RESULT_ANALYSIS:
                if (standardId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD,
                            "Standard id cannot be null."));
                }

                if (examId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
                            "Exam id is invalid."));
                }
                if (courseId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COURSE,
                            "Course id is invalid."));
                }
                return generateSubjectWiseClassResultAnalysis(instituteId, academicSessionId, standardId, sectionId, examId, courseId);
            case SUBJECT_WISE_EXAM_GRADE_ANALYSIS:
                if (standardId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD,
                            "Standard id cannot be null."));
                }

                if (examId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
                            "Exam id is invalid."));
                }
                if (courseType == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COURSE_DETAILS,
                            "Course Type is invalid."));
                }
                return generateSubjectwiseExamGradeWiseAnalysis(instituteId, academicSessionId, standardId, sectionIdSet, examId, courseType);
            case EXAM_WISE_GRADE_ANALYSIS:
                if (standardId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD,
                            "Standard id cannot be null."));
                }

                if (examId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
                            "Exam id is invalid."));
                }
                if (courseType == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COURSE_DETAILS,
                            "Course Type is invalid."));
                }
                return generateExamWiseGradeAnalysis(instituteId, academicSessionId, standardId, sectionId, examId, courseType);
            case TEACHER_WISE_DIVISION_RESULT_ANALYSIS_REPORT:
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_TEACHER_WISE_RESULT_ANALYSIS_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download teacher wise result analysis report!"));
                }
                if (staffId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF,
                            "Staff is invalid."));
                }
                if (StringUtils.isBlank(reportCardType)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Exam Report card type is invalid."));
                }
                return getStaffLevelResultAnalysis(instituteId, academicSessionId, staffId, reportCardType, true);
            case TEACHER_WISE_GRADE_RESULT_ANALYSIS_REPORT:
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_TEACHER_WISE_RESULT_ANALYSIS_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download teacher wise result analysis report!"));
                }
                if (staffId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF,
                            "Staff is invalid."));
                }
                if (StringUtils.isBlank(reportCardType)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Exam Report card type is invalid."));
                }
                return getStaffLevelResultAnalysis(instituteId, academicSessionId, staffId, reportCardType, false);

            case DIVISION_WISE_CLASS_RESULT:
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_DIVISION_WISE_CLASS_RESULT_ANALYSIS_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download division wise class result report!"));
                }
                if (StringUtils.isBlank(reportCardType)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Exam Report card type is invalid."));
                }
                return getClassLevelDivisionWiseReport(instituteId, academicSessionId, reportCardType, true);
            case GRADE_WISE_CLASS_RESULT:
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_GRADE_WISE_CLASS_RESULT_ANALYSIS_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download grade wise class result report!"));
                }
                if (StringUtils.isBlank(reportCardType)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Exam Report card type is invalid."));
                }
                return getClassLevelDivisionWiseReport(instituteId, academicSessionId, reportCardType, false);
            case SUBJECT_WISE_RANK_REPORT:
                if (CollectionUtils.isEmpty(examIdSet)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Please select at-least one exam to generate report"));
                }
                /**
                 * setting rank till as 1 of coming null from UI
                 */
                rankTill = rankTill == null ? 1 : rankTill;
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_SUBJECT_WISE_RANK_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download subjectwise rank report!"));
                }
                return generateSubjectWiseRankReport(instituteId, academicSessionId, standardId, sectionIdSet,
                        examIdSet, courseIdSet, compareCummulativeWithExamIdSet, rankTill);
            case OVERALL_RANK_REPORT:
                if (CollectionUtils.isEmpty(examIdSet)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Please select at-least one exam to generate report"));
                }
                /**
                 * setting rank till as 1 of coming null from UI
                 */
                rankTill = rankTill == null ? 1 : rankTill;
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_OVERALL_RANK_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download overall rank report!"));
                }
                return generateOverallRankReport(instituteId, academicSessionId, standardId, sectionIdSet,
                        examIdSet, compareCummulativeWithExamIdSet, additionalCoursesSet, rankTill);
            case RESULT_SUMMARY_REPORT:
                if (CollectionUtils.isEmpty(examIdSet)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Please select at-least one exam to generate report"));
                }
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_RESULT_SUMMARY_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download result summary report!"));
                }
                return generateResultSummaryRankReport(instituteId, academicSessionId, standardId, sectionIdSet,
                        examIdSet, courseIdSet);
            case MULTIPLE_EXAM_MARKS_REPORT:
                if (standardId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Please select standard to generate report"));
                }
                if (CollectionUtils.isEmpty(examIdSet)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Please select at-least one exam to generate report"));
                }
                if (StringUtils.isBlank(requiredHeaders)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            "Please select at-least one header to generate report"));
                }
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_MULTIPLE_EXAM_MARKS_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download multiple exam marks report!"));
                }
                scholasticMarksDisplayTypeSet = CollectionUtils.isEmpty(scholasticMarksDisplayTypeSet) ? new HashSet<>(Collections.singletonList(MarksDisplayType.MARKS)) : scholasticMarksDisplayTypeSet;
                coScholasticMarksDisplayTypeSet = CollectionUtils.isEmpty(coScholasticMarksDisplayTypeSet) ? new HashSet<>(Collections.singletonList(MarksDisplayType.MARKS)) : coScholasticMarksDisplayTypeSet;
                return generateMultipleExamMarksReport(instituteId, academicSessionId, standardId, sectionIdSet,
                        examIdSet, excludeCoScholasticSubjects, requiredHeaders, isSortStudentOnRank, additionalCoursesSet, scholasticMarksDisplayTypeSet,
                        coScholasticMarksDisplayTypeSet, showClassAverageDetails, showStaffDetails);
            default:
                break;
        }
        return null;

    }

    private ReportDetails generateExamWiseGradeAnalysis(int instituteId, int academicSessionId, UUID standardId, Integer sectionId, UUID examId, CourseType courseType) {

        final String sheetName = "ExamWiseGradeAnalysis";
        final String reportName = "Exam Wise Grade Analysis";

        final List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationManager.getResolvedClassMarks(
                instituteId, examId, sectionId, false);

        if (CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }


        //Map of studentId and StudentExamMarksDetails
        final Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = getStudentExamMarksDetailMap(studentExamMarksDetailsList);
        Set<Integer> sectionIdSet = null;
        if (sectionId != null && sectionId > 0) {
            sectionIdSet = new HashSet<Integer>();
            sectionIdSet.add(sectionId);
        }
        final List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId, standardId,
                sectionIdSet);

        if (CollectionUtils.isEmpty(students)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        final Map<UUID, LinkedHashMap<UUID, Course>> studentCourseMap = getStudentCourseMap(instituteId,
                academicSessionId, standardId);

        if (CollectionUtils.isEmpty(studentCourseMap.entrySet())) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        final Map<CourseType, List<ExamGrade>> examGradesMap = examinationManager.getExamGrades(instituteId,
                academicSessionId, standardId);
        List<ExamGrade> examGradesList = examGradesMap.get(courseType);

        if (CollectionUtils.isEmpty(examGradesList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Collections.sort(examGradesList, new Comparator<ExamGrade>() {
            @Override
            public int compare(ExamGrade s1, ExamGrade s2) {
                return s2.getGradeValue().compareTo(s1.getGradeValue());
            }
        });

        LinkedHashMap<Integer, Integer> gradeTotalMap = new LinkedHashMap<>();
        for (ExamGrade examGrade : examGradesList) {
            gradeTotalMap.put(examGrade.getGradeId(), 0);
        }

        for (final Student student : students) {
            if (!validStudentRow(student, sectionId, studentExamMarksDetailsMap)) {
                continue;
            }
            double maxMarks = 0d;
            double marksObtained = 0d;
            Map<UUID, Course> studentCourse = studentCourseMap.get(student.getStudentId());
            StudentExamMarksDetails studentExamMarksDetails = studentExamMarksDetailsMap.get(student.getStudentId());
            /**
             * Make sure sort header course also
             */
            Collections.sort(studentExamMarksDetails.getExamCoursesAllDimensionsMarks(), new Comparator<ExamCourseMarks>() {
                @Override
                public int compare(ExamCourseMarks s1, ExamCourseMarks s2) {
                    return s1.getCourse().compareTo(s2.getCourse());
                }
            });

            for (ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
                if (!studentCourse.containsKey(examCourseMarks.getCourse().getCourseId())) {
                    continue;
                }
                final MarksRowData marksRowData = getMarksRowDataOfCourse(instituteId,
                        examCourseMarks, studentCourse);
                maxMarks += marksRowData.getTotalMaxMarks();
                marksObtained += marksRowData.getTotalObtainedMarks();
            }

            Double percentage = null;
            if (maxMarks > 0d) {
                percentage = marksObtained / maxMarks;
            }

            /**
             * Assuming if scholastic and coscholastic course are shown
             * together then grading scheme is same. So using scholastic
             * grading scheme only
             */
            final ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradesList, percentage);

            if (examGrade == null || examGrade.getGradeId() == null) {
                continue;
            }

            Integer gradeId = examGrade.getGradeId();
            gradeTotalMap.put(gradeId, gradeTotalMap.get(gradeId) + 1);
        }

        //Assuming students is not empty taking standard from first student object
        Student student = students.get(0);
        Standard standard = student.getStudentAcademicSessionInfoResponse().getStandard();

        Institute institute = instituteManager.getInstitute(instituteId);
        return getExamWiseGradeAnalysisReportDetails(reportName, sheetName,
                instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId),
                getExamGradeMap(examGradesList), examinationManager.getExamDetails(examId, instituteId).getExamMetaData(),
                standard, gradeTotalMap, institute);
    }

    private ReportDetails getExamWiseGradeAnalysisReportDetails(String reportName, String sheetName, AcademicSession academicSession,
                                                                LinkedHashMap<Integer, ExamGrade> examGradeMap, ExamMetaData examMetaData,
                                                                Standard standard, LinkedHashMap<Integer, Integer> gradeTotalMap, Institute institute) {
        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        if (examGradeMap == null || CollectionUtils.isEmpty(examGradeMap.entrySet())) {
            return null;
        }
        generateExamWiseGradeAnalysisHeadingDetails(academicSession, standard, examMetaData, headerReportCellDetails, headerMergeCellIndexesList);
        generateExamWiseGradeAnalysisTableHeader(examGradeMap, headerReportCellDetails, headerMergeCellIndexesList);

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
        generateExamWiseGradeAnalysisTableDetails(examGradeMap, gradeTotalMap, reportCellDetails, mergeCellIndexesList);

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, mergeCellIndexesList,
//                reportCellDetails);

        int totalColumns = 3;
        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);

        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private void generateExamWiseGradeAnalysisTableDetails(LinkedHashMap<Integer, ExamGrade> examGradeMap,
                                                           LinkedHashMap<Integer, Integer> gradeTotalMap, List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {

        int totalCount = 0;
        int index = 2;
        for (Entry<Integer, Integer> entry : gradeTotalMap.entrySet()) {
            List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

            reportCellDetailsRow.add(new ReportCellDetails(examGradeMap.get(entry.getKey()).getGradeName(), STRING, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR, true));

            String range = examGradeMap.get(entry.getKey()).getRangeDisplayName();
            if (StringUtils.isEmpty(range)) {
                Double startRange = (double) Math.round(examGradeMap.get(entry.getKey()).getMarksRangeStart() * 100);
                Double endRange = (double) (Math.round(examGradeMap.get(entry.getKey()).getMarksRangeEnd() * 100) > 100 ?
                        100.0 : Math.round(examGradeMap.get(entry.getKey()).getMarksRangeEnd() * 100));
                range = startRange + "-" + endRange;
            }
            reportCellDetailsRow.add(new ReportCellDetails(range, STRING, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR, true));

            totalCount += entry.getValue();
            reportCellDetailsRow.add(new ReportCellDetails(entry.getValue(), INTEGER, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR,
                    false));
            reportCellDetails.add(reportCellDetailsRow);
            index++;
        }

        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(totalCount, INTEGER, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsRow);

        CellIndexes cellIndexes = new CellIndexes(index, index, 0, 1);
        mergeCellIndexesList.add(cellIndexes);
    }

    private void generateExamWiseGradeAnalysisTableHeader(LinkedHashMap<Integer, ExamGrade> examGradeMap, List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {
        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsHeaderRow.add(new ReportCellDetails("Grade", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));

        reportCellDetailsHeaderRow.add(new ReportCellDetails("Range", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));

        reportCellDetailsHeaderRow.add(new ReportCellDetails("Count", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));

        reportCellDetails.add(reportCellDetailsHeaderRow);
    }

    private void generateExamWiseGradeAnalysisHeadingDetails(AcademicSession academicSession, Standard standard,
                                                             ExamMetaData examMetaData, List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {
        /**
         * In reportDetails object the first line of 2d list contain the heading this is mandatory
         */
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        String heading = "Academic Year : " + academicSession.getDisplayName() + " | Class : " + standard.getDisplayNameWithSection() + " | Exam : " + examMetaData.getExamName();
        reportHeaderRow.add(new ReportCellDetails(heading, STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        reportCellDetails.add(reportHeaderRow);

        //Adding 1 for total column
        CellIndexes cellIndexes = new CellIndexes(0, 0, 0, 2);
        mergeCellIndexesList.add(cellIndexes);
    }

    private ReportDetails generateSubjectwiseExamGradeWiseAnalysis(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIdSet, UUID examId, CourseType courseType) {

        final String sheetName = "SubjectwiseExamGradeWiseAnalysis";
        final String reportName = "Subject-wise Exam Grade Wise Analysis";

        final List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationManager.getResolvedClassMarks(
                instituteId, examId, null, false);

        if (CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }


        //Map of studentId and StudentExamMarksDetails
        final Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = getStudentExamMarksDetailMap(studentExamMarksDetailsList);
//        Set<Integer> sectionIdSet = null;
//        if (sectionId != null && sectionId > 0) {
//            sectionIdSet = new HashSet<Integer>();
//            sectionIdSet.add(sectionId);
//        }
        final List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId, standardId,
                sectionIdSet);

        if (CollectionUtils.isEmpty(students)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        final Map<UUID, LinkedHashMap<UUID, Course>> studentCourseMap = getStudentCourseMap(instituteId,
                academicSessionId, standardId);

        if (CollectionUtils.isEmpty(studentCourseMap.entrySet())) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        final Map<CourseType, List<ExamGrade>> examGradesMap = examinationManager.getExamGrades(instituteId,
                academicSessionId, standardId);
        List<ExamGrade> examGradesList = examGradesMap.get(courseType);

        if (CollectionUtils.isEmpty(examGradesList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Collections.sort(examGradesList, new Comparator<ExamGrade>() {
            @Override
            public int compare(ExamGrade s1, ExamGrade s2) {
                return s2.getGradeValue().compareTo(s1.getGradeValue());
            }
        });

        List<Course> courseList = getClassCourseByCourseType(
                courseManager.getClassCoursesByStandardId(instituteId, standardId, academicSessionId), courseType);

        if (CollectionUtils.isEmpty(courseList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Institute institute = instituteManager.getInstitute(instituteId);

        //Map of GradeId, CourseId, no of student in this grade
        LinkedHashMap<Integer, Map<UUID, Integer>> gradeSubjectWiseCountMap = new LinkedHashMap<Integer, Map<UUID, Integer>>();
        LinkedHashMap<Integer, Integer> gradeTotalMap = new LinkedHashMap<>();
        for (ExamGrade examGrade : examGradesList) {
            Map<UUID, Integer> subjectWiseCountMap = new HashMap<UUID, Integer>();
            for (Course course : courseList) {
                subjectWiseCountMap.put(course.getCourseId(), 0);
            }
            gradeSubjectWiseCountMap.put(examGrade.getGradeId(), subjectWiseCountMap);
            gradeTotalMap.put(examGrade.getGradeId(), 0);
        }

        for (final Student student : students) {
            if (!validStudentRow(student, sectionIdSet, studentExamMarksDetailsMap)) {
                continue;
            }

            Map<UUID, Course> studentCourse = studentCourseMap.get(student.getStudentId());
            StudentExamMarksDetails studentExamMarksDetails = studentExamMarksDetailsMap.get(student.getStudentId());
            /**
             * Make sure sort header course also
             */
            Collections.sort(studentExamMarksDetails.getExamCoursesAllDimensionsMarks(), new Comparator<ExamCourseMarks>() {
                @Override
                public int compare(ExamCourseMarks s1, ExamCourseMarks s2) {
                    return s1.getCourse().compareTo(s2.getCourse());
                }
            });

            for (ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
                if (!studentCourse.containsKey(examCourseMarks.getCourse().getCourseId())) {
                    continue;
                }
                double maxMarks = 0d;
                double marksObtained = 0d;
                final MarksRowData marksRowData = getMarksRowDataOfCourse(instituteId,
                        examCourseMarks, studentCourse);
                maxMarks += marksRowData.getTotalMaxMarks();
                marksObtained += marksRowData.getTotalObtainedMarks();

                Double percentage = null;
                if (maxMarks > 0d) {
                    percentage = marksObtained / maxMarks;
                }

                /**
                 * Assuming if scholastic and coscholastic course are shown
                 * together then grading scheme is same. So using scholastic
                 * grading scheme only
                 */
                final ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradesList, percentage);

                if (examGrade == null || examGrade.getGradeId() == null) {
                    continue;
                }

                Integer gradeId = examGrade.getGradeId();
                UUID courseId = examCourseMarks.getCourse().getCourseId();
                gradeSubjectWiseCountMap.get(gradeId).put(courseId,
                        gradeSubjectWiseCountMap.get(gradeId).get(courseId) == null ? 1 :
                                gradeSubjectWiseCountMap.get(gradeId).get(courseId) + 1);
            }
        }

        //Assuming students is not empty taking standard from first student object
        Standard standard = instituteManager.getStandardByStandardId(instituteId, academicSessionId, standardId);
        return getAnnualOverallGradeWiseAnalysisReportDetails(reportName, sheetName,
                instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId),
                gradeSubjectWiseCountMap, getExamGradeMap(examGradesList),
                examinationManager.getExamDetails(examId, instituteId).getExamMetaData(),
                standard, sectionIdSet, courseType, getCourseMap(courseList), gradeTotalMap, institute);
    }

    private LinkedHashMap<UUID, Course> getCourseMap(List<Course> courseList) {
        if (CollectionUtils.isEmpty(courseList)) {
            return null;
        }
        LinkedHashMap<UUID, Course> courseMap = new LinkedHashMap<UUID, Course>();
        for (Course course : courseList) {
            courseMap.put(course.getCourseId(), course);
        }
        return courseMap == null || CollectionUtils.isEmpty(courseMap.entrySet()) ? null : courseMap;
    }

    private ReportDetails getAnnualOverallGradeWiseAnalysisReportDetails(String reportName, String sheetName,
                                                                         AcademicSession academicSession, LinkedHashMap<Integer, Map<UUID, Integer>> gradeSubjectWiseCountMap,
                                                                         LinkedHashMap<Integer, ExamGrade> examGradeMap, ExamMetaData examMetaData,
                                                                         Standard standard, Set<Integer> sectionIdSet, CourseType courseType, LinkedHashMap<UUID, Course> courseMap, LinkedHashMap<Integer, Integer> gradeTotalMap,
                                                                         Institute institute) {

        if (examGradeMap == null || CollectionUtils.isEmpty(examGradeMap.entrySet())) {
            return null;
        }

        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        generateAnnualCourseTypeReportHeadingDetails(academicSession, standard, sectionIdSet, courseType, examGradeMap.size(), examMetaData, headerReportCellDetails, headerMergeCellIndexesList);
        generateAnnualCourseTypeReportTableHeader(examGradeMap, headerReportCellDetails, headerMergeCellIndexesList);

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        generateAnnualCourseTypeReportTableDetails(gradeSubjectWiseCountMap, examGradeMap, courseMap, gradeTotalMap, reportCellDetails, mergeCellIndexesList);

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, mergeCellIndexesList,
//                reportCellDetails);

        int totalColumns = CollectionUtils.isEmpty(examGradeMap.entrySet()) ? 0
                : examGradeMap.size() + 2;
        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);

        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private void generateAnnualCourseTypeReportTableDetails(LinkedHashMap<Integer, Map<UUID, Integer>> gradeSubjectWiseCountMap,
                                                            LinkedHashMap<Integer, ExamGrade> examGradeMap, LinkedHashMap<UUID, Course> courseMap, LinkedHashMap<Integer, Integer> gradeTotalMap,
                                                            List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {

        int overAllTotal = 0;
        for (Entry<UUID, Course> entry2 : courseMap.entrySet()) {
            List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
            reportCellDetailsRow.add(new ReportCellDetails(entry2.getValue().getCourseName(), STRING, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR,
                    true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
            int totalCount = 0;
            for (Entry<Integer, ExamGrade> entry1 : examGradeMap.entrySet()) {
                int count = gradeSubjectWiseCountMap.get(entry1.getKey()).get(entry2.getKey());
                totalCount += count;
                gradeTotalMap.put(entry1.getKey(), gradeTotalMap.get(entry1.getKey()) + count);
                reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE,
                        BLACK_COLOR, WHITE_COLOR,
                        false));
            }
            overAllTotal += totalCount;
            reportCellDetailsRow.add(new ReportCellDetails(totalCount, INTEGER, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR,
                    false));
            reportCellDetails.add(reportCellDetailsRow);
        }


        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails("TOTAL", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        for (Entry<Integer, Integer> entry : gradeTotalMap.entrySet()) {
            reportCellDetailsRow.add(new ReportCellDetails(entry.getValue(), INTEGER, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR,
                    true));
        }
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsRow);

    }

    private void generateAnnualCourseTypeReportTableHeader(LinkedHashMap<Integer, ExamGrade> examGradeMap, List<List<ReportCellDetails>> reportCellDetails,
                                                           List<CellIndexes> mergeCellIndexesList) {
        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsHeaderRow.add(new ReportCellDetails("SUBJECTS", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));

        for (Entry<Integer, ExamGrade> entry : examGradeMap.entrySet()) {
            String gradeNameWithRange = entry.getValue().getGradeName() + (StringUtils.isBlank(entry.getValue().getRangeDisplayName())
                    ? EMPTY_TEXT : " (" + entry.getValue().getRangeDisplayName() + ")");
            reportCellDetailsHeaderRow.add(new ReportCellDetails(gradeNameWithRange, STRING, CONTENT_SIZE,
                    BLACK_COLOR, WHITE_COLOR,
                    true));
        }

        reportCellDetailsHeaderRow.add(new ReportCellDetails("TOTAL", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));

        reportCellDetails.add(reportCellDetailsHeaderRow);
    }

    private void generateAnnualCourseTypeReportHeadingDetails(AcademicSession academicSession, Standard standard, Set<Integer> sectionIdSet, CourseType courseType, int noOfGrades, ExamMetaData examMetaData,
                                                              List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {

        /**
         * In reportDetails object the first line of 2d list contain the heading this is mandatory
         */
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();

        String standardName = standard.getDisplayName();
        StringBuilder sectionNames = new StringBuilder();
        List<StandardSections> standardSectionsList = standard.getStandardSectionList();
        if(!CollectionUtils.isEmpty(standardSectionsList)) {
            String delimeter = "";
            for(StandardSections standardSections : standardSectionsList) {
                if(CollectionUtils.isEmpty(sectionIdSet) || (!CollectionUtils.isEmpty(sectionIdSet) && sectionIdSet.contains(standardSections.getSectionId()))) {
                    sectionNames.append(delimeter).append(standardSections.getSectionName());
                    delimeter = ", ";
                    continue;
                }
            }
        }
        String heading = "Academic Year : " + academicSession.getDisplayName() + " | Class : " + standardName
                + (!StringUtils.isBlank(sectionNames) ? " | Section(s) : " + sectionNames : EMPTY_TEXT)
                + " | Exam : " + examMetaData.getExamName();
        reportHeaderRow.add(new ReportCellDetails(heading, STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        reportCellDetails.add(reportHeaderRow);

        //Adding 1 for total column
        CellIndexes cellIndexes = new CellIndexes(0, 0, 0, noOfGrades + 1);
        mergeCellIndexesList.add(cellIndexes);
    }

    private List<Course> getClassCourseByCourseType(List<Course> courseList, CourseType courseType) {
        if (CollectionUtils.isEmpty(courseList)) {
            return null;
        }
        List<Course> finalCourseList = new ArrayList<Course>();
        for (Course course : courseList) {
            if (course.getCourseType().equals(courseType)) {
                finalCourseList.add(course);
            }
        }
        return CollectionUtils.isEmpty(finalCourseList) ? null : finalCourseList;
    }

    private Map<UUID, StudentExamMarksDetails> getStudentExamMarksDetailMap(
            List<StudentExamMarksDetails> studentExamMarksDetailsList) {
        final Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = new HashMap<UUID, StudentExamMarksDetails>();
        for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
            studentExamMarksDetailsMap.put(studentExamMarksDetails.getStudent().getStudentId(),
                    studentExamMarksDetails);
        }
        return studentExamMarksDetailsMap;
    }

    private ReportDetails generateSubjectWiseClassResultAnalysis(int instituteId, int academicSessionId, UUID standardId, Integer sectionId, UUID examId,
                                                                 UUID courseId) {

        final String sheetName = "SubjectWiseClassResultAnalysis";
        final String reportName = "Subject Wise Class Result Analysis";

        Institute institute = instituteManager.getInstitute(instituteId);
        final List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationManager.getResolvedClassMarks(
                instituteId, examId, courseId, sectionId == null ? null : new HashSet<>(Arrays.asList(sectionId)), false);

        if (CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }


        //Map of studentId and StudentExamMarksDetails
        final Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = getStudentExamMarksDetailMap(studentExamMarksDetailsList);
        Set<Integer> sectionIdSet = null;
        if (sectionId != null && sectionId > 0) {
            sectionIdSet = new HashSet<Integer>();
            sectionIdSet.add(sectionId);
        }
        final List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId, standardId,
                sectionIdSet);

        if (CollectionUtils.isEmpty(students)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        final Map<UUID, LinkedHashMap<UUID, Course>> studentCourseMap = getStudentCourseMap(instituteId,
                academicSessionId, standardId);

        if (CollectionUtils.isEmpty(studentCourseMap.entrySet())) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Course course = courseManager.getCourseDetails(courseId);
        final Map<CourseType, List<ExamGrade>> examGradesMap = examinationManager.getExamGrades(instituteId,
                academicSessionId, standardId);
        List<ExamGrade> examGradesList = examGradesMap.get(course.getCourseType());

        if (CollectionUtils.isEmpty(examGradesList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Collections.sort(examGradesList, new Comparator<ExamGrade>() {
            @Override
            public int compare(ExamGrade s1, ExamGrade s2) {
                return s2.getGradeValue().compareTo(s1.getGradeValue());
            }
        });

        //Map of gradeId, Gender, no of student od gender x having grade as gradeId
        LinkedHashMap<Integer, Map<Gender, Integer>> gradeGenderMap = new LinkedHashMap<Integer, Map<Gender, Integer>>();
        for (ExamGrade examGrade : examGradesList) {
            Map<Gender, Integer> genderCountMap = new HashMap<Gender, Integer>();
            genderCountMap.put(Gender.MALE, 0);
            genderCountMap.put(Gender.FEMALE, 0);
            genderCountMap.put(Gender.TRANSGENDER, 0);
            gradeGenderMap.put(examGrade.getGradeId(), genderCountMap);
        }

        //Map of gradeId, String(PASSED, APPEARED), no of student of gender x having grade as gradeId
        Map<String, Map<Gender, Integer>> statusGenderMap = new HashMap<String, Map<Gender, Integer>>();
        Map<Gender, Integer> genderCountAppearedMap = new HashMap<Gender, Integer>();
        genderCountAppearedMap.put(Gender.MALE, 0);
        genderCountAppearedMap.put(Gender.FEMALE, 0);
        genderCountAppearedMap.put(Gender.TRANSGENDER, 0);
        statusGenderMap.put("APPEARED", genderCountAppearedMap);

        Map<Gender, Integer> genderCountPassedMap = new HashMap<Gender, Integer>();
        genderCountPassedMap.put(Gender.MALE, 0);
        genderCountPassedMap.put(Gender.FEMALE, 0);
        genderCountPassedMap.put(Gender.TRANSGENDER, 0);
        statusGenderMap.put("PASSED", genderCountPassedMap);


        for (final Student student : students) {
            if (!validStudentRow(student, sectionId, studentExamMarksDetailsMap)) {
                continue;
            }
            Gender gender = student.getStudentBasicInfo().getGender();
            if (gender == null) {
                continue;
            }
            StudentExamMarksDetails studentExamMarksDetails = studentExamMarksDetailsMap.get(student.getStudentId());

            double maxMarks = 0d;
            double marksObtained = 0d;
            final Map<CourseType, List<ExamCourseMarks>> courseMarksMatrix = studentExamMarksDetails
                    .getCourseMarksMatrix();
            if (!CollectionUtils.isEmpty(courseMarksMatrix.get(CourseType.SCHOLASTIC))) {
                final MarksRowData marksRowData = getMarksRowData(instituteId,
                        courseMarksMatrix.get(CourseType.SCHOLASTIC), studentCourseMap.get(student.getStudentId()));
                maxMarks += marksRowData.getTotalMaxMarks();
                marksObtained += marksRowData.getTotalObtainedMarks();
            }

            if (!CollectionUtils.isEmpty(courseMarksMatrix.get(CourseType.COSCHOLASTIC))) {
                final MarksRowData marksRowData = getMarksRowData(instituteId,
                        courseMarksMatrix.get(CourseType.COSCHOLASTIC), studentCourseMap.get(student.getStudentId()));
                maxMarks += marksRowData.getTotalMaxMarks();
                marksObtained += marksRowData.getTotalObtainedMarks();
            }
            Double percentage = null;
            if (maxMarks > 0d) {
                percentage = marksObtained / maxMarks;
            }

            /**
             * Assuming if scholastic and coscholastic course are shown
             * together then grading scheme is same. So using scholastic
             * grading scheme only
             */
            final ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradesList, percentage);

            if (examGrade == null || examGrade.getGradeId() == null) {
                continue;
            }

            Integer gradeId = examGrade.getGradeId();
            gradeGenderMap.get(gradeId).put(gender, gradeGenderMap.get(gradeId).get(gender) + 1);
            statusGenderMap.get("APPEARED").put(gender, statusGenderMap.get("APPEARED").get(gender) + 1);

            final ExamResultStatus examResultStatus = ExamMarksUtils.getExamResultStatus(percentage);

            if (examResultStatus == ExamResultStatus.PASS) {
                statusGenderMap.get("PASSED").put(gender, statusGenderMap.get("PASSED").get(gender) + 1);
            }
        }

        //Assuming students is not empty taking standard from first student object
        Student student = students.get(0);
        Standard standard = student.getStudentAcademicSessionInfoResponse().getStandard();
        return getSubjectWiseClassResultAnalysisReportDetails(reportName, sheetName, instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId),
                gradeGenderMap, statusGenderMap, getExamGradeMap(examGradesList), examinationManager.getExamDetails(examId, instituteId).getExamMetaData(),
                standard, course, institute);
    }

    private LinkedHashMap<Integer, ExamGrade> getExamGradeMap(List<ExamGrade> examGradesList) {

        Collections.sort(examGradesList, new Comparator<ExamGrade>() {
            @Override
            public int compare(ExamGrade s1, ExamGrade s2) {
                return s2.getGradeValue().compareTo(s1.getGradeValue());
            }
        });

        LinkedHashMap<Integer, ExamGrade> examGradeMap = new LinkedHashMap<Integer, ExamGrade>();

        for (ExamGrade examGrade : examGradesList) {
            examGradeMap.put(examGrade.getGradeId(), examGrade);
        }
        return examGradeMap;
    }

    private MarksRowData getMarksRowDataOfCourse(int instituteId, ExamCourseMarks examCourseMarks, Map<UUID, Course> studentCourseMap) {

        double totalMaxMarks = 0d;
        double totalObtainedMarks = 0d;

        for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                .getExamDimensionObtainedValues()) {
            final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
            final Double obtainedMarks = examDimensionObtainedValues.getObtainedMarks();

            /**
             * Add total Marks
             */
            if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                totalMaxMarks += (maxMarks == null || maxMarks < 0d ? 0d : maxMarks);
                totalObtainedMarks += (obtainedMarks == null || obtainedMarks < 0d ? 0d : obtainedMarks);
            }
        }
        return new MarksRowData(totalMaxMarks, totalObtainedMarks);
    }

    private MarksRowData getMarksRowData(int instituteId, List<ExamCourseMarks> examCourseMarksList, Map<UUID, Course> studentCourseMap) {

        /**
         * Make sure sort header course also
         */
        Collections.sort(examCourseMarksList, new Comparator<ExamCourseMarks>() {
            @Override
            public int compare(ExamCourseMarks s1, ExamCourseMarks s2) {
                return s1.getCourse().compareTo(s2.getCourse());
            }
        });

        double totalMaxMarks = 0d;
        double totalObtainedMarks = 0d;

        for (final ExamCourseMarks examCourseMarks : examCourseMarksList) {

            if (!studentCourseMap.containsKey(examCourseMarks.getCourse().getCourseId())) {
                continue;
            }
            for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                    .getExamDimensionObtainedValues()) {
                final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                final Double obtainedMarks = examDimensionObtainedValues.getObtainedMarks();

                /**
                 * Add total Marks
                 */
                if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                    if (instituteId != 10001
                            || (!examCourseMarks.getCourse().getCourseName().equalsIgnoreCase("Foundation of IT*"))) {
                        totalMaxMarks += (maxMarks == null || maxMarks < 0d ? 0d : maxMarks);
                        totalObtainedMarks += (obtainedMarks == null || obtainedMarks < 0d ? 0d : obtainedMarks);
                    }
                }
            }

        }
        return new MarksRowData(totalMaxMarks, totalObtainedMarks);
    }

    private ReportDetails getSubjectWiseClassResultAnalysisReportDetails(String reportName, String sheetName, AcademicSession academicSession,
                                                                         LinkedHashMap<Integer, Map<Gender, Integer>> gradeGenderMap, Map<String, Map<Gender, Integer>> statusGenderMap,
                                                                         LinkedHashMap<Integer, ExamGrade> examGradeMap, ExamMetaData examMetaData, Standard standard, Course course,
                                                                         Institute institute) {

        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        generateReportHeadingDetails(academicSession, standard, course, headerReportCellDetails, headerMergeCellIndexesList);

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        generateReportTableTopHeadingDetails(examMetaData, reportCellDetails, mergeCellIndexesList);
        generateReportTableFirstHeadingDetails(reportCellDetails, mergeCellIndexesList);
        generateReportTableFirstDataDetails(statusGenderMap, reportCellDetails, mergeCellIndexesList);
        generateReportTableSecondHeadingDetails(reportCellDetails, mergeCellIndexesList);
        generateReportTableSecondDataDetails(gradeGenderMap, examGradeMap, reportCellDetails, mergeCellIndexesList);

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, false, 5, mergeCellIndexesList,
//                reportCellDetails);

        int totalColumns = 5;
        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);

        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private void generateReportTableSecondDataDetails(LinkedHashMap<Integer, Map<Gender, Integer>> gradeGenderMap, LinkedHashMap<Integer, ExamGrade> examGradeMap,
                                                      List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {

        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        Map<Gender, Integer> genderWiseTotalMarks = new HashMap<Gender, Integer>();
        for (Entry<Integer, Map<Gender, Integer>> entry : gradeGenderMap.entrySet()) {
            reportCellDetailsRow = new ArrayList<ReportCellDetails>();
            String gradeName = examGradeMap.get(entry.getKey()).getGradeName();
            reportCellDetailsRow.add(new ReportCellDetails(gradeName, STRING,
                    CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                    true));

            String range = examGradeMap.get(entry.getKey()).getRangeDisplayName();
            if (StringUtils.isEmpty(range)) {
                Double startRange = (double) Math.round(examGradeMap.get(entry.getKey()).getMarksRangeStart() * 100);
                Double endRange = (double) (Math.round(examGradeMap.get(entry.getKey()).getMarksRangeEnd() * 100) > 100 ?
                        100.0 : Math.round(examGradeMap.get(entry.getKey()).getMarksRangeEnd() * 100));
                range = startRange + "-" + endRange;
            }
            reportCellDetailsRow.add(new ReportCellDetails(range, STRING,
                    CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                    true));

            int count = 0;
            int totalGradeStudents = 0;

            count = entry.getValue().get(Gender.MALE);
            if (genderWiseTotalMarks.containsKey(Gender.MALE)) {
                genderWiseTotalMarks.put(Gender.MALE, genderWiseTotalMarks.get(Gender.MALE) + count);
            } else {
                genderWiseTotalMarks.put(Gender.MALE, count);
            }
            totalGradeStudents += count;
            reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
                    CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                    false));


            count = entry.getValue().get(Gender.FEMALE);
            if (genderWiseTotalMarks.containsKey(Gender.FEMALE)) {
                genderWiseTotalMarks.put(Gender.FEMALE, genderWiseTotalMarks.get(Gender.FEMALE) + count);
            } else {
                genderWiseTotalMarks.put(Gender.FEMALE, count);
            }
            totalGradeStudents += count;
            reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
                    CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                    false));

            reportCellDetailsRow.add(new ReportCellDetails(totalGradeStudents, INTEGER,
                    CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                    true));
            reportCellDetails.add(reportCellDetailsRow);
        }

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails("Total", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(genderWiseTotalMarks.get(Gender.MALE), INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(genderWiseTotalMarks.get(Gender.FEMALE), INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(genderWiseTotalMarks.get(Gender.MALE) + genderWiseTotalMarks.get(Gender.FEMALE), INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsRow);
    }

    private void generateReportTableSecondHeadingDetails(List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {
        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails("GRADE", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("RANGE", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("BOYS", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("GIRLS", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("TOTAL", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsRow);
    }

    private void generateReportTableFirstDataDetails(Map<String, Map<Gender, Integer>> statusGenderMap, List<List<ReportCellDetails>> reportCellDetails,
                                                     List<CellIndexes> mergeCellIndexesList) {
        int totalAppearedStudents = 0;
        int totalAppearedBoysStudents = 0;
        int totalAppearedGirlsStudents = 0;
        int totalPassedStudents = 0;
        int totalPassedBoysStudents = 0;
        int totalPassedGirlsStudents = 0;
        int count = 0;

        Map<Gender, Integer> genderCountMap = new HashMap<Gender, Integer>();
        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

        genderCountMap = statusGenderMap.get("APPEARED");
        reportCellDetailsRow.add(new ReportCellDetails("APPEARED", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));

        count = genderCountMap.get(Gender.MALE);
        totalAppearedStudents += count;
        totalAppearedBoysStudents = count;
        reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                false));

        count = genderCountMap.get(Gender.FEMALE);
        totalAppearedStudents += count;
        totalAppearedGirlsStudents = count;
        reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                false));

        reportCellDetailsRow.add(new ReportCellDetails(totalAppearedStudents, INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsRow);


        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        genderCountMap = statusGenderMap.get("PASSED");
        reportCellDetailsRow.add(new ReportCellDetails("PASSED", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));

        count = genderCountMap.get(Gender.MALE);
        totalPassedStudents += count;
        totalPassedBoysStudents = count;
        reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                false));

        count = genderCountMap.get(Gender.FEMALE);
        totalPassedStudents += count;
        totalPassedGirlsStudents = count;
        reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                false));

        reportCellDetailsRow.add(new ReportCellDetails(totalPassedStudents, INTEGER,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));

        reportCellDetails.add(reportCellDetailsRow);


        reportCellDetailsRow = new ArrayList<ReportCellDetails>();

        reportCellDetailsRow.add(new ReportCellDetails("PASS%", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(totalAppearedBoysStudents <= 0 ? 0 : (totalPassedBoysStudents / totalAppearedBoysStudents) * 100, DOUBLE,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(totalAppearedGirlsStudents <= 0 ? 0 : (totalPassedGirlsStudents / totalAppearedGirlsStudents) * 100, DOUBLE,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsRow.add(new ReportCellDetails(totalAppearedStudents <= 0 ? 0 : (totalPassedStudents / totalAppearedStudents) * 100, DOUBLE,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsRow);


        CellIndexes cellIndexes = new CellIndexes(4, 4, 0, 1);
        mergeCellIndexesList.add(cellIndexes);

        cellIndexes = new CellIndexes(5, 5, 0, 1);
        mergeCellIndexesList.add(cellIndexes);

        cellIndexes = new CellIndexes(6, 6, 0, 1);
        mergeCellIndexesList.add(cellIndexes);

    }

    private void generateReportTableFirstHeadingDetails(List<List<ReportCellDetails>> reportCellDetails, List<CellIndexes> mergeCellIndexesList) {
        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsHeaderRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsHeaderRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsHeaderRow.add(new ReportCellDetails("BOYS", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsHeaderRow.add(new ReportCellDetails("GIRLS", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetailsHeaderRow.add(new ReportCellDetails("TOTAL", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsHeaderRow);

        CellIndexes cellIndexes = new CellIndexes(3, 3, 0, 1);
        mergeCellIndexesList.add(cellIndexes);
    }

    private void generateReportTableTopHeadingDetails(ExamMetaData examMetaData, List<List<ReportCellDetails>> reportCellDetails,
                                                      List<CellIndexes> mergeCellIndexesList) {
        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsHeaderRow.add(new ReportCellDetails("ANALYSIS", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsHeaderRow);

        reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsHeaderRow.add(new ReportCellDetails(examMetaData.getExamName(), STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                true));
        reportCellDetails.add(reportCellDetailsHeaderRow);

        CellIndexes cellIndexes = new CellIndexes(1, 1, 0, 4);
        mergeCellIndexesList.add(cellIndexes);

        cellIndexes = new CellIndexes(2, 2, 0, 4);
        mergeCellIndexesList.add(cellIndexes);
    }

    private void generateReportHeadingDetails(AcademicSession academicSession, Standard standard, Course course, List<List<ReportCellDetails>> reportCellDetails,
                                              List<CellIndexes> mergeCellIndexesList) {

        /**
         * In reportDetails object the first line of 2d list contain the heading this is mandatory
         */
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        String heading = "Academic Year : " + academicSession.getDisplayName() + " | Class : " + standard.getDisplayNameWithSection() + " | Subject : " + course.getCourseName();
        reportHeaderRow.add(new ReportCellDetails(heading, STRING,
                CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
                true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        reportCellDetails.add(reportHeaderRow);

        CellIndexes cellIndexes = new CellIndexes(0, 0, 0, 4);
        mergeCellIndexesList.add(cellIndexes);
    }

    private class MarksRowData {
        private final double totalMaxMarks;
        private final double totalObtainedMarks;
        private int colNum;

        public MarksRowData(int colNum, double totalMaxMarks, double totalObtainedMarks) {
            this.colNum = colNum;
            this.totalMaxMarks = totalMaxMarks;
            this.totalObtainedMarks = totalObtainedMarks;
        }

        public MarksRowData(double totalMaxMarks, double totalObtainedMarks) {
            this.totalMaxMarks = totalMaxMarks;
            this.totalObtainedMarks = totalObtainedMarks;
        }

        public int getColNum() {
            return colNum;
        }

        public double getTotalMaxMarks() {
            return totalMaxMarks;
        }

        public double getTotalObtainedMarks() {
            return totalObtainedMarks;
        }

    }

    public ReportDetails getStaffLevelResultAnalysis(int instituteId, int academicSessionId, UUID staffId,
                                                     String reportCardType, boolean isDivisionReport) {

        StaffStandardEntityDetails staffStandardEntityDetails = timetableManager
                .getStaffStandardEntityDetails(instituteId, academicSessionId, staffId);

        AcademicSession academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
        String academicSessionName = academicSession.getDisplayName();

        final String sheetName = isDivisionReport ? "TeacherWiseDivisionResultAnalysis" : "TeacherWiseGradeResultAnalysis";
        final String reportName = isDivisionReport ? "Teacherwise Division Result Analysis \n(" + academicSessionName + ")" : "Teacherwise Grade Result Analysis \n(" + academicSessionName + ")";

        if (staffStandardEntityDetails == null ||
                CollectionUtils.isEmpty(staffStandardEntityDetails.getStandardEntityDetailsList())) {
            logger.error(
                    "No classes assign to staff for institute {}, academicSessionId {}",
                    instituteId, academicSessionId);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Map<UUID, String> courseIdNameMap = new HashMap<>();
        Map<UUID, String> courseIdStandardNameMap = new HashMap<>();

        Map<UUID, Set<UUID>> standardIdCourseIdMap = new HashMap<>();
        Map<UUID, Set<Integer>> standardIdSectionIdMap = new HashMap<>();
        Map<UUID, Set<Integer>> courseIdSectionIdMap = new HashMap<>();
        StringBuilder teachersInClassesString = new StringBuilder();

        getStandardIdCourseIdMap(staffStandardEntityDetails.getStandardEntityDetailsList(), standardIdCourseIdMap, courseIdSectionIdMap,
                standardIdSectionIdMap, courseIdNameMap, courseIdStandardNameMap, teachersInClassesString);

        Map<UUID, ExamReportCardConfiguration> examReportCardConfigurationStandardIdMap = examReportCardManager
                .getExamReportCardConfigurationMap(instituteId, academicSessionId, reportCardType);

        if (examReportCardConfigurationStandardIdMap == null || CollectionUtils.isEmpty(examReportCardConfigurationStandardIdMap.entrySet())) {
            logger.error(
                    "Exam report meta data is not configured for institute {}, academicSessionId {}, reportType{}",
                    instituteId, academicSessionId, reportCardType);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        /**
         * Assuming all standards has same grading scheme
         */
        UUID globalStandardId = standardIdCourseIdMap.entrySet().iterator().next().getKey();
        List<ExamGrade> examGradeList = examinationManager.getExamGrades(instituteId, academicSessionId, globalStandardId, CourseType.SCHOLASTIC);
        LinkedHashSet<String> examGradeNameList = new LinkedHashSet<>();
        if (!CollectionUtils.isEmpty(examGradeList)) {
            for (ExamGrade examGrade : examGradeList) {
                if (!examGradeNameList.contains(examGrade.getGradeName())) {
                    examGradeNameList.add(examGrade.getGradeName());
                }
            }
        }

        //StandardId, CourseId, Division string, division Count
        ExamResultGradeDivisionDetails examResultGradeDivisionDetails = getStandardCourseDivisionCountMap(
                instituteId, academicSessionId, reportCardType, standardIdCourseIdMap, standardIdSectionIdMap, courseIdSectionIdMap,
                examReportCardConfigurationStandardIdMap, examGradeNameList, examGradeList);

        List<CourseStudents> studentCoursesList = courseManager.getStudentsByCourse(courseIdNameMap.keySet());
        Map<UUID, CourseStudents> courseIdCourseStudentsMap = new HashMap<>();
        for (CourseStudents courseStudents : studentCoursesList) {
            UUID courseId = courseStudents.getCourse().getCourseId();
            if (!courseIdCourseStudentsMap.containsKey(courseId)) {
                courseIdCourseStudentsMap.put(courseId, courseStudents);
            }
        }

        List<UUID> studentIdList = getStudentIdList(studentCoursesList);
        Set<StudentStatus> studentStatusSet = new HashSet<>();
        studentStatusSet.add(StudentStatus.ENROLLED);
        studentStatusSet.add(StudentStatus.RELIEVED);
        studentStatusSet.add(StudentStatus.DELETED);
        studentStatusSet.add(StudentStatus.NSO);
        studentStatusSet.add(StudentStatus.ENROLMENT_PENDING);
        List<Student> studentList = CollectionUtils.isEmpty(studentIdList) ? null : studentManager
                .getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIdList, studentStatusSet);
        Map<UUID, Student> studentMap = CollectionUtils.isEmpty(studentList) ? null : getStudentMap(studentList);

        Institute institute = instituteManager.getInstitute(instituteId);
        Map<UUID, Integer> courseIdStudentCount = new HashMap<>();
        for (Entry<UUID, CourseStudents> courseStudentsEntry : courseIdCourseStudentsMap.entrySet()) {
            UUID courseId = courseStudentsEntry.getKey();
            courseIdStudentCount.put(courseId, 0);
            if (courseStudentsEntry.getValue() == null || CollectionUtils.isEmpty(courseStudentsEntry.getValue().getStudents())) {
                continue;
            }
            if (studentMap == null) {
                continue;
            }
            for (UUID studentId : courseStudentsEntry.getValue().getStudents()) {
                Student student = studentMap.get(studentId);
                if (student == null) {
                    continue;
                }
                UUID standardId = student.getStudentAcademicSessionInfoResponse() == null ? null :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
                Integer sectionId = student.getStudentAcademicSessionInfoResponse() == null ? null :
                        CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard()
                                .getStandardSectionList()) ? null : student.getStudentAcademicSessionInfoResponse()
                                .getStandard().getStandardSectionList().get(0).getSectionId();

                if (!org.springframework.util.CollectionUtils.isEmpty(standardIdSectionIdMap) || standardId == null) {
                    Set<Integer> sectionIds = standardIdSectionIdMap.get(standardId);
                    if (!org.springframework.util.CollectionUtils.isEmpty(sectionIds)) {
                        Set<Integer> courseSectionIdList = courseIdSectionIdMap.get(courseId);
                        if (sectionId == null || !sectionIds.contains(sectionId) || !courseSectionIdList.contains(sectionId)) {
                            continue;
                        }
                    }
                }
                courseIdStudentCount.put(courseId, courseIdStudentCount.get(courseId) + 1);
            }
        }

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<>();
        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<>();

        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<>();

        int totalColumns = isDivisionReport ? 9 : examGradeList.size() + 5;
        /**
         * addign one more column in report which is 'board', so increasing total count by 1
         */
        totalColumns++;

        getReportCellDetails2DList(staffStandardEntityDetails, examResultGradeDivisionDetails, courseIdStandardNameMap,
                courseIdNameMap, courseIdStudentCount, isDivisionReport, new ArrayList<>(examGradeNameList),
                reportCellDetails, headerReportCellDetails, mergeCellIndexesList, headerMergeCellIndexesList, totalColumns,
                teachersInClassesString.toString(), academicSessionName);
        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
//                reportCellDetails2DList);

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);
        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private void getReportCellDetails2DList(StaffStandardEntityDetails staffStandardEntityDetails,
                                            ExamResultGradeDivisionDetails examResultGradeDivisionDetails,
                                            Map<UUID, String> courseIdStandardNameMap, Map<UUID, String> courseIdNameMap,
                                            Map<UUID, Integer> courseIdStudentCount, boolean isDivisionReport, List<String> examGradeNameList,
                                            List<List<ReportCellDetails>> reportCellDetails, List<List<ReportCellDetails>> headerReportCellDetails,
                                            List<CellIndexes> mergeCellIndexesList, List<CellIndexes> headerMergeCellIndexesList, int totalColumns, String teachersInClassesString, String academicSessionName) {

        FullStaffDetails staffDetails = staffManager.getFullStaffDetails(staffStandardEntityDetails.getStaff().getInstituteId(), staffStandardEntityDetails.getStaff());
        List<ReportCellDetails> reportCellDetailsList = getStaffDetailsReportHeader(staffDetails, teachersInClassesString, academicSessionName);
        if (!CollectionUtils.isEmpty(reportCellDetailsList)) {
            headerReportCellDetails.add(reportCellDetailsList);
            CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        reportCellDetailsList = getStaffResultReportHeader(isDivisionReport, examGradeNameList);
        if (!CollectionUtils.isEmpty(reportCellDetailsList)) {
            headerReportCellDetails.add(reportCellDetailsList);
        }

        if (isDivisionReport) {
            for (Entry<UUID, Map<UUID, Map<ExamDivision, Integer>>> standardCourseDivisionCountEntry :
                    examResultGradeDivisionDetails.getStandardCourseDivisionCountMap().entrySet()) {
                for (Entry<UUID, Map<ExamDivision, Integer>> courseDivisionCountEntry :
                        standardCourseDivisionCountEntry.getValue().entrySet()) {
                    reportCellDetailsList = getStaffResultReportData(courseDivisionCountEntry, courseIdStandardNameMap,
                            courseIdNameMap, courseIdStudentCount);
                    reportCellDetails.add(reportCellDetailsList);
                }
            }
        } else {
            for (Entry<UUID, Map<UUID, Map<String, Integer>>> standardCourseDivisionCountEntry :
                    examResultGradeDivisionDetails.getStandardCourseGradeCountMap().entrySet()) {
                for (Entry<UUID, Map<String, Integer>> courseDivisionCountEntry :
                        standardCourseDivisionCountEntry.getValue().entrySet()) {
                    reportCellDetailsList = getStaffResultGradeReportData(courseDivisionCountEntry, courseIdStandardNameMap,
                            courseIdNameMap, courseIdStudentCount, examGradeNameList);
                    reportCellDetails.add(reportCellDetailsList);
                }
            }
        }

        List<ReportCellDetails> footerReportCellDetailsList = new ArrayList<>();
        footerReportCellDetailsList.add(new ReportCellDetails("Date", STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE, 50f));
        for(int i = 0; i < 3; i++) {
            footerReportCellDetailsList.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR, false));
        }
        footerReportCellDetailsList.add(new ReportCellDetails("Principal's Signature & Seal", STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE, 50f));
        for(int i = 4; i < totalColumns - 1; i++) {
            footerReportCellDetailsList.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR, false));
        }
        reportCellDetails.add(footerReportCellDetailsList);

        mergeCellIndexesList.add(new CellIndexes(reportCellDetails.size() + 1, reportCellDetails.size() + 1, 0, 3));
        mergeCellIndexesList.add(new CellIndexes(reportCellDetails.size() + 1, reportCellDetails.size() + 1, 4, totalColumns - 1));

    }

    private List<ReportCellDetails> getStaffResultGradeReportData(Entry<UUID, Map<String, Integer>> courseDivisionCountEntry,
                                                                  Map<UUID, String> courseIdStandardNameMap,
                                                                  Map<UUID, String> courseIdNameMap,
                                                                  Map<UUID, Integer> courseIdStudentCount,
                                                                  List<String> examGradeNameList) {
        List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();

        UUID courseId = courseDivisionCountEntry.getKey();

        String standardName = courseIdStandardNameMap.get(courseId) == null ? "" : courseIdStandardNameMap.get(courseId);
        reportCellDetailsList.add(new ReportCellDetails(standardName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

        String courseName = courseIdNameMap.get(courseId) == null ? "" : courseIdNameMap.get(courseId);
        reportCellDetailsList.add(new ReportCellDetails(courseName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

        int totalStudents = courseIdStudentCount.get(courseId) == null ? 0 : courseIdStudentCount.get(courseId);
        reportCellDetailsList.add(new ReportCellDetails(totalStudents, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        int totalCount = 0;


        for (int i = 0; i < examGradeNameList.size() - 1; i++) {
            int countFirstDivision = courseDivisionCountEntry.getValue().get(examGradeNameList.get(i));
            reportCellDetailsList.add(new ReportCellDetails(countFirstDivision, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            totalCount += countFirstDivision;
        }

        reportCellDetailsList.add(new ReportCellDetails(totalCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        double percentage = totalStudents == 0 ? 0 : ((totalCount / (totalStudents * 1d)) * 100d);
        double percentageRoundOff = Math.round(percentage * 100) / 100d;
        reportCellDetailsList.add(new ReportCellDetails(percentageRoundOff, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        if (CollectionUtils.isNotEmpty(examGradeNameList)) {
            String lastExamGradeName = examGradeNameList.get(examGradeNameList.size() - 1);
            int failedCount = 0;
            if (NumberUtils.isNumeric(lastExamGradeName)) {
                int index = Integer.parseInt(lastExamGradeName);
                failedCount = courseDivisionCountEntry.getValue().getOrDefault(index, 0);
            }
            reportCellDetailsList.add(new ReportCellDetails(failedCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        }

        reportCellDetailsList.add(new ReportCellDetails(EMPTY_TEXT, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        return reportCellDetailsList;
    }

    private List<ReportCellDetails> getStaffResultReportData(Entry<UUID, Map<ExamDivision, Integer>> courseDivisionCountEntry,
                                                             Map<UUID, String> courseIdStandardNameMap,
                                                             Map<UUID, String> courseIdNameMap,
                                                             Map<UUID, Integer> courseIdStudentCount) {
        List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();

        UUID courseId = courseDivisionCountEntry.getKey();

        String standardName = courseIdStandardNameMap.get(courseId) == null ? "" : courseIdStandardNameMap.get(courseId);
        reportCellDetailsList.add(new ReportCellDetails(
                standardName, STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

        String courseName = courseIdNameMap.get(courseId) == null ? "" : courseIdNameMap.get(courseId);
        reportCellDetailsList.add(new ReportCellDetails(
                courseName, STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        int totalStudents = courseIdStudentCount.get(courseId) == null ? 0 : courseIdStudentCount.get(courseId);
        reportCellDetailsList.add(new ReportCellDetails(
                totalStudents, INTEGER, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR,
                false));

        int totalCount = 0;

        int countFirstDivision = courseDivisionCountEntry.getValue().get(ExamDivision.I);
        reportCellDetailsList.add(new ReportCellDetails(countFirstDivision, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        totalCount += countFirstDivision;

        int countSecondDivision = courseDivisionCountEntry.getValue().get(ExamDivision.II);
        reportCellDetailsList.add(new ReportCellDetails(countSecondDivision, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        totalCount += countSecondDivision;

        int countThirdDivision = courseDivisionCountEntry.getValue().get(ExamDivision.III);
        reportCellDetailsList.add(new ReportCellDetails(countThirdDivision, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        totalCount += countThirdDivision;


        reportCellDetailsList.add(new ReportCellDetails(totalCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        double percentage = totalStudents == 0 ? 0 : ((totalCount / (totalStudents * 1d)) * 100d);
        double percentageRoundOff = Math.round(percentage * 100) / 100d;
        reportCellDetailsList.add(new ReportCellDetails(percentageRoundOff, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        int countFourthDivision = courseDivisionCountEntry.getValue().get(ExamDivision.IV);
        reportCellDetailsList.add(new ReportCellDetails(countFourthDivision, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        reportCellDetailsList.add(new ReportCellDetails(EMPTY_TEXT, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        return reportCellDetailsList;
    }

    private List<ReportCellDetails> getStaffResultReportHeader(boolean isDivisionReport, List<String> examGradeNameList) {
        if (isDivisionReport) {
            return getStaffDivisionResultReportHeader();
        } else {
            return getStaffGradeResultReportHeader(examGradeNameList);
        }
    }

    private List<ReportCellDetails> getStaffGradeResultReportHeader(List<String> examGradeNameList) {
        List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();
        reportCellDetailsList.add(new ReportCellDetails("Class", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Subject", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Total Students", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

        for (int i = 0; i < examGradeNameList.size() - 1; i++) {
            reportCellDetailsList.add(new ReportCellDetails(examGradeNameList.get(i), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        reportCellDetailsList.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Percentage (%)", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        if(CollectionUtils.isNotEmpty(examGradeNameList)){
            reportCellDetailsList.add(new ReportCellDetails(examGradeNameList.get(examGradeNameList.size() - 1),
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        reportCellDetailsList.add(new ReportCellDetails("Board", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        return reportCellDetailsList;
    }

    private List<ReportCellDetails> getStaffDivisionResultReportHeader() {
        List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();
        reportCellDetailsList.add(new ReportCellDetails("Class", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Subject", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Total Students", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails(ExamDivision.I.name(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails(ExamDivision.II.name(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails(ExamDivision.III.name(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Percentage (%)", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("E", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsList.add(new ReportCellDetails("Board", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        return reportCellDetailsList;
    }

    private List<ReportCellDetails> getStaffDetailsReportHeader(FullStaffDetails staff, String teachersInClassesString, String sessionDetails) {
        List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();
        String teacherWiseResultAnalysisReportHeading = String.format(TEACHER_WISE_RESULT_ANALYSIS_REPORT_HEADING,
                staff.getStaffBasicDetailsWithCategoryDepartDesignation().getName(),
                String.join(", ", staff.getStaffBasicDetailsWithCategoryDepartDesignation().getStaffDesignationList()),
                staff.getStaffBasicDetailsWithCategoryDepartDesignation().getStaffInstituteId(),
                teachersInClassesString, sessionDetails);
        reportCellDetailsList.add(new ReportCellDetails(teacherWiseResultAnalysisReportHeading, STRING, CONTENT_SIZE + 1,
                BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        return reportCellDetailsList;
    }

    private ExamResultGradeDivisionDetails getStandardCourseDivisionCountMap(int instituteId,
                                                                             int academicSessionId, String reportCardType, Map<UUID, Set<UUID>> standardIdCourseIdMap,
                                                                             Map<UUID, Set<Integer>> standardIdSectionIdMap,
                                                                             Map<UUID, Set<Integer>> courseIdSectionIdMap,
                                                                             Map<UUID, ExamReportCardConfiguration> examReportCardConfigurationStandardIdMap,
                                                                             LinkedHashSet<String> examGradeNameList, List<ExamGrade> examGradeList) {

        Map<UUID, Map<UUID, Map<ExamDivision, Integer>>> standardCourseDivisionCount = new HashMap<>();
        Map<UUID, Map<UUID, Map<String, Integer>>> standardCourseGradeCount = new HashMap<>();
        for (Map.Entry<UUID, Set<UUID>> entry : standardIdCourseIdMap.entrySet()) {

            UUID standardId = entry.getKey();
            standardCourseDivisionCount.put(standardId, new HashMap<>());
            standardCourseGradeCount.put(standardId, new HashMap<>());
            Set<Integer> sectionIdList = standardIdSectionIdMap.get(standardId);

            ExamReportCardConfiguration examReportCardConfiguration = examReportCardConfigurationStandardIdMap.get(standardId);
            if (examReportCardConfiguration == null || examReportCardConfiguration.getExamReportStructure() == null) {
                logger.error(
                        "Exam report meta data is not configured for institute {}, academicSessionId {}, standardId {},reportType{}",
                        instituteId, academicSessionId, standardId, reportCardType);
                continue;
            }

            List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                    academicSessionId, standardId, CollectionUtils.isEmpty(sectionIdList) ? null : new HashSet<>(sectionIdList),
                    examReportCardConfiguration.getExamReportStructure(), reportCardType, false);

            if (org.springframework.util.CollectionUtils.isEmpty(classExamReportData)) {
                continue;
            }

            logger.info("Exam report card data computation done for students {}. Starting report generation...",
                    classExamReportData.size());

            Iterator<ExamReportData> classExamReportDataIterator = classExamReportData.iterator();
            while (classExamReportDataIterator.hasNext()) {
                ExamReportData examReportData = classExamReportDataIterator.next();
                if (examReportData.getStudentLite() == null) {
                    classExamReportDataIterator.remove();
                }
            }

            logger.info("Exam report card data of filtered student computation done for students {}. Starting report generation...",
                    classExamReportData.size());

            for (UUID courseId : entry.getValue()) {

                standardCourseDivisionCount.get(standardId).put(courseId, new TreeMap<>());
                standardCourseDivisionCount.get(standardId).get(courseId).put(ExamDivision.I, 0);
                standardCourseDivisionCount.get(standardId).get(courseId).put(ExamDivision.II, 0);
                standardCourseDivisionCount.get(standardId).get(courseId).put(ExamDivision.III, 0);
                standardCourseDivisionCount.get(standardId).get(courseId).put(ExamDivision.IV, 0);

                standardCourseGradeCount.get(standardId).put(courseId, new TreeMap<>());
                for (String gradeName : examGradeNameList) {
                    standardCourseGradeCount.get(standardId).get(courseId).put(gradeName, 0);
                }

                for (ExamReportData examReportData : classExamReportData) {
                    Integer sectionId = examReportData.getStudentLite().getStudentSessionData().getStandardSection() == null ? null :
                            examReportData.getStudentLite().getStudentSessionData().getStandardSection().getSectionId();
                    if (!org.springframework.util.CollectionUtils.isEmpty(standardIdSectionIdMap)) {
                        Set<Integer> sectionIds = standardIdSectionIdMap.get(standardId);
                        if (!org.springframework.util.CollectionUtils.isEmpty(sectionIds)) {
                            Set<Integer> courseSectionIdList = courseIdSectionIdMap.get(courseId);
                            if (sectionId == null || !sectionIds.contains(sectionId) || !courseSectionIdList.contains(sectionId)) {
                                continue;
                            }
                        }
                    }
                    ExamReportMarksGrid examReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC);
                    for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
                        if (examReportCourseMarksRow.getCourse().getCourseId().equals(courseId)) {
                            List<ExamReportCourseMarksColumn> examReportCourseMarksColumnList
                                    = examReportCourseMarksRow.getExamReportCourseMarksColumns();
                            if (org.springframework.util.CollectionUtils.isEmpty(examReportCourseMarksColumnList)) {
                                continue;
                            }
                            boolean lastSum = false;
                            for (int i = examReportCourseMarksColumnList.size() - 1; i >= 0; i--) {
                                if (lastSum) {
                                    break;
                                }
                                ExamReportCourseMarksColumn examReportCourseMarksColumn = examReportCourseMarksColumnList.get(i);
                                if (examReportCourseMarksColumn
                                        .getExamReportGridColumnType() == ExamReportGridColumnType.SUM) {
                                    lastSum = true;
                                    for (ExamDimensionObtainedValues examDimensionObtainedValues :
                                            examReportCourseMarksColumn.getExamDimensionObtainedValuesList()) {
                                        Double marks = examDimensionObtainedValues.getObtainedMarksFraction() == null ? null
                                                : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 1000) / 10d;

                                        if (marks != null) {

                                            ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradeList, (marks / 100));
                                            if (examGrade != null) {
                                                String gradeName = examGrade.getGradeName();
                                                int gradeCount = standardCourseGradeCount.get(standardId).get(courseId).get(gradeName);
                                                standardCourseGradeCount.get(standardId).get(courseId).put(gradeName, gradeCount + 1);
                                            }

                                            ExamDivision examDivision = ExamDivision.getExamDivisionByMarks(marks);
                                            int count = standardCourseDivisionCount.get(standardId).get(courseId).get(examDivision);
                                            standardCourseDivisionCount.get(standardId).get(courseId).put(examDivision, count + 1);

                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return new ExamResultGradeDivisionDetails(standardCourseDivisionCount, standardCourseGradeCount);
    }

    private List<UUID> getStudentIdList(List<CourseStudents> studentCoursesList) {
        if (CollectionUtils.isEmpty(studentCoursesList)) {
            return null;
        }
        HashSet<UUID> studentIdSet = new HashSet<>();
        for (CourseStudents courseStudents : studentCoursesList) {
            studentIdSet.addAll(courseStudents.getStudents());
        }
        return CollectionUtils.isEmpty(studentIdSet) ? null : new ArrayList<>(studentIdSet);
    }

    private void getStandardIdCourseIdMap(List<StandardEntityDetails> standardEntityDetailsList,
                                          Map<UUID, Set<UUID>> standardIdCourseIdMap,
                                          Map<UUID, Set<Integer>> courseIdSectionIdMap,
                                          Map<UUID, Set<Integer>> standardIdSectionIdMap,
                                          Map<UUID, String> courseIdNameMap,
                                          Map<UUID, String> courseIdStandardNameMap,
                                          StringBuilder teachersInClassesString) {
        Set<UUID> standardIdSet = new HashSet<>();
        if (org.springframework.util.CollectionUtils.isEmpty(standardEntityDetailsList)) {
            return;
        }
        for (StandardEntityDetails standardEntityDetails : standardEntityDetailsList) {
            if (standardEntityDetails == null) {
                continue;
            }
            UUID standardId = standardEntityDetails.getStandardRowDetails().getStandardId();

            Integer sectionId = standardEntityDetails.getStandardRowDetails().getSectionId();
            if (standardIdSectionIdMap.containsKey(standardId)) {
                Set<Integer> sectionIdList = standardIdSectionIdMap.get(standardId);
                if (sectionId != null && !sectionIdList.contains(sectionId)) {
                    sectionIdList.add(sectionId);
                }
            } else {
                Set<Integer> sectionIdList = new HashSet<>();
                if (sectionId != null) {
                    sectionIdList.add(sectionId);
                }
                standardIdSectionIdMap.put(standardId, sectionIdList);
            }

            for (EntityDetails entityDetails : standardEntityDetails.getEntityDetailsList()) {
                if (entityDetails.getEntity() == Entity.ACTIVITY) {
                    continue;
                }
                UUID courseId = entityDetails.getEntityId();

                if (courseIdSectionIdMap.containsKey(courseId)) {
                    Set<Integer> sectionIdList = courseIdSectionIdMap.get(courseId);
                    if (sectionId != null && !sectionIdList.contains(sectionId)) {
                        sectionIdList.add(sectionId);
                    }
                } else {
                    Set<Integer> sectionIdList = new HashSet<>();
                    if (sectionId != null) {
                        sectionIdList.add(sectionId);
                    }
                    courseIdSectionIdMap.put(courseId, sectionIdList);
                }

                if (!courseIdNameMap.containsKey(courseId)) {
                    courseIdNameMap.put(courseId, entityDetails.getEntityIdVal());
                }

                if (standardIdCourseIdMap.containsKey(standardId)) {
                    standardIdCourseIdMap.get(standardId).add(courseId);
                } else {
                    Set<UUID> courseIdList = new HashSet<>();
                    courseIdList.add(courseId);
                    standardIdCourseIdMap.put(standardId, courseIdList);
                }
            }
            for (Entry<UUID, Set<Integer>> courseIdSectionIdEntry : courseIdSectionIdMap.entrySet()) {
                UUID courseId = courseIdSectionIdEntry.getKey();
                if (sectionId == null) {
                    if (!courseIdStandardNameMap.containsKey(courseId)) {
                        courseIdStandardNameMap.put(courseId, standardEntityDetails.getStandardRowDetails().getStandardName());
                        if(!standardIdSet.contains(standardId)) {
                            if (!StringUtils.isBlank(teachersInClassesString)) {
                                teachersInClassesString.append(", ");
                            }
                            teachersInClassesString.append(standardEntityDetails.getStandardRowDetails().getStandardName());
                            standardIdSet.add(standardId);
                        }
                    }
                    continue;
                }
                Set<Integer> sectionIdList = courseIdSectionIdEntry.getValue();
                if (!sectionIdList.contains(sectionId)) {
                    continue;
                }
                String standardName = standardEntityDetails.getStandardRowDetails().getStandardName() +
                        (StringUtils.isBlank(standardEntityDetails.getStandardRowDetails().getSectionName()) ?
                                "" : " - " + standardEntityDetails.getStandardRowDetails().getSectionName());
                if (courseIdStandardNameMap.containsKey(courseId)) {
                    standardName = courseIdStandardNameMap.get(courseId);
                    standardName = standardName + ", " + standardEntityDetails.getStandardRowDetails().getStandardName() +
                            (StringUtils.isBlank(standardEntityDetails.getStandardRowDetails().getSectionName()) ?
                                    "" : " - " + standardEntityDetails.getStandardRowDetails().getSectionName());
                }
                courseIdStandardNameMap.put(courseId, standardName);
                if(!standardIdSet.contains(standardId)) {
                    if (!StringUtils.isBlank(teachersInClassesString)) {
                        teachersInClassesString.append(", ");
                    }
                    teachersInClassesString.append(standardEntityDetails.getStandardRowDetails().getStandardName());
                    standardIdSet.add(standardId);
                }
            }
        }
    }

    private ReportDetails getClassLevelDivisionWiseReport(int instituteId, int academicSessionId,
                                                          String reportCardType, boolean isDivisionReport) {

        Institute institute = instituteManager.getInstitute(instituteId);
        String instituteName = institute.getInstituteName();
        String branchName = institute.getBranchName();
        String instituteFinalString = instituteName + (StringUtils.isBlank(branchName) ? "" : " - " + branchName);

        AcademicSession academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
        String academicSessionName = academicSession.getDisplayName();

        final String sheetName = isDivisionReport ? "ClassWiseDivisionResultAnalysis" : "ClassWiseGradeResultAnalysis";
        final String reportName = "Session : " + academicSessionName;

        List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);

        Map<UUID, ExamReportCardConfiguration> examReportCardConfigurationStandardIdMap = examReportCardManager
                .getExamReportCardConfigurationMap(instituteId, academicSessionId, reportCardType);

        if (examReportCardConfigurationStandardIdMap == null || CollectionUtils.isEmpty(examReportCardConfigurationStandardIdMap.entrySet())) {
            logger.error(
                    "Exam report meta data is not configured for institute {}, academicSessionId {}, reportType{}",
                    instituteId, academicSessionId, reportCardType);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        Map<UUID, List<ExamReportData>> standardWiseExamReportData = new HashMap<>();
        for (Standard standard : standardList) {
            if (standard == null) {
                continue;
            }
            UUID standardId = standard.getStandardId();
            standardWiseExamReportData.put(standardId, new ArrayList<>());
            ExamReportCardConfiguration examReportCardConfiguration = examReportCardConfigurationStandardIdMap.get(standardId);
            if (examReportCardConfiguration == null || examReportCardConfiguration.getExamReportStructure() == null) {
                logger.error(
                        "Exam report meta data is not configured for institute {}, academicSessionId {}, standardId {},reportType{}",
                        instituteId, academicSessionId, standardId, reportCardType);
                continue;
            }

            List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                    academicSessionId, standardId, null, examReportCardConfiguration.getExamReportStructure(),
                    reportCardType, false);

            if (CollectionUtils.isEmpty(classExamReportData)) {
                continue;
            }
            standardWiseExamReportData.get(standardId).addAll(classExamReportData);
        }

        Map<UUID, Map<ExamDivision, Integer>> standardDivisionCount = new HashMap<>();
        Map<UUID, Map<GreensheetGrade, Integer>> standardGradeCount = new HashMap<>();
        for (Entry<UUID, List<ExamReportData>> entry : standardWiseExamReportData.entrySet()) {
            UUID standardId = entry.getKey();
            if (standardId == null) {
                continue;
            }
            Map<ExamDivision, Integer> divisionCountMap = new HashMap<>();
            divisionCountMap.put(ExamDivision.I, 0);
            divisionCountMap.put(ExamDivision.II, 0);
            divisionCountMap.put(ExamDivision.III, 0);
            divisionCountMap.put(ExamDivision.IV, 0);
            standardDivisionCount.put(entry.getKey(), divisionCountMap);

            Map<GreensheetGrade, Integer> greensheetCountMap = new HashMap<>();
            greensheetCountMap.put(GreensheetGrade.A, 0);
            greensheetCountMap.put(GreensheetGrade.B, 0);
            greensheetCountMap.put(GreensheetGrade.C, 0);
            greensheetCountMap.put(GreensheetGrade.D, 0);
            greensheetCountMap.put(GreensheetGrade.E, 0);
            standardGradeCount.put(entry.getKey(), greensheetCountMap);

            List<ExamReportData> examReportDataList = entry.getValue();
            for (ExamReportData examReportData : examReportDataList) {
                if (examReportData == null || examReportData.getStudentLite().getStudentStatus() != StudentStatus.ENROLLED) {
                    continue;
                }
                ExamDivision examDivision = ExamDivision.getExamDivisionByMarks(examReportData.getPercentage());
                if (examDivision != null) {
                    standardDivisionCount.get(standardId).put(
                            examDivision, standardDivisionCount.get(standardId).get(examDivision) + 1);
                }

                GreensheetGrade greensheetGrade = GreensheetGrade.getGreensheetGradeByMarks(examReportData.getPercentage());
                if (greensheetGrade != null) {
                    standardGradeCount.get(standardId).put(
                            greensheetGrade, standardGradeCount.get(standardId).get(greensheetGrade) + 1);
                }
            }
        }


        int totalColumns = isDivisionReport ? 5 : 7;
        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

        List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();
        reportCellDetailsList.add(new ReportCellDetails(instituteFinalString, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        headerReportCellDetails.add(reportCellDetailsList);

        headerMergeCellIndexesList.add(new CellIndexes(0, 0, 0, totalColumns - 1));

        reportCellDetailsList = new ArrayList<>();
        reportCellDetailsList.add(new ReportCellDetails("Classes",
                STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        if (isDivisionReport) {
            reportCellDetailsList.add(new ReportCellDetails("Division I",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsList.add(new ReportCellDetails("Division II",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsList.add(new ReportCellDetails("Division III",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        } else {
            reportCellDetailsList.add(new ReportCellDetails("Grade A",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsList.add(new ReportCellDetails("Grade B",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsList.add(new ReportCellDetails("Grade C",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsList.add(new ReportCellDetails("Grade D",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsList.add(new ReportCellDetails("Grade E",
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        reportCellDetailsList.add(new ReportCellDetails("Total",
                STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerReportCellDetails.add(reportCellDetailsList);

        int divisionITotal = 0;
        int divisionIITotal = 0;
        int divisionIIITotal = 0;
        int divisionFinalTotal = 0;
        int gradeATotal = 0;
        int gradeBTotal = 0;
        int gradeCTotal = 0;
        int gradeDTotal = 0;
        int gradeETotal = 0;
        int gradeFinalTotal = 0;

        List<List<ReportCellDetails>> reportCellDetails2DList = new ArrayList<>();
        for (Standard standard : standardList) {
            reportCellDetailsList = new ArrayList<>();
            UUID standardId = standard.getStandardId();
            reportCellDetailsList.add(new ReportCellDetails(standard.getDisplayName(),
                    STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            if (isDivisionReport) {
                Map<ExamDivision, Integer> divisionCountMap = standardDivisionCount.get(standardId);
                int divisionICount = divisionCountMap.get(ExamDivision.I);
                divisionITotal += divisionICount;
                reportCellDetailsList.add(new ReportCellDetails(divisionICount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int divisionIICount = divisionCountMap.get(ExamDivision.II);
                divisionIITotal += divisionIICount;
                reportCellDetailsList.add(new ReportCellDetails(divisionIICount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int divisionIIICount = divisionCountMap.get(ExamDivision.III);
                divisionIIITotal += divisionIIICount;
                reportCellDetailsList.add(new ReportCellDetails(divisionIIICount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int divisionTotal = divisionICount + divisionIICount + divisionIIICount;
                divisionFinalTotal += divisionTotal;
                reportCellDetailsList.add(new ReportCellDetails(divisionTotal,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetails2DList.add(reportCellDetailsList);
            } else {
                Map<GreensheetGrade, Integer> greensheetCountMap = standardGradeCount.get(standardId);
                int gradeACount = greensheetCountMap.get(GreensheetGrade.A);
                gradeATotal += gradeACount;
                reportCellDetailsList.add(new ReportCellDetails(gradeACount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int gradeBCount = greensheetCountMap.get(GreensheetGrade.B);
                gradeBTotal += gradeBCount;
                reportCellDetailsList.add(new ReportCellDetails(gradeBCount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int gradeCCount = greensheetCountMap.get(GreensheetGrade.C);
                gradeCTotal += gradeCCount;
                reportCellDetailsList.add(new ReportCellDetails(gradeCCount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int gradeDCount = greensheetCountMap.get(GreensheetGrade.D);
                gradeDTotal += gradeDCount;
                reportCellDetailsList.add(new ReportCellDetails(gradeDCount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int gradeECount = greensheetCountMap.get(GreensheetGrade.E);
                gradeETotal += gradeECount;
                reportCellDetailsList.add(new ReportCellDetails(gradeECount,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                int gradeTotal = gradeACount + gradeBCount + gradeCCount + gradeDCount + gradeECount;
                gradeFinalTotal += gradeTotal;
                reportCellDetailsList.add(new ReportCellDetails(gradeTotal,
                        INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetails2DList.add(reportCellDetailsList);
            }
        }

        reportCellDetailsList = new ArrayList<>();
        reportCellDetailsList.add(new ReportCellDetails("Total",
                STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        if (isDivisionReport) {
            reportCellDetailsList.add(new ReportCellDetails(divisionITotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(divisionIITotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(divisionIIITotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(divisionFinalTotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        } else {
            reportCellDetailsList.add(new ReportCellDetails(gradeATotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(gradeBTotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(gradeCTotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(gradeDTotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(gradeETotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsList.add(new ReportCellDetails(gradeFinalTotal,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        }
        reportCellDetails2DList.add(reportCellDetailsList);

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
//                reportCellDetails2DList);

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails2DList, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);
        return new ReportDetails(reportName, reportSheetDetailsList);

    }

    //TODO:rewrite the function, remove redundant code.
    private ReportDetails generateMultipleExamMarksReport(int instituteId, int academicSessionId, UUID standardId,
                                                          Set<Integer> sectionIdSet, Set<UUID> examIdSet, boolean excludeCoScholasticSubjects,
                                                          String requiredHeaders, boolean isSortStudentOnRank, Set<UUID> additionalCoursesSet,
                                                          Set<MarksDisplayType> scholasticMarksDisplayTypeSet, Set<MarksDisplayType> coScholasticMarksDisplayTypeSet,
                                                          boolean showClassAverageDetails, boolean showStaffDetails) {

        final String sheetName = "MultipleExamMarksReport";
        final String reportName = "Multiple Exam Marks Report";

        Map<UUID, List<Staff>> subjectTeacherMap = timetableManager.fetchSubjectTeacherDetails(instituteId, academicSessionId, standardId, sectionIdSet);

        Institute institute = instituteManager.getInstitute(instituteId);
        final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(instituteId,
                academicSessionId, standardId);

        if(CollectionUtils.isEmpty(examCoursesDataList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        boolean isCoScholasticGrade = standardMetaData.isCoScholasticGradingEnabled();
        boolean isScholasticGrade = standardMetaData.isScholasticGradingEnabled();

        String instituteName = institute.getInstituteName();
        StringBuilder academicSessionDisplayName = new StringBuilder();
        StringBuilder standardName = new StringBuilder();
        StringBuilder examNames = new StringBuilder();
        Map<Integer, String> sectionNameMap = new HashMap<>();

        Integer attendanceStartDate = Integer.MAX_VALUE;
        Integer attendanceEndDate = Integer.MIN_VALUE;

        Map<UUID, Course> courseDetailsMap = new HashMap<>();
        Map<UUID, Double> courseWiseMaxMarks = new HashMap<>();
        for(ExamCoursesData examCoursesData : examCoursesDataList) {
            UUID examId = examCoursesData.getExamMetaData().getExamId();
            if(!examIdSet.contains(examId)) {
                continue;
            }
            String examName = examCoursesData.getExamMetaData().getExamName();
            examNames.append(StringUtils.isBlank(examNames) ? examName : ", " + examName);

            Integer startDate = examCoursesData.getExamMetaData().getAttendanceStartDate();
            Integer endDate = examCoursesData.getExamMetaData().getAttendanceEndDate();

            if(startDate != null) {
                attendanceStartDate = Math.min(attendanceStartDate, startDate);
            }
            if(endDate != null) {
                attendanceEndDate = Math.max(attendanceEndDate, endDate);
            }

            List<ExamCourse> examCourseList = examCoursesData.getExamCourses();
            for(ExamCourse examCourse : examCourseList) {
                Course course = examCourse.getCourse();
                UUID courseId = course.getCourseId();
                if(!courseDetailsMap.containsKey(courseId)) {
                    courseDetailsMap.put(courseId, course);
                }
                if(!courseWiseMaxMarks.containsKey(courseId)) {
                    courseWiseMaxMarks.put(courseId, null);
                }
                for(ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
                    if(examDimensionValues.getExamDimension().isTotal()) {
                        continue;
                    }
                    courseWiseMaxMarks.put(courseId, NumberUtils.addValues(
                            courseWiseMaxMarks.get(courseId), examDimensionValues.getMaxMarks()));
                }
            }
        }

        List<Course> courseList = new ArrayList<>(courseDetailsMap.values());
        courseList = Course.sortCoursesBySequence(courseList);

        if(CollectionUtils.isEmpty(courseList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        boolean containsScholasticSubjects = false;
        boolean containsCoScholasticSubjects = false;
        LinkedHashMap<UUID, Course> filteredScholasticCourseMap = new LinkedHashMap<>();
        LinkedHashMap<UUID, Course> filteredCoScholasticCourseMap = new LinkedHashMap<>();
        for(Course course : courseList) {
            if(course == null || course.getCourseId() == null) {
                continue;
            }
            if(course.getCourseType() == CourseType.SCHOLASTIC) {
                containsScholasticSubjects = true;
                filteredScholasticCourseMap.put(course.getCourseId(), course);
            }
            if(!excludeCoScholasticSubjects && course.getCourseType() == CourseType.COSCHOLASTIC) {
                containsCoScholasticSubjects = true;
                filteredCoScholasticCourseMap.put(course.getCourseId(), course);
            }
        }

        Map<UUID, Boolean> examScholasticContainMap = new HashMap<>();
        Map<UUID, Boolean> examCoScholasticContainMap = new HashMap<>();
        ExamMarksUtils.getCoursesContainMapDetails(examCoursesDataList, examScholasticContainMap, examCoScholasticContainMap);

        String reportCardType = "REPORT";
        String examResultCalculatorId = examResultCalculatorFactory.getExamResultCalculatorId(instituteId, standardId);
        ExamReportCardConfiguration examReportCardConfiguration = ExamReportCardConfigurationUtils
                .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, examIdSet, reportCardType,
                        containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap,
                        null, true, true, examResultCalculatorId, attendanceStartDate, attendanceEndDate,
                        isCoScholasticGrade, isScholasticGrade, additionalCoursesSet);

        List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                academicSessionId, standardId, sectionIdSet, examReportCardConfiguration.getExamReportStructure(),
                reportCardType, true);

        boolean includeCoscholasticCourses = false;
        IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
        ExamResultAnalysisDetails examResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(classExamReportData,
                academicSessionDisplayName, standardName, sectionNameMap, sectionIdSet,
                null, includeCoscholasticCourses, additionalCoursesSet, isScholasticGrade, isCoScholasticGrade, rankCalculator);

        StringBuilder sectionNames = new StringBuilder();
        if(!CollectionUtils.isEmpty(sectionNameMap.entrySet())) {
            sectionNames.append(String.join(",", sectionNameMap.values()));
        }


        //CourseId/StudentId/Attendance
        Map<UUID, Map<UUID, ExamAttendanceStatus>> studentCourseExamAttendanceStatusMap = examResultAnalysisDetails.getStudentCourseExamAttendanceStatusMap();
        //CourseId/StudentId/Marks
        Map<UUID, Map<UUID, Double>> studentWiseCourseObtainedMarks = examResultAnalysisDetails.getStudentCourseObtainedMarksMap();
        //CourseId/StudentId/Marks
        Map<UUID, Map<UUID, Double>> studentWiseCourseMinMarksMap = examResultAnalysisDetails.getStudentCourseMinMarksMap();
        //CourseId/StudentId/Marks
        Map<UUID, Map<UUID, Double>> studentWiseCourseMaxMarksMap = examResultAnalysisDetails.getStudentCourseMaxMarksMap();
        //CourseId/StudentId/Marks
        Map<UUID, Map<UUID, Double>> studentWiseCoursePercetageMap = examResultAnalysisDetails.getStudentCoursePercentageMap();
        //StudentId/StudentLite
        Map<UUID, StudentLite> studentMap = examResultAnalysisDetails.getStudentLiteMap();
        List<StudentLite> studentLiteList = new ArrayList<>(studentMap.values());
        StudentDataUtils.sortStudentOnRollNumberAndName(studentLiteList);
        //StudentId/Marks
        Map<UUID, Double> studentObtainedMarksMap = examResultAnalysisDetails.getStudentObtainedMarksMap();
        //StudentId/Marks
        Map<UUID, Double> studentMaxMarksMap = examResultAnalysisDetails.getStudentMaxMarksMap();
        //StudentId/Percentage
        Map<UUID, Double> studentPercentageMap = examResultAnalysisDetails.getStudentPercentageMap();
        //StudentId/Grade
        Map<UUID, String> studentGradeMap = examResultAnalysisDetails.getStudentGradeMap();
        //StudentId/Division
        Map<UUID, ExamDivision> studentDivisionMap = examResultAnalysisDetails.getStudentDivisionMap();
        //StudentId/Result
        Map<UUID, ExamResultStatus> studentExamResultStatusMap = examResultAnalysisDetails.getStudentExamResultStatusMap();
        //StudentId/Rank
        Map<UUID, Integer> studentRankMap = examResultAnalysisDetails.getStudentRankMap();

        Map<UUID, Double> courseAvgMarksMap = examResultAnalysisDetails.getCourseAvgMarksMap();

        double avgPercentage = examResultAnalysisDetails.getAvgPercentage();

        if(isSortStudentOnRank) {
            studentLiteList = ExamMarksUtils.sortStudentOnRank(studentLiteList, studentRankMap);
        }
        Map<UUID, Map<AttendanceStatus, Integer>> studentAttendanceDetailsMap =
                studentManager.getStudentAttendanceDetails(instituteId, academicSessionId,
                        new HashSet<>(studentMap.keySet()), attendanceStartDate, attendanceEndDate);

        final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId,
                academicSessionId, standardId);

        List<StudentCourses> studentCoursesList = courseManager.getStudentCourses(
                instituteId, academicSessionId, standardId, sectionIdSet);

        Map<UUID, List<UUID>> studentCourseMap = ExamMarksUtils.getStudentAssignedCourseMap(studentCoursesList);

        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        String heading = "";
        if(!StringUtils.isBlank(instituteName)) {
            heading += "Institute Name : " + instituteName;
        }
        if(!StringUtils.isBlank(academicSessionDisplayName)) {
            heading += " | Session : " + academicSessionDisplayName;
        }
        if(!StringUtils.isBlank(standardName)) {
            heading+= " | Class : " + standardName;
        }
        if(!StringUtils.isBlank(sectionNames)) {
            heading+= " | Section(s) : " + sectionNames;
        }
        if(!StringUtils.isBlank(examNames)) {
            heading+= " | Exam(s) : " + examNames;
        }

        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        headerReportCellDetails.add(reportCellDetailsRow);

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        List<ReportCellDetails> reportCellDetailsRow2 = new ArrayList<ReportCellDetails>();

        int columnNum = 0;
        int courseStartColumn = 0;
        for(ReportHeaderAttribute reportHeaderAttribute : EXAM_MARKS_REPORT_HEADER_1) {
            if(requiredHeaders.contains(reportHeaderAttribute.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(reportHeaderAttribute.getDisplayName(),
                        STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
                        BLACK_COLOR, WHITE_COLOR, true));
                columnNum++;
                courseStartColumn++;
            }
        }

        for(Entry<UUID, Course> courseMap : filteredScholasticCourseMap.entrySet()) {
            UUID courseId = courseMap.getKey();
            Course course = courseMap.getValue();
            String courseName = course.getCourseName();
            reportCellDetailsRow.add(new ReportCellDetails(courseName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            Double maxMarks = courseWiseMaxMarks.get(courseId);
            int mergeCount = 0;
            if(scholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                reportCellDetailsRow2.add(new ReportCellDetails("Marks " + (maxMarks == null ? "" : " (" + NumberUtils.formatDouble(maxMarks) + ")"),
                        STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                mergeCount++;
            }
            if(scholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                if(mergeCount != 0) {
                    reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                }
                reportCellDetailsRow2.add(new ReportCellDetails("Grade",
                        STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                mergeCount++;
            }
            if(scholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                if(mergeCount != 0) {
                    reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                }
                reportCellDetailsRow2.add(new ReportCellDetails("%",
                        STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                mergeCount++;
            }
            headerMergeCellIndexesList.add(new CellIndexes(1, 1, columnNum, columnNum + mergeCount - 1));
            columnNum += mergeCount;
        }

        for(Entry<UUID, Course> courseMap : filteredCoScholasticCourseMap.entrySet()) {
            UUID courseId = courseMap.getKey();
            Course course = courseMap.getValue();
            String courseName = course.getCourseName();
            reportCellDetailsRow.add(new ReportCellDetails(courseName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            Double maxMarks = courseWiseMaxMarks.get(courseId);
            int mergeCount = 0;
            if(coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                reportCellDetailsRow2.add(new ReportCellDetails("Marks " + (maxMarks == null ? "" : " (" + NumberUtils.formatDouble(maxMarks) + ")"),
                        STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                mergeCount++;
            }
            if(coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                if(mergeCount != 0) {
                    reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                }
                reportCellDetailsRow2.add(new ReportCellDetails("Grade", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                mergeCount++;
            }
            if(coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                if(mergeCount != 0) {
                    reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                }
                reportCellDetailsRow2.add(new ReportCellDetails("%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                mergeCount++;
            }
            headerMergeCellIndexesList.add(new CellIndexes(1, 1, columnNum, columnNum + mergeCount - 1));
            columnNum += mergeCount;
        }

        reportCellDetailsRow.add(new ReportCellDetails("Grand Total", STRING, CONTENT_SIZE, BLACK_COLOR,
                WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("MM", STRING, CONTENT_SIZE, BLACK_COLOR,
                WHITE_COLOR, true));

        reportCellDetailsRow.add(new ReportCellDetails("Grand Total", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("MO", STRING, CONTENT_SIZE,
                BLACK_COLOR, WHITE_COLOR, true));

        for(ReportHeaderAttribute reportHeaderAttribute : EXAM_MARKS_REPORT_HEADER_2) {
            if(requiredHeaders.contains(reportHeaderAttribute.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(reportHeaderAttribute.getDisplayName(),
                        STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
                        BLACK_COLOR, WHITE_COLOR, true));
            }
        }

        int totalColumns = reportCellDetailsRow2.size();

        headerReportCellDetails.add(reportCellDetailsRow);
        headerReportCellDetails.add(reportCellDetailsRow2);
        headerMergeCellIndexesList.add(new CellIndexes(0, 0, 0, totalColumns - 1));

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        int index = 0;
        for(StudentLite studentLite : studentLiteList) {
            reportCellDetailsRow = new ArrayList<>();
            index++;
            UUID studentId = studentLite.getStudentId();
            String standardNameWithSection = studentLite.getStudentSessionData().getStandardNameWithSection();
            String rollNumber = studentLite.getStudentSessionData().getRollNumber();
            String studentName = studentLite.getName();
            String admissionNumber = studentLite.getAdmissionNumber();
            String fatherName = studentLite.getFathersName();
            String dob = studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? null :
                    DateUtils.getFormattedDate(studentLite.getDateOfBirth());

            Double totalMaxMarks = studentMaxMarksMap.get(studentId);
            Double totalObtainedMarks = studentObtainedMarksMap.get(studentId);
            Double percentage = studentPercentageMap.get(studentId);
            String grade = studentGradeMap.get(studentId);
            ExamDivision examDivision = studentDivisionMap.get(studentId);
            ExamResultStatus examResultStatus = studentExamResultStatusMap.get(studentId);
            Integer rank = studentRankMap.get(studentId);

            String attendance = AttendanceUtils.getAttendanceDetails(studentAttendanceDetailsMap, studentId);

            if (requiredHeaders.contains(ReportHeaderAttribute.EXAM_SR_NO.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(index, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.ADMISSION_No.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(admissionNumber, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.ROLL_NO.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(rollNumber, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.NAME.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(studentName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.FATHER_NAME.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(fatherName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.DOB.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(dob, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.CLASS.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(standardNameWithSection, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }

            for(Entry<UUID, Course> courseMap : filteredScholasticCourseMap.entrySet()) {
                UUID courseId = courseMap.getKey();
                Course course = courseMap.getValue();
                CourseType courseType = course.getCourseType();

                ExamAttendanceStatus examAttendanceStatus = studentCourseExamAttendanceStatusMap.get(courseId) == null ? null :
                        studentCourseExamAttendanceStatusMap.get(courseId).get(studentId);
                boolean courseAssignedToStudent = studentCourseMap.get(studentId).contains(courseId);

                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                    Double obtainedMarks = studentWiseCourseObtainedMarks.get(courseId) == null ? null :
                            studentWiseCourseObtainedMarks.get(courseId).get(studentId);
                    Double minMarks = studentWiseCourseMinMarksMap.get(courseId) == null ? null :
                            studentWiseCourseMinMarksMap.get(courseId).get(studentId);
                    Double maxMarks = studentWiseCourseMaxMarksMap.get(courseId) == null ? null :
                            studentWiseCourseMaxMarksMap.get(courseId).get(studentId);
                    /**
                     * adding check for institute 10045 as they don't want to see subject wise pass or fail in there reports
                     * so if instituteId == 10045, don't show minMarks suffix
                     * ow show
                     */
                    String finalValue = !courseAssignedToStudent ? EMPTY_TEXT :
                            ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                    obtainedMarks == null ? null : getRoundValueString(obtainedMarks),
                                    examAttendanceStatus, minMarks, instituteId != 10045,
                                    instituteId == 10295 ? "" : " (Fail)", maxMarks, NO_MARKS_TEXT, false);
                    reportCellDetailsRow.add(new ReportCellDetails(finalValue, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                    String finalValue = "";
                    Double maxMarks = courseWiseMaxMarks.get(courseId);
                    List<ExamGrade> examGradeList = courseTypeExamGrades.get(courseType);
                    if (!CollectionUtils.isEmpty(examGradeList)) {
                        if (isScholasticGrade) {
                            //TODO:add flow for calculation of multiple exam for grade
                            Double coursePercentage = studentWiseCoursePercetageMap.get(courseId) == null ?
                                    null : studentWiseCoursePercetageMap.get(courseId).get(studentId);
                            if(coursePercentage != null) {
                                ExamGrade examGrade = ExamMarksUtils.getExamGradeByValue(examGradeList, coursePercentage);
                                finalValue = !courseAssignedToStudent ? EMPTY_TEXT :
                                        ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                                examGrade == null ? null : examGrade.getGradeName(), examAttendanceStatus,
                                                null, instituteId != 10045,
                                                instituteId == 10295 ? "" : " (Fail)", null, NO_MARKS_TEXT, false);
                            }
                        } else {
                            if (maxMarks != null && maxMarks != 0) {
                                Double coursePercentage = studentWiseCoursePercetageMap.get(courseId) == null ?
                                        null : studentWiseCoursePercetageMap.get(courseId).get(studentId);
                                if(coursePercentage != null) {
                                    ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradeList, (coursePercentage / 100d));
                                    finalValue = !courseAssignedToStudent ? EMPTY_TEXT :
                                            ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                                    examGrade == null ? null : examGrade.getGradeName(), examAttendanceStatus,
                                                    null, instituteId != 10045,
                                                    instituteId == 10295 ? "" : " (Fail)", null, NO_MARKS_TEXT, false);
                                }
                            }
                        }
                    }
                    reportCellDetailsRow.add(new ReportCellDetails(finalValue, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                    Double coursePercentage = studentWiseCoursePercetageMap.get(courseId) == null ?
                            null : studentWiseCoursePercetageMap.get(courseId).get(studentId);
                    reportCellDetailsRow.add(new ReportCellDetails(coursePercentage, DOUBLE, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
                }
            }

            for(Entry<UUID, Course> courseMap : filteredCoScholasticCourseMap.entrySet()) {

                UUID courseId = courseMap.getKey();
                Course course = courseMap.getValue();
                CourseType courseType = course.getCourseType();
                boolean courseAssignedToStudent = studentCourseMap.get(studentId).contains(courseId);
                ExamAttendanceStatus examAttendanceStatus = studentCourseExamAttendanceStatusMap.get(courseId) == null ? null :
                        studentCourseExamAttendanceStatusMap.get(courseId).get(studentId);

                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                    Double obtainedMarks = studentWiseCourseObtainedMarks.get(courseId) == null ? null :
                            studentWiseCourseObtainedMarks.get(courseId).get(studentId);
                    Double minMarks = studentWiseCourseMinMarksMap.get(courseId) == null ? null :
                            studentWiseCourseMinMarksMap.get(courseId).get(studentId);
                    Double maxMarks = studentWiseCourseMaxMarksMap.get(courseId) == null ? null :
                            studentWiseCourseMaxMarksMap.get(courseId).get(studentId);
                    /**
                     * adding check for institute 10045 as they don't want to see subject wise pass or fail in there reports
                     * so if instituteId == 10045, don't show minMarks suffix
                     * ow show
                     */
                    String obtainedMarksValue = !courseAssignedToStudent ? EMPTY_TEXT :
                            ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                    obtainedMarks == null ? null : getRoundValueString(obtainedMarks),
                                    examAttendanceStatus, minMarks, instituteId != 10045,
                                    instituteId == 10295 ? "" : " (Fail)", maxMarks, NO_MARKS_TEXT, false);
                    String finalValue = obtainedMarksValue;
                    reportCellDetailsRow.add(new ReportCellDetails(finalValue, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                    String finalValue = "";
                    Double maxMarks = courseWiseMaxMarks.get(courseId);
                    List<ExamGrade> examGradeList = courseTypeExamGrades.get(courseType);
                    if (!CollectionUtils.isEmpty(examGradeList)) {
                        if (isCoScholasticGrade) {
                            //TODO:add flow for calculation of multiple exam for grade
                            Double coursePercentage = studentWiseCoursePercetageMap.get(courseId) == null ?
                                    null : studentWiseCoursePercetageMap.get(courseId).get(studentId);
                            if(coursePercentage != null) {
                                ExamGrade examGrade = ExamMarksUtils.getExamGradeByValue(examGradeList, coursePercentage);
                                String obtainedMarksValue = !courseAssignedToStudent ? EMPTY_TEXT :
                                        ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                                examGrade == null ? null : examGrade.getGradeName(), examAttendanceStatus,
                                                null, instituteId != 10045,
                                                instituteId == 10295 ? "" : " (Fail)", null, NO_MARKS_TEXT, false);
                                finalValue = obtainedMarksValue;
                            }
                        } else {
                            if (maxMarks != null && maxMarks != 0) {
                                Double coursePercentage = studentWiseCoursePercetageMap.get(courseId) == null ?
                                        null : studentWiseCoursePercetageMap.get(courseId).get(studentId);
                                if(coursePercentage != null) {
                                    ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(examGradeList, (coursePercentage / 100d));
                                    String obtainedMarksValue = !courseAssignedToStudent ? EMPTY_TEXT :
                                            ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                                    examGrade == null ? null : examGrade.getGradeName(), examAttendanceStatus,
                                                    null, instituteId != 10045,
                                                    instituteId == 10295 ? "" : " (Fail)", null, NO_MARKS_TEXT, false);
                                    finalValue = obtainedMarksValue;
                                }
                            }
                        }
                    }
                    reportCellDetailsRow.add(new ReportCellDetails(finalValue, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                    Double coursePercentage = studentWiseCoursePercetageMap.get(courseId) == null ?
                            null : studentWiseCoursePercetageMap.get(courseId).get(studentId);
                    reportCellDetailsRow.add(new ReportCellDetails(coursePercentage, DOUBLE, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
                }
            }

            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(totalMaxMarks), INTEGER, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(totalObtainedMarks), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            if (requiredHeaders.contains(ReportHeaderAttribute.PERCENTAGE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(percentage), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.GRADE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(grade, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.DIVISION.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(examDivision == null ? "" : examDivision.name(), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.RESULT.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(examResultStatus.getDisplayName(), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.RANK.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(rank, INTEGER, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.ATTENDANCE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(attendance, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.PARENT_REMARKS.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }

            reportCellDetails.add(reportCellDetailsRow);
        }

        if(showClassAverageDetails) {
            reportCellDetailsRow = new ArrayList<>();

            columnNum = 1;
            int summaryStartIndex = index + 3;
            reportCellDetailsRow.add(new ReportCellDetails("Class Avg %", STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
            for (int i = 0; i < courseStartColumn - 1; i++) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                columnNum++;
            }
            mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, 0, courseStartColumn - 1));

            for (Entry<UUID, Course> courseMap : filteredScholasticCourseMap.entrySet()) {
                Course course = courseMap.getValue();
                UUID courseId = course.getCourseId();
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(courseAvgMarksMap.get(courseId)), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                int mergeCount = 0;
                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                    mergeCount++;
                }
                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, columnNum, columnNum + mergeCount - 1));
                columnNum += mergeCount;
            }

            for (Entry<UUID, Course> courseMap : filteredCoScholasticCourseMap.entrySet()) {
                Course course = courseMap.getValue();
                UUID courseId = course.getCourseId();
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(courseAvgMarksMap.get(courseId)), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                int mergeCount = 0;
                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                    mergeCount++;
                }
                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, columnNum, columnNum + mergeCount - 1));
                columnNum += mergeCount;
            }

            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(avgPercentage), STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
            mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, columnNum, columnNum + 1));
            reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            if (requiredHeaders.contains(ReportHeaderAttribute.PERCENTAGE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.GRADE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.DIVISION.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.RESULT.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.RANK.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, INTEGER, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.ATTENDANCE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.PARENT_REMARKS.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }

            reportCellDetails.add(reportCellDetailsRow);
            index++;
        }


        if(showStaffDetails) {
            reportCellDetailsRow = new ArrayList<>();

            columnNum = 1;
            int summaryStartIndex = index + 3;
            reportCellDetailsRow.add(new ReportCellDetails("Subject Teacher", STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
            for (int i = 0; i < courseStartColumn - 1; i++) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                columnNum++;
            }
            mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, 0, courseStartColumn - 1));

            for (Entry<UUID, Course> courseMap : filteredScholasticCourseMap.entrySet()) {
                Course course = courseMap.getValue();
                UUID courseId = course.getCourseId();
                String staffNameString = getStaffNameString(subjectTeacherMap.get(courseId));
                reportCellDetailsRow.add(new ReportCellDetails(staffNameString, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                int mergeCount = 0;
                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                    mergeCount++;
                }
                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                if (scholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, columnNum, columnNum + mergeCount - 1));
                columnNum += mergeCount;
            }

            for (Entry<UUID, Course> courseMap : filteredCoScholasticCourseMap.entrySet()) {
                Course course = courseMap.getValue();
                UUID courseId = course.getCourseId();
                String staffNameString = getStaffNameString(subjectTeacherMap.get(courseId));
                reportCellDetailsRow.add(new ReportCellDetails(staffNameString, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                int mergeCount = 0;
                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.MARKS)) {
                    mergeCount++;
                }
                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.GRADE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                if (coScholasticMarksDisplayTypeSet.contains(MarksDisplayType.PERCENTAGE)) {
                    if (mergeCount != 0) {
                        reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, true));
                    }
                    mergeCount++;
                }
                mergeCellIndexesList.add(new CellIndexes(summaryStartIndex, summaryStartIndex, columnNum, columnNum + mergeCount - 1));
                columnNum += mergeCount;
            }

            reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            if (requiredHeaders.contains(ReportHeaderAttribute.PERCENTAGE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.GRADE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.DIVISION.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.RESULT.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.RANK.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, INTEGER, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.ATTENDANCE.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 2, BLACK_COLOR, WHITE_COLOR, false));
            }
            if (requiredHeaders.contains(ReportHeaderAttribute.PARENT_REMARKS.getKey())) {
                reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }

            reportCellDetails.add(reportCellDetailsRow);
        }

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, false,
                totalColumns, PageSize.A2.rotate(), headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);
        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private String getStaffNameString(List<Staff> staffList) {
        if(CollectionUtils.isEmpty(staffList)) {
            return null;
        }
        Set<String> staffNameSet = new HashSet<>();
        for(Staff staff : staffList) {
            if (staff == null) {
                continue;
            }
            String staffName = staff.getStaffBasicInfo().getName();
            if(!staffNameSet.contains(staffName)) {
                staffNameSet.add(staffName);
            }
        }
        return String.join( ",\n", staffNameSet);
    }

    private ReportDetails generateResultSummaryRankReport(int instituteId, int academicSessionId, UUID standardId,
                                                          Set<Integer> sectionIdSet, Set<UUID> examIdSet, Set<UUID> courseIdSet) {
        final String sheetName = "ResultSummaryReport";
        final String reportName = "Result Summary Report";

        Institute institute = instituteManager.getInstitute(instituteId);
        final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(instituteId,
                academicSessionId, standardId);
//        List<Course> courseList = courseManager.getClassCoursesByStandardId(instituteId, standardId, academicSessionId);

        if(CollectionUtils.isEmpty(examCoursesDataList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        boolean isCoScholasticGrade = standardMetaData.isCoScholasticGradingEnabled();
        boolean isScholasticGrade = standardMetaData.isScholasticGradingEnabled();

        StringBuilder academicSessionDisplayName = new StringBuilder();
        StringBuilder standardName = new StringBuilder();
        StringBuilder examNames = new StringBuilder();
        StringBuilder courseNames = new StringBuilder();
        Map<Integer, String> sectionNameMap = new HashMap<>();

        List<Course> courseList = new ArrayList<>();
        List<UUID> courseUUIDList = new ArrayList<>();
        for(ExamCoursesData examCoursesData : examCoursesDataList) {
            UUID examId = examCoursesData.getExamMetaData().getExamId();
            if(!examIdSet.contains(examId)) {
                continue;
            }
            String examName = examCoursesData.getExamMetaData().getExamName();
            examNames.append(StringUtils.isBlank(examNames) ? examName : ", " + examName);

            for(ExamCourse examCourse : examCoursesData.getExamCourses()) {
                Course course = examCourse.getCourse();
                UUID courseId = course.getCourseId();
                if(!courseUUIDList.contains(courseId)) {
                    courseList.add(course);
                    courseUUIDList.add(courseId);
                }
            }

        }

        if(CollectionUtils.isEmpty(courseList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        boolean containsScholasticSubjects = false;
        boolean containsCoScholasticSubjects = false;
        LinkedHashMap<UUID, Course> filteredCourseMap = new LinkedHashMap<>();
        for(Course course : courseList) {
            if(course == null || course.getCourseId() == null) {
                continue;
            }
            if(!CollectionUtils.isEmpty(courseIdSet) && !courseIdSet.contains(course.getCourseId())) {
                continue;
            }
            filteredCourseMap.put(course.getCourseId(), course);
            courseNames.append(CollectionUtils.isEmpty(courseIdSet) ? "" : StringUtils.isBlank(courseNames) ? course.getCourseName() : ", " + course.getCourseName());
            if(course.getCourseType() == CourseType.SCHOLASTIC) {
                containsScholasticSubjects = true;
            }
            if(course.getCourseType() == CourseType.COSCHOLASTIC) {
                containsCoScholasticSubjects = true;
            }
        }

        Map<UUID, Boolean> examScholasticContainMap = new HashMap<>();
        Map<UUID, Boolean> examCoScholasticContainMap = new HashMap<>();
        ExamMarksUtils.getCoursesContainMapDetails(examCoursesDataList, examScholasticContainMap, examCoScholasticContainMap);

        String reportCardType = "REPORT";
        String examResultCalculatorId = examResultCalculatorFactory.getExamResultCalculatorId(instituteId, standardId);
        ExamReportCardConfiguration examReportCardConfiguration = ExamReportCardConfigurationUtils
                .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, examIdSet, reportCardType,
                        containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap,
                        null, true, true, examResultCalculatorId, null, null,
                        isCoScholasticGrade, isScholasticGrade, null);

        List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                academicSessionId, standardId, sectionIdSet, examReportCardConfiguration.getExamReportStructure(),
                reportCardType, true);

        boolean includeCoscholasticCourses = true;
        IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
        ExamResultAnalysisDetails examResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(classExamReportData,
                academicSessionDisplayName, standardName, sectionNameMap, sectionIdSet, null, includeCoscholasticCourses, null, isScholasticGrade, isCoScholasticGrade, rankCalculator);

        StringBuilder sectionNames = new StringBuilder();
        if(!CollectionUtils.isEmpty(sectionNameMap.entrySet())) {
            sectionNames.append(String.join(",", sectionNameMap.values()));
        }

        Map<UUID, Integer> courseWiseTotalStudentCount = examResultAnalysisDetails.getCourseWiseTotalStudentCount();
        Map<UUID, Integer> courseWisePassStudentCount = examResultAnalysisDetails.getCourseWisePassStudentCount();
        Map<UUID, Integer> courseWiseFailStudentCount = examResultAnalysisDetails.getCourseWiseFailStudentCount();
        Map<UUID, Integer> courseWiseDistinctStudentCount = examResultAnalysisDetails.getCourseWiseDistinctStudentCount();
        Map<UUID, Map<ExamDivision, Integer>> courseWiseDivisionStudentCount = examResultAnalysisDetails.getCourseWiseDivisionStudentCount();
        Map<UUID, Double> courseAvgMarksMap = examResultAnalysisDetails.getCourseAvgMarksMap();
        Map<UUID, Double> courseHighMarksMap = examResultAnalysisDetails.getCourseHighMarksMap();
        double avgPercentage = examResultAnalysisDetails.getAvgPercentage();
        double highPercentage = examResultAnalysisDetails.getHighPercentage();


        int totalStudentCount = examResultAnalysisDetails.getTotalStudentCount();
        int passStudentCount = examResultAnalysisDetails.getPassStudentCount();
        Double passPercentageCount = totalStudentCount == 0 ? 0 : ((double)passStudentCount / totalStudentCount) * 100d;
        int failStudentCount = examResultAnalysisDetails.getFailStudentCount();
        int distinctionStudentCount = examResultAnalysisDetails.getDistinctionStudentCount();
        Map<ExamDivision, Integer> divisionStudentCount = examResultAnalysisDetails.getDivisionStudentCount();

        int totalColumns = 11;
        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        String heading = "";
        if(!StringUtils.isBlank(academicSessionDisplayName)) {
            heading += "Session : " + academicSessionDisplayName;
        }

        if(!StringUtils.isBlank(standardName)) {
            heading+= " | Class : " + standardName;
        }
        if(!StringUtils.isBlank(sectionNames)) {
            heading+= " | Section(s) : " + sectionNames;
        }
        if(!StringUtils.isBlank(examNames)) {
            heading+= " | Exam(s) : " + examNames;
        }
        if(!StringUtils.isBlank(courseNames)) {
            heading+= " | Course(s) : " + courseNames;
        }

        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        headerReportCellDetails.add(reportCellDetailsRow);
        headerMergeCellIndexesList.add(new CellIndexes(0, 0, 0, totalColumns - 1));

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails("Subject", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerMergeCellIndexesList.add(new CellIndexes(1, 2, 0, 0));
        reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerMergeCellIndexesList.add(new CellIndexes(1, 1, 1, 3));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("Division", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerMergeCellIndexesList.add(new CellIndexes(1, 1, 6, 8));
        reportCellDetailsRow.add(new ReportCellDetails("Class", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerMergeCellIndexesList.add(new CellIndexes(1, 1, 9, 10));
        headerReportCellDetails.add(reportCellDetailsRow);

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        for(int i = 0; i < RESULT_SUMMARY_REPORT_HEADER_2.length; i++) {
            reportCellDetailsRow.add(new ReportCellDetails(RESULT_SUMMARY_REPORT_HEADER_2[i], STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        headerReportCellDetails.add(reportCellDetailsRow);


        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        for(Entry<UUID, Course> courseMap : filteredCourseMap.entrySet()) {
            reportCellDetailsRow = new ArrayList<ReportCellDetails>();

            UUID courseId = courseMap.getValue().getCourseId();

            String courseName = courseMap.getValue().getCourseName();
            Integer courseTotalStudentCount = courseWiseTotalStudentCount.get(courseId);
            Integer courseTotalPassStudentCount = courseWisePassStudentCount.get(courseId);
            Integer courseTotalFailStudentCount = courseWiseFailStudentCount.get(courseId);
            Double coursePassPercentageCount = courseTotalStudentCount == null || courseTotalStudentCount == 0
                    ? 0 : ((double) courseTotalPassStudentCount / courseTotalStudentCount) * 100d;

            Integer courseTotalDistinctionStudentCount = courseWiseDistinctStudentCount.get(courseId);
            Integer courseTotalIDivisionStudentCount = courseWiseDivisionStudentCount.get(courseId) == null ? null : courseWiseDivisionStudentCount.get(courseId).get(ExamDivision.I);
            Integer courseTotalIIDivisionStudentCount = courseWiseDivisionStudentCount.get(courseId) == null ? null : courseWiseDivisionStudentCount.get(courseId).get(ExamDivision.II);
            Integer courseTotalIIIDivisionStudentCount = courseWiseDivisionStudentCount.get(courseId) == null ? null : courseWiseDivisionStudentCount.get(courseId).get(ExamDivision.III);

            Double courseAvg = courseAvgMarksMap.get(courseId);
            Double courseHigh = courseHighMarksMap.get(courseId);

            reportCellDetailsRow.add(new ReportCellDetails(courseName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

            reportCellDetailsRow.add(new ReportCellDetails(courseTotalStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(courseTotalPassStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(courseTotalFailStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(coursePassPercentageCount), DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(courseTotalDistinctionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(courseTotalIDivisionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(courseTotalIIDivisionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(courseTotalIIIDivisionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(courseAvg), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(courseHigh), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetails.add(reportCellDetailsRow);

        }

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        Integer totalIDivisionStudentCount = divisionStudentCount.get(ExamDivision.I);
        Integer totalIIDivisionStudentCount = divisionStudentCount.get(ExamDivision.II);
        Integer totalIIIDivisionStudentCount = divisionStudentCount.get(ExamDivision.III);

        reportCellDetailsRow.add(new ReportCellDetails("Class Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        reportCellDetailsRow.add(new ReportCellDetails(totalStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        reportCellDetailsRow.add(new ReportCellDetails(passStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        reportCellDetailsRow.add(new ReportCellDetails(failStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(passPercentageCount), DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        reportCellDetailsRow.add(new ReportCellDetails(distinctionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        reportCellDetailsRow.add(new ReportCellDetails(totalIDivisionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        reportCellDetailsRow.add(new ReportCellDetails(totalIIDivisionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        reportCellDetailsRow.add(new ReportCellDetails(totalIIIDivisionStudentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(avgPercentage), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
        reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(highPercentage), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

        reportCellDetails.add(reportCellDetailsRow);

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList, reportCellDetails);

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, false,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);
        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private ReportDetails generateOverallRankReport(int instituteId, int academicSessionId, UUID standardId,
            Set<Integer> sectionIdSet, Set<UUID> examIdSet, Set<UUID> compareCummulativeWithExamIdSet, Set<UUID> additionalCoursesSet, Integer rankTill) {
        final String sheetName = "OverallRankReport";
        final String reportName = "Overall Rank Analysis";

        boolean fetchPastCummulativeMarks = !CollectionUtils.isEmpty(compareCummulativeWithExamIdSet);

        Institute institute = instituteManager.getInstitute(instituteId);

        final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(instituteId,
                academicSessionId, standardId);
        List<Course> courseList = courseManager.getClassCoursesByStandardId(instituteId, standardId, academicSessionId);

        if(CollectionUtils.isEmpty(courseList) || CollectionUtils.isEmpty(examCoursesDataList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        boolean isCoScholasticGrade = standardMetaData.isCoScholasticGradingEnabled();
        boolean isScholasticGrade = standardMetaData.isScholasticGradingEnabled();

        StringBuilder academicSessionDisplayName = new StringBuilder();
        StringBuilder standardName = new StringBuilder();
        StringBuilder examNames = new StringBuilder();
        Map<Integer, String> sectionNameMap = new HashMap<>();

        for(ExamCoursesData examCoursesData : examCoursesDataList) {
            UUID examId = examCoursesData.getExamMetaData().getExamId();
            if(!examIdSet.contains(examId)) {
                continue;
            }
            String examName = examCoursesData.getExamMetaData().getExamName();
            examNames.append(StringUtils.isBlank(examNames) ? examName : ", " + examName);
        }

        boolean containsScholasticSubjects = false;
        boolean containsCoScholasticSubjects = false;
        Map<UUID, Course> filteredCourseMap = new HashMap<>();
        for(Course course : courseList) {
            if(course == null || course.getCourseId() == null) {
                continue;
            }
            filteredCourseMap.put(course.getCourseId(), course);
            if(course.getCourseType() == CourseType.SCHOLASTIC) {
                containsScholasticSubjects = true;
            }
            if(course.getCourseType() == CourseType.COSCHOLASTIC) {
                containsCoScholasticSubjects = true;
            }
        }

        Map<UUID, Boolean> examScholasticContainMap = new HashMap<>();
        Map<UUID, Boolean> examCoScholasticContainMap = new HashMap<>();
        ExamMarksUtils.getCoursesContainMapDetails(examCoursesDataList, examScholasticContainMap, examCoScholasticContainMap);

        String reportCardType = "REPORT";
        String examResultCalculatorId = examResultCalculatorFactory.getExamResultCalculatorId(instituteId, standardId);
        ExamReportCardConfiguration examReportCardConfiguration = ExamReportCardConfigurationUtils
                .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, examIdSet, reportCardType,
                containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap, null, true, true,
                        examResultCalculatorId, null, null, isCoScholasticGrade, isScholasticGrade, additionalCoursesSet);

        List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                academicSessionId, standardId, sectionIdSet, examReportCardConfiguration.getExamReportStructure(),
                reportCardType, true);

        boolean includeCoscholasticCourses = false;
        IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
        ExamResultAnalysisDetails examResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(classExamReportData,
                academicSessionDisplayName, standardName, sectionNameMap, sectionIdSet, null, includeCoscholasticCourses, additionalCoursesSet, isScholasticGrade, isCoScholasticGrade, rankCalculator);

        StringBuilder sectionNames = new StringBuilder();
        if(!CollectionUtils.isEmpty(sectionNameMap.entrySet())) {
            sectionNames.append(String.join(",", sectionNameMap.values()));
        }

        Map<UUID, StudentLite> studentLiteMap = examResultAnalysisDetails.getStudentLiteMap();
        Map<UUID, Double> studentObtainedMarksMap = examResultAnalysisDetails.getStudentObtainedMarksMap();
        Map<UUID, Double> studentMaxMarksMap = examResultAnalysisDetails.getStudentMaxMarksMap();
        Map<UUID, Double> studentPercentageMap = examResultAnalysisDetails.getStudentPercentageMap();
        Map<UUID, Integer> studentRankMap = examResultAnalysisDetails.getStudentRankMap();

        Map<UUID, Double> pastStudentObtainedMarksMap = new HashMap<>();
        Map<UUID, Double> pastStudentMaxMarksMap = new HashMap<>();
        Map<UUID, Double> pastStudentPercentageMap = new HashMap<>();
        Map<UUID, Integer> pastStudentRankMap = new HashMap<>();

        //TODO:improve the way as currently we are fetching examReportData twice, one for examIds and other for past cumulative gain exam Ids
        if(fetchPastCummulativeMarks) {

            ExamReportCardConfiguration pastExamReportCardConfiguration = ExamReportCardConfigurationUtils
                    .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, compareCummulativeWithExamIdSet, reportCardType,
                            containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap,
                            null, true, true, examResultCalculatorId,
                            null, null, isCoScholasticGrade, isScholasticGrade, additionalCoursesSet);

            List<ExamReportData> pastClassExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                    academicSessionId, standardId, sectionIdSet, pastExamReportCardConfiguration.getExamReportStructure(),
                    reportCardType, true);
            ExamResultAnalysisDetails pastExamResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(pastClassExamReportData,
                    academicSessionDisplayName, standardName, sectionNameMap, sectionIdSet, null, includeCoscholasticCourses, additionalCoursesSet, isScholasticGrade, isCoScholasticGrade, rankCalculator);

            pastStudentObtainedMarksMap = pastExamResultAnalysisDetails.getStudentObtainedMarksMap();
            pastStudentMaxMarksMap = pastExamResultAnalysisDetails.getStudentMaxMarksMap();
            pastStudentPercentageMap = pastExamResultAnalysisDetails.getStudentPercentageMap();
            pastStudentRankMap = pastExamResultAnalysisDetails.getStudentRankMap();

        }

        int totalColumns = !fetchPastCummulativeMarks ? 8 : 13;

        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        String heading = "";
        if(!StringUtils.isBlank(academicSessionDisplayName)) {
            heading += "Session : " + academicSessionDisplayName;
        }

        if(!StringUtils.isBlank(standardName)) {
            heading+= " | Class : " + standardName;
        }
        if(!StringUtils.isBlank(sectionNames)) {
            heading+= " | Section(s) : " + sectionNames;
        }
        if(!StringUtils.isBlank(examNames)) {
            heading+= " | Exam(s) : " + examNames;
        }

        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        headerReportCellDetails.add(reportCellDetailsRow);
        headerMergeCellIndexesList.add(new CellIndexes(0, 0, 0, totalColumns - 1));

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        List<ReportCellDetails> reportCellDetailsRow2 = new ArrayList<ReportCellDetails>();
        for(int i = 0; i < OVERALL_RANK_REPORT.length; i++) {
            reportCellDetailsRow.add(new ReportCellDetails(OVERALL_RANK_REPORT[i], STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            headerMergeCellIndexesList.add(new CellIndexes(1, 2, i, i));
        }

        reportCellDetailsRow.add(new ReportCellDetails("Current Marks", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("Rank", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("MO", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("MM", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerMergeCellIndexesList.add(new CellIndexes(1, 1, 4, 7));


        if(fetchPastCummulativeMarks) {

            reportCellDetailsRow.add(new ReportCellDetails("Past Cumulative Marks", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("Rank", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("MO", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("MM", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            headerMergeCellIndexesList.add(new CellIndexes(1, 1, 8, 11));

            reportCellDetailsRow.add(new ReportCellDetails("Gain(%)", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            headerMergeCellIndexesList.add(new CellIndexes(1, 2, 12, 12));

        }

        headerReportCellDetails.add(reportCellDetailsRow);
        headerReportCellDetails.add(reportCellDetailsRow2);

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        studentRankMap = EMapUtils.sortMapByValue(studentRankMap, false);
        for(Entry<UUID, Integer> studentRankEntry : studentRankMap.entrySet()) {
            reportCellDetailsRow = new ArrayList<ReportCellDetails>();
            UUID studentId = studentRankEntry.getKey();
            Integer rank = studentRankEntry.getValue();
            if(rank == null || rank > rankTill) {
                continue;
            }
            StudentLite studentLite = studentLiteMap.get(studentId);
            String sectionName = studentLite.getStudentSessionData().getStandardSection() == null ?
                    "" : studentLite.getStudentSessionData().getStandardSection().getSectionName();
            String rollNumber = studentLite.getStudentSessionData().getRollNumber();
            String studentName = studentLite.getName();
            String admissionNumber = studentLite.getAdmissionNumber();

            reportCellDetailsRow.add(new ReportCellDetails(sectionName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(rollNumber, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(studentName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(admissionNumber, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            Double obtainedMarks = studentObtainedMarksMap.get(studentId);
            Double maxMarks = studentMaxMarksMap.get(studentId);
            Double percentage = studentPercentageMap.get(studentId);

            reportCellDetailsRow.add(new ReportCellDetails(rank, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(obtainedMarks == null ? "" : obtainedMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(maxMarks == null ? "" : maxMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            reportCellDetailsRow.add(new ReportCellDetails(percentage == null ? "" : NumberUtils.formatDouble(percentage), DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));


            if(fetchPastCummulativeMarks) {

                Integer pastRank = pastStudentRankMap.get(studentId);
                Double pastObtainedMarks = pastStudentObtainedMarksMap.get(studentId);
                Double pastMaxMarks = pastStudentMaxMarksMap.get(studentId);
                Double pastPercentage = pastStudentPercentageMap.get(studentId);

                Double gainPercentage = NumberUtils.subtractValues(percentage, pastPercentage);

                reportCellDetailsRow.add(new ReportCellDetails(pastRank, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(pastObtainedMarks == null ? "" : pastObtainedMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(pastMaxMarks == null ? "" : pastMaxMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(pastPercentage == null ? "" : NumberUtils.formatDouble(pastPercentage), DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                reportCellDetailsRow.add(new ReportCellDetails(gainPercentage == null ? "" : NumberUtils.formatDouble(gainPercentage) + "%", DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            }

            reportCellDetails.add(reportCellDetailsRow);

        }

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, PageSize.A4.rotate(),
//                mergeCellIndexesList, reportCellDetails);

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, false,
                totalColumns, PageSize.A4.rotate(), headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);

        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    private ReportDetails generateSubjectWiseRankReport(int instituteId, int academicSessionId, UUID standardId,
                                                        Set<Integer> sectionIdSet, Set<UUID> examIdSet, Set<UUID> courseIdSet,
                                                        Set<UUID> compareCummulativeWithExamIdSet, Integer rankTill) {

        final String sheetName = "SubjectWiseRankReport";
        final String reportName = "Subjectwise Rank Analysis";

        boolean fetchPastCummulativeMarks = !CollectionUtils.isEmpty(compareCummulativeWithExamIdSet);

        Institute institute = instituteManager.getInstitute(instituteId);

        final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(instituteId,
                academicSessionId, standardId);
        List<Course> courseList = courseManager.getClassCoursesByStandardId(instituteId, standardId, academicSessionId);

        if(CollectionUtils.isEmpty(courseList) || CollectionUtils.isEmpty(examCoursesDataList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        boolean isCoScholasticGrade = standardMetaData.isCoScholasticGradingEnabled();
        boolean isScholasticGrade = standardMetaData.isScholasticGradingEnabled();

        StringBuilder academicSessionDisplayName = new StringBuilder();
        StringBuilder standardName = new StringBuilder();
        StringBuilder examNames = new StringBuilder();
        StringBuilder courseNames = new StringBuilder();
        Map<Integer, String> sectionNameMap = new HashMap<>();

        for(ExamCoursesData examCoursesData : examCoursesDataList) {
            UUID examId = examCoursesData.getExamMetaData().getExamId();
            if(!examIdSet.contains(examId)) {
                continue;
            }
            String examName = examCoursesData.getExamMetaData().getExamName();
            examNames.append(StringUtils.isBlank(examNames) ? examName : ", " + examName);
        }

        boolean containsScholasticSubjects = false;
        boolean containsCoScholasticSubjects = false;
        LinkedHashMap<UUID, Course> filteredCourseMap = new LinkedHashMap<>();
        for(Course course : courseList) {
            if(course == null || course.getCourseId() == null) {
                continue;
            }
            if(!CollectionUtils.isEmpty(courseIdSet) && !courseIdSet.contains(course.getCourseId())) {
                continue;
            }
            filteredCourseMap.put(course.getCourseId(), course);
            courseNames.append(CollectionUtils.isEmpty(courseIdSet) ? "" : StringUtils.isBlank(courseNames) ? course.getCourseName() : ", " + course.getCourseName());
            if(course.getCourseType() == CourseType.SCHOLASTIC) {
                containsScholasticSubjects = true;
            }
            if(course.getCourseType() == CourseType.COSCHOLASTIC) {
                containsCoScholasticSubjects = true;
            }
        }

        Map<UUID, Boolean> examScholasticContainMap = new HashMap<>();
        Map<UUID, Boolean> examCoScholasticContainMap = new HashMap<>();
        ExamMarksUtils.getCoursesContainMapDetails(examCoursesDataList, examScholasticContainMap, examCoScholasticContainMap);

        String reportCardType = "REPORT";
        String examResultCalculatorId = examResultCalculatorFactory.getExamResultCalculatorId(instituteId, standardId);
        ExamReportCardConfiguration examReportCardConfiguration = ExamReportCardConfigurationUtils
                .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, examIdSet, reportCardType,
                        containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap,
                        null, true, true, examResultCalculatorId,
                        null, null, isCoScholasticGrade, isScholasticGrade, null);

        List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                academicSessionId, standardId, sectionIdSet, examReportCardConfiguration.getExamReportStructure(),
                reportCardType, true);

        boolean includeCoscholasticCourses = true;
        IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
        ExamResultAnalysisDetails examResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(classExamReportData,
                academicSessionDisplayName, standardName, sectionNameMap, sectionIdSet, courseIdSet, includeCoscholasticCourses, null, isScholasticGrade, isCoScholasticGrade, rankCalculator);

        StringBuilder sectionNames = new StringBuilder();
        if(!CollectionUtils.isEmpty(sectionNameMap.entrySet())) {
            sectionNames.append(String.join(",", sectionNameMap.values()));
        }

        Map<UUID, StudentLite> studentLiteMap = examResultAnalysisDetails.getStudentLiteMap();
        Map<UUID, Map<UUID, Double>> studentCourseObtainedMarksMap = examResultAnalysisDetails.getStudentCourseObtainedMarksMap();
        Map<UUID, Map<UUID, Double>> studentCourseMaxMarksMap = examResultAnalysisDetails.getStudentCourseMaxMarksMap();
        Map<UUID, Map<UUID, Double>> studentCoursePercentageMap = examResultAnalysisDetails.getStudentCoursePercentageMap();
        Map<UUID, Map<UUID, Integer>> courseStudentRankMap = examResultAnalysisDetails.getCourseStudentRankMap();

        Map<UUID, Map<UUID, Double>> pastStudentCourseObtainedMarksMap = new HashMap<>();
        Map<UUID, Map<UUID, Double>> pastStudentCourseMaxMarksMap = new HashMap<>();
        Map<UUID, Map<UUID, Double>> pastStudentCoursePercentageMap = new HashMap<>();
        Map<UUID, Integer> pastStudentRankMap = new HashMap<>();

        //TODO:improve the way as currently we are fetching examReportData twice, one for examIds and other for past cumulative gain exam Ids
        if(fetchPastCummulativeMarks) {

            ExamReportCardConfiguration pastExamReportCardConfiguration = ExamReportCardConfigurationUtils
                    .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, compareCummulativeWithExamIdSet, reportCardType,
                            containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap,
                            null, true, true, examResultCalculatorId,
                            null, null, isCoScholasticGrade, isScholasticGrade, null);

            List<ExamReportData> pastClassExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                    academicSessionId, standardId, sectionIdSet, pastExamReportCardConfiguration.getExamReportStructure(),
                    reportCardType, true);
            ExamResultAnalysisDetails pastExamResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(pastClassExamReportData,
                    academicSessionDisplayName, standardName, sectionNameMap, sectionIdSet, courseIdSet, includeCoscholasticCourses, null, isScholasticGrade, isCoScholasticGrade, rankCalculator);

            pastStudentCourseObtainedMarksMap = pastExamResultAnalysisDetails.getStudentCourseObtainedMarksMap();
            pastStudentCourseMaxMarksMap = pastExamResultAnalysisDetails.getStudentCourseMaxMarksMap();
            pastStudentCoursePercentageMap = pastExamResultAnalysisDetails.getStudentCoursePercentageMap();
            pastStudentRankMap = pastExamResultAnalysisDetails.getStudentRankMap();

        }

        int totalColumns = !fetchPastCummulativeMarks ? 9 : 14;

        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

        String heading = "";
        if(!StringUtils.isBlank(academicSessionDisplayName)) {
            heading += "Session : " + academicSessionDisplayName;
        }

        if(!StringUtils.isBlank(standardName)) {
            heading+= " | Class : " + standardName;
        }
        if(!StringUtils.isBlank(sectionNames)) {
            heading+= " | Section(s) : " + sectionNames;
        }
        if(!StringUtils.isBlank(examNames)) {
            heading+= " | Exam(s) : " + examNames;
        }
        if(!StringUtils.isBlank(courseNames)) {
            heading+= " | Course(s) : " + courseNames;
        }

        List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        reportCellDetailsRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
        headerReportCellDetails.add(reportCellDetailsRow);

        headerMergeCellIndexesList.add(new CellIndexes(0, 0, 0, totalColumns - 1));

        reportCellDetailsRow = new ArrayList<ReportCellDetails>();
        List<ReportCellDetails> reportCellDetailsRow2 = new ArrayList<ReportCellDetails>();
        for(int i = 0; i < SUBJECT_WISE_RANK_REPORT.length; i++) {
            reportCellDetailsRow.add(new ReportCellDetails(SUBJECT_WISE_RANK_REPORT[i], STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            headerMergeCellIndexesList.add(new CellIndexes(1, 2, i, i));
        }

        reportCellDetailsRow.add(new ReportCellDetails("Current Marks", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("Rank", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("MO", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("MM", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsRow2.add(new ReportCellDetails("%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerMergeCellIndexesList.add(new CellIndexes(1, 1, 5, 8));


        if(fetchPastCummulativeMarks) {

            reportCellDetailsRow.add(new ReportCellDetails("Past Cumulative Marks", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("Rank", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("MO", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("MM", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow2.add(new ReportCellDetails("%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            headerMergeCellIndexesList.add(new CellIndexes(1, 1, 9, 12));

            reportCellDetailsRow.add(new ReportCellDetails("Gain(%)", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            headerMergeCellIndexesList.add(new CellIndexes(1, 2, 13, 13));

        }

        headerReportCellDetails.add(reportCellDetailsRow);
        headerReportCellDetails.add(reportCellDetailsRow2);

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        int startRow = 3;
        int currentRow = 3;
        for(Entry<UUID, Course> courseMap : filteredCourseMap.entrySet()) {
            UUID courseId = courseMap.getValue().getCourseId();
            String courseName = courseMap.getValue().getCourseName();
            Map<UUID, Integer> rankMap = courseStudentRankMap.get(courseId);
            if(rankMap == null || CollectionUtils.isEmpty(rankMap.entrySet())) {
                continue;
            }
            rankMap = EMapUtils.sortMapByValue(rankMap, false);
            startRow = currentRow;
            for(Entry<UUID, Integer> studentRankEntry : rankMap.entrySet()) {
                reportCellDetailsRow = new ArrayList<ReportCellDetails>();
                UUID studentId = studentRankEntry.getKey();
                Integer rank = studentRankEntry.getValue();
                if(rank == null || rank > rankTill) {
                    continue;
                }

                StudentLite studentLite = studentLiteMap.get(studentId);
                String sectionName = studentLite.getStudentSessionData().getStandardSection() == null ?
                        "" : studentLite.getStudentSessionData().getStandardSection().getSectionName();
                String rollNumber = studentLite.getStudentSessionData().getRollNumber();
                String studentName = studentLite.getName();
                String admissionNumber = studentLite.getAdmissionNumber();

                reportCellDetailsRow.add(new ReportCellDetails(courseName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(sectionName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(rollNumber, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(studentName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(admissionNumber, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                Double obtainedMarks = studentCourseObtainedMarksMap.get(courseId).get(studentId);
                Double maxMarks = studentCourseMaxMarksMap.get(courseId).get(studentId);
                Double percentage = studentCoursePercentageMap.get(courseId).get(studentId);

                reportCellDetailsRow.add(new ReportCellDetails(rank, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(obtainedMarks == null ? "" : obtainedMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(maxMarks == null ? "" : maxMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(percentage == null ? "" : NumberUtils.formatDouble(percentage), DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                if(fetchPastCummulativeMarks) {

                    Integer pastRank = pastStudentRankMap.get(studentId);
                    Double pastObtainedMarks = pastStudentCourseObtainedMarksMap.get(courseId) == null ? null : pastStudentCourseObtainedMarksMap.get(courseId).get(studentId);
                    Double pastMaxMarks = pastStudentCourseMaxMarksMap.get(courseId) == null ? null : pastStudentCourseMaxMarksMap.get(courseId).get(studentId);
                    Double pastPercentage = pastStudentCoursePercentageMap.get(courseId) == null ? null : pastStudentCoursePercentageMap.get(courseId).get(studentId);
                    Double gainPercentage = NumberUtils.subtractValues(percentage, pastPercentage);

                    reportCellDetailsRow.add(new ReportCellDetails(pastRank, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(pastObtainedMarks == null ? "" : pastObtainedMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(pastMaxMarks == null ? "" : pastMaxMarks, DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(pastPercentage == null ? "" : NumberUtils.formatDouble(pastPercentage), DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                    reportCellDetailsRow.add(new ReportCellDetails(gainPercentage == null ? "" : NumberUtils.formatDouble(gainPercentage) + "%", DOUBLE, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                }

                reportCellDetails.add(reportCellDetailsRow);
                currentRow++;

            }

            if(startRow < currentRow - 1) {
                mergeCellIndexesList.add(new CellIndexes(startRow, currentRow - 1, 0, 0));
            }
        }

        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, PageSize.A4.rotate(),
//                mergeCellIndexesList, reportCellDetails);

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, false,
                totalColumns, PageSize.A4.rotate(), headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
                true, institute, totalColumns);

        reportSheetDetailsList.add(reportSheetDetails);
        return new ReportDetails(reportName, reportSheetDetailsList);

    }

    /**
     * Creates comprehensive maps for exam, course, dimension data including max marks and min marks
     *
     * @param instituteId The institute ID
     * @param academicSessionId The academic session ID
     * @param standardId The standard ID
     * @return ExamCourseMarksData containing all the required maps
     */
    public ExamCourseMarksData getExamCourseMarksData(int instituteId, int academicSessionId, UUID standardId, UUID selectedExamId) {
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap = new HashMap<>();
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap = new HashMap<>();
        Map<UUID, ExamMetaData> examMetaDataMap = new HashMap<>();
        Map<UUID, Course> courseMap = new HashMap<>();
        Map<Integer, ExamDimension> examDimensionMap = new HashMap<>();

        try {
            if(selectedExamId == null) {
                ExamMetaData  examMetaData = examinationManager.getSystemExamMetadata(instituteId, academicSessionId, standardId);
                selectedExamId = examMetaData.getExamId();
            }
            // Get the exam structure for the standard
            ExamNode defaultExamStructureNode = getExamNode(instituteId, academicSessionId, standardId, selectedExamId);
            if (defaultExamStructureNode == null) {
                logger.error("No exam structure found for institute {}, session {}, standard {}",
                        instituteId, academicSessionId, standardId);
                return new ExamCourseMarksData(examCourseMaxMarksMap, examCourseMinMarksMap, examMetaDataMap, courseMap, examDimensionMap);
            }

            // Build a map of all exams in the forest
            Map<UUID, ExamNode> examForestMap = new HashMap<>();
            getExamForestMap(defaultExamStructureNode, examForestMap);

            // Get exam courses data
            final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(
                    instituteId, academicSessionId, standardId);

            if (CollectionUtils.isEmpty(examCoursesDataList)) {
                return new ExamCourseMarksData(examCourseMaxMarksMap, examCourseMinMarksMap, examMetaDataMap, courseMap, examDimensionMap);
            }

            StandardMetadata standardMetadata = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
            boolean isScholasticGradingEnabled = standardMetadata.isScholasticGradingEnabled();
            boolean isCoScholasticGradingEnabled = standardMetadata.isCoScholasticGradingEnabled();

            // Process each exam course data
            for (ExamCoursesData examCoursesData : examCoursesDataList) {
                ExamMetaData examMetaData = examCoursesData.getExamMetaData();
                if (examMetaData == null || examMetaData.getExamId() == null) {
                    continue;
                }

                UUID examId = examMetaData.getExamId();

                // Add exam metadata to map
                examMetaDataMap.put(examId, examMetaData);

                List<ExamCourse> examCourseList = examCoursesData.getExamCourses();

                if (CollectionUtils.isEmpty(examCourseList)) {
                    continue;
                }

                // Initialize the maps for this exam if not already present
                if (!examCourseMaxMarksMap.containsKey(examId)) {
                    examCourseMaxMarksMap.put(examId, new HashMap<>());
                }
                if (!examCourseMinMarksMap.containsKey(examId)) {
                    examCourseMinMarksMap.put(examId, new HashMap<>());
                }

                // Process each course in the exam
                for (ExamCourse examCourse : examCourseList) {
                    Course course = examCourse.getCourse();
                    if (course == null) {
                        continue;
                    }

                    UUID courseId = course.getCourseId();
                    CourseType courseType = course.getCourseType();

                    // Add course to map
                    courseMap.put(courseId, course);

                    List<ExamDimensionValues> examDimensionValuesList = examCourse.getExamDimensionValues();

                    if (CollectionUtils.isEmpty(examDimensionValuesList)) {
                        continue;
                    }

                    // Initialize the maps for this course if not already present
                    if (!examCourseMaxMarksMap.get(examId).containsKey(courseId)) {
                        examCourseMaxMarksMap.get(examId).put(courseId, new HashMap<>());
                    }
                    if (!examCourseMinMarksMap.get(examId).containsKey(courseId)) {
                        examCourseMinMarksMap.get(examId).put(courseId, new HashMap<>());
                    }

                    // Process each dimension for the course
                    for (ExamDimensionValues examDimensionValues : examDimensionValuesList) {
                        if (examDimensionValues == null || examDimensionValues.getExamDimension() == null) {
                            continue;
                        }

                        if(courseType == CourseType.SCHOLASTIC && !isScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                            continue;
                        }
                        if(courseType == CourseType.SCHOLASTIC && isScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                            continue;
                        }
                        if(courseType == CourseType.COSCHOLASTIC && !isCoScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                            continue;
                        }
                        if(courseType == CourseType.COSCHOLASTIC && isCoScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                            continue;
                        }

                        ExamDimension examDimension = examDimensionValues.getExamDimension();
                        Integer dimensionId = examDimension.getDimensionId();
                        Double maxMarks = examDimensionValues.getMaxMarks();
                        Double minMarks = examDimensionValues.getMinMarks();

                        // Add exam dimension to map
                        examDimensionMap.put(dimensionId, examDimension);

                        // Add the dimension max marks and min marks to the maps
                        examCourseMaxMarksMap.get(examId).get(courseId).put(dimensionId, maxMarks == null ? "-" : maxMarks.toString());
                        examCourseMinMarksMap.get(examId).get(courseId).put(dimensionId, minMarks == null ? "-" : minMarks.toString());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error while creating exam course marks data for institute {}, session {}, standard {}",
                    instituteId, academicSessionId, standardId, e);
        }

        return new ExamCourseMarksData(examCourseMaxMarksMap, examCourseMinMarksMap, examMetaDataMap, courseMap, examDimensionMap);
    }

    /**
     * Creates a report structure with configurable orientation
     *
     * @deprecated This method has been moved to ExaminationDocuments class for better separation of concerns.
     * Use ExaminationDocuments.generateExamCourseMarksReport() instead.
     *
     * @param instituteId The institute ID
     * @param academicSessionId The academic session ID
     * @param standardId The standard ID
     * @param selectedExamId The selected exam ID (can be null for all exams)
     * @param coursesInColumns If true, courses are in columns; if false, courses are in rows
     * @return ReportDetails containing the exam course marks report
     */
    @Deprecated
    public ReportDetails generateExamCourseMarksReport(int instituteId, int academicSessionId, UUID standardId, UUID selectedExamId, boolean coursesInColumns) {
        throw new UnsupportedOperationException(
            "This method has been deprecated and moved to ExaminationDocuments class. " +
            "Please use ExaminationDocuments.generateExamCourseMarksReport() instead."
        );
    }
}







enum GreensheetGrade {
    A, B, C, D, E;

    public static GreensheetGrade getGreensheetGradeByMarks(Double marks) {
        if (marks == null) {
            return null;
        }
        if (marks >= 85.5 && marks <= 100) {
            return GreensheetGrade.A;
        } else if (marks >= 70.5 && marks < 85.5) {
            return GreensheetGrade.B;
        } else if (marks >= 50.5 && marks < 70.5) {
            return GreensheetGrade.C;
        } else if (marks >= 30.5 && marks < 50.5) {
            return GreensheetGrade.D;
        } else if (marks >= 0 && marks < 30.5) {
            return GreensheetGrade.E;
        }
        return null;
    }
}

class ExamResultGradeDivisionDetails {

    private Map<UUID, Map<UUID, Map<ExamDivision, Integer>>> standardCourseDivisionCountMap;

    private Map<UUID, Map<UUID, Map<String, Integer>>> standardCourseGradeCountMap;

    public ExamResultGradeDivisionDetails(Map<UUID, Map<UUID, Map<ExamDivision, Integer>>> standardCourseDivisionCountMap, Map<UUID, Map<UUID, Map<String, Integer>>> standardCourseGradeCountMap) {
        this.standardCourseDivisionCountMap = standardCourseDivisionCountMap;
        this.standardCourseGradeCountMap = standardCourseGradeCountMap;
    }

    public Map<UUID, Map<UUID, Map<ExamDivision, Integer>>> getStandardCourseDivisionCountMap() {
        return standardCourseDivisionCountMap;
    }

    public void setStandardCourseDivisionCountMap(Map<UUID, Map<UUID, Map<ExamDivision, Integer>>> standardCourseDivisionCountMap) {
        this.standardCourseDivisionCountMap = standardCourseDivisionCountMap;
    }

    public Map<UUID, Map<UUID, Map<String, Integer>>> getStandardCourseGradeCountMap() {
        return standardCourseGradeCountMap;
    }

    public void setStandardCourseGradeCountMap(Map<UUID, Map<UUID, Map<String, Integer>>> standardCourseGradeCountMap) {
        this.standardCourseGradeCountMap = standardCourseGradeCountMap;
    }

    @Override
    public String toString() {
        return "ExamResultGradeDivisionDetails{" +
                "standardCourseDivisionCountMap=" + standardCourseDivisionCountMap +
                ", standardCourseGradeCountMap=" + standardCourseGradeCountMap +
                '}';
    }
}