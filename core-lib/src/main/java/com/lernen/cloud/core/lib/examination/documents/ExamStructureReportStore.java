package com.lernen.cloud.core.lib.examination.documents;

import java.util.Map;
import java.util.UUID;

import com.embrate.cloud.core.api.report.util.IReportStore;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamMetaData;

/**
 * Store class for exam structure report data
 * Contains all the processed data needed for report generation
 */
public class ExamStructureReportStore implements IReportStore {
    
    private final Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap;
    private final Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap;
    private final Map<UUID, ExamMetaData> examMetaDataMap;
    private final Map<UUID, Course> courseMap;
    private final Map<Integer, ExamDimension> examDimensionMap;
    private final boolean coursesInColumns;
    private final String reportName;
    
    public ExamStructureReportStore(Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap,
                                   Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap,
                                   Map<UUID, ExamMetaData> examMetaDataMap,
                                   Map<UUID, Course> courseMap,
                                   Map<Integer, ExamDimension> examDimensionMap,
                                   boolean coursesInColumns,
                                   String reportName) {
        this.examCourseMaxMarksMap = examCourseMaxMarksMap;
        this.examCourseMinMarksMap = examCourseMinMarksMap;
        this.examMetaDataMap = examMetaDataMap;
        this.courseMap = courseMap;
        this.examDimensionMap = examDimensionMap;
        this.coursesInColumns = coursesInColumns;
        this.reportName = reportName;
    }
    
    public Map<UUID, Map<UUID, Map<Integer, String>>> getExamCourseMaxMarksMap() {
        return examCourseMaxMarksMap;
    }
    
    public Map<UUID, Map<UUID, Map<Integer, String>>> getExamCourseMinMarksMap() {
        return examCourseMinMarksMap;
    }
    
    public Map<UUID, ExamMetaData> getExamMetaDataMap() {
        return examMetaDataMap;
    }
    
    public Map<UUID, Course> getCourseMap() {
        return courseMap;
    }
    
    public Map<Integer, ExamDimension> getExamDimensionMap() {
        return examDimensionMap;
    }
    
    public boolean isCoursesInColumns() {
        return coursesInColumns;
    }
    
    public String getReportName() {
        return reportName;
    }
    
    @Override
    public String toString() {
        return "ExamStructureReportStore{" +
                "examCourseMaxMarksMap=" + examCourseMaxMarksMap +
                ", examCourseMinMarksMap=" + examCourseMinMarksMap +
                ", examMetaDataMap=" + examMetaDataMap +
                ", courseMap=" + courseMap +
                ", examDimensionMap=" + examDimensionMap +
                ", coursesInColumns=" + coursesInColumns +
                ", reportName='" + reportName + '\'' +
                '}';
    }
}
