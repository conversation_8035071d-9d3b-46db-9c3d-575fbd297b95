package com.lernen.cloud.core.lib.examination.documents;

import java.util.UUID;

/**
 * Helper class to represent a column in the report that combines course and dimension information
 */
public class CourseDimensionColumn {
    private final UUID courseId;
    private final String courseName;
    private final Integer dimensionId;
    private final String dimensionName;

    public CourseDimensionColumn(UUID courseId, String courseName, Integer dimensionId, String dimensionName) {
        this.courseId = courseId;
        this.courseName = courseName;
        this.dimensionId = dimensionId;
        this.dimensionName = dimensionName;
    }

    public UUID getCourseId() {
        return courseId;
    }

    public String getCourseName() {
        return courseName;
    }

    public Integer getDimensionId() {
        return dimensionId;
    }

    public String getDimensionName() {
        return dimensionName;
    }

    @Override
    public String toString() {
        return "CourseDimensionColumn{" +
                "courseId=" + courseId +
                ", courseName='" + courseName + '\'' +
                ", dimensionId=" + dimensionId +
                ", dimensionName='" + dimensionName + '\'' +
                '}';
    }
}
