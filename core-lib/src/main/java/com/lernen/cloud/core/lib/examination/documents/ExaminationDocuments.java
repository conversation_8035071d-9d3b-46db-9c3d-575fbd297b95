package com.lernen.cloud.core.lib.examination.documents;

import java.util.UUID;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.lib.report.factory.ReportRegistry;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.embrate.cloud.core.api.report.util.IReportPayload;
import com.embrate.cloud.core.api.report.util.IReportStore;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;

/**
 * ExaminationDocuments class for generating examination-related reports
 * Follows the pattern of StudyTrackerReportGenerator
 */
public class ExaminationDocuments extends ReportGenerator {

    private static final Logger logger = LogManager.getLogger(ExaminationDocuments.class);

    private final UserPermissionManager userPermissionManager;
    private final ReportRegistry reportRegistry;

    public ExaminationDocuments(UserPermissionManager userPermissionManager,
                               ReportRegistry reportRegistry) {
        this.userPermissionManager = userPermissionManager;
        this.reportRegistry = reportRegistry;
    }

    /**
     * Generates exam course marks report with configurable orientation
     *
     * @param instituteId The institute ID
     * @param academicSessionId The academic session ID
     * @param standardId The standard ID
     * @param selectedExamId The selected exam ID (can be null for all exams)
     * @param coursesInColumns If true, courses are in columns; if false, courses are in rows
     * @param userId The user ID for authorization
     * @param downloadFormat The download format (PDF/Excel)
     * @return ReportDetails containing the exam course marks report
     */
    public ReportDetails generateExamCourseMarksReport(int instituteId, int academicSessionId,
                                                      UUID standardId, UUID selectedExamId,
                                                      boolean coursesInColumns, UUID userId,
                                                      DownloadFormat downloadFormat) {

        // Validate input parameters
        if (instituteId <= 0 || userId == null || academicSessionId <= 0) {
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }
//
//        // Verify authorization based on download format
//        if (downloadFormat == DownloadFormat.EXCEL) {
//            userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.EXAM_EXCEL_REPORTS);
//        } else if (downloadFormat == DownloadFormat.PDF) {
//            userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.EXAM_PDF_REPORTS);
//        }

        // Get the appropriate report builder
        String reportId = "EXAM_COURSE_MARKS_REPORT";
        ReportBuilder<IReportPayload, IReportStore> reportBuilder =
            reportRegistry.getReportBuilders(Module.EXAMINATION).get(reportId);

        if (reportBuilder == null) {
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                "Exam Course Marks Report Builder not found"));
        }

        // Create payload and build report
        ExamStructureReportPayload payload = new ExamStructureReportPayload(
            instituteId, academicSessionId, standardId, selectedExamId,
            coursesInColumns, userId);

        return reportBuilder.build(payload);
    }

    /**
     * Generates exam structure report for different orientations
     *
     * @param instituteId The institute ID
     * @param academicSessionId The academic session ID
     * @param standardId The standard ID
     * @param selectedExamId The selected exam ID
     * @param coursesInColumns The orientation flag
     * @param userId The user ID
     * @return ReportDetails containing the exam structure report
     */
    private ReportDetails generateExamStructureReport(int instituteId, int academicSessionId,
                                                     UUID standardId, UUID selectedExamId,
                                                     boolean coursesInColumns, UUID userId) {

        String reportId = "EXAM_STRUCTURE_REPORT";
        ReportBuilder<IReportPayload, IReportStore> reportBuilder =
            reportRegistry.getReportBuilders(Module.EXAMINATION).get(reportId);

        return reportBuilder.build(new ExamStructureReportPayload(
            instituteId, academicSessionId, standardId, selectedExamId,
            coursesInColumns, userId));
    }
}
