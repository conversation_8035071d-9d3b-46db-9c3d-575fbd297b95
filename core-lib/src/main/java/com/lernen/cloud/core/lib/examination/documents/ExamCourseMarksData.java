package com.lernen.cloud.core.lib.examination.documents;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamMetaData;

/**
 * Data class to hold comprehensive exam course marks information
 */
public class ExamCourseMarksData {
    private final Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap;
    private final Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap;
    private final Map<UUID, ExamMetaData> examMetaDataMap;
    private final Map<UUID, Course> courseMap;
    private final Map<Integer, ExamDimension> examDimensionMap;

    public ExamCourseMarksData(Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap,
                               Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap,
                               Map<UUID, ExamMetaData> examMetaDataMap,
                               Map<UUID, Course> courseMap,
                               Map<Integer, ExamDimension> examDimensionMap) {
        this.examCourseMaxMarksMap = examCourseMaxMarksMap;
        this.examCourseMinMarksMap = examCourseMinMarksMap;
        this.examMetaDataMap = examMetaDataMap;
        this.courseMap = courseMap;
        this.examDimensionMap = examDimensionMap;
    }

    public Map<UUID, Map<UUID, Map<Integer, String>>> getExamCourseMaxMarksMap() {
        return examCourseMaxMarksMap;
    }

    public Map<UUID, Map<UUID, Map<Integer, String>>> getExamCourseMinMarksMap() {
        return examCourseMinMarksMap;
    }

    public Map<UUID, ExamMetaData> getExamMetaDataMap() {
        return examMetaDataMap;
    }

    public Map<UUID, Course> getCourseMap() {
        return courseMap;
    }

    public Map<Integer, ExamDimension> getExamDimensionMap() {
        return examDimensionMap;
    }

    @Override
    public String toString() {
        return "ExamCourseMarksData{" +
                "examCourseMaxMarksMap=" + examCourseMaxMarksMap +
                ", examCourseMinMarksMap=" + examCourseMinMarksMap +
                ", examMetaDataMap=" + examMetaDataMap +
                ", courseMap=" + courseMap +
                ", examDimensionMap=" + examDimensionMap +
                '}';
    }
}
