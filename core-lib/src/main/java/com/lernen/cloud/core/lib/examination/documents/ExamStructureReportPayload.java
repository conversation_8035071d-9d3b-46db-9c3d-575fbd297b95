package com.lernen.cloud.core.lib.examination.documents;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import com.embrate.cloud.core.api.report.util.IReportPayload;

/**
 * Payload class for exam structure report generation
 * Contains all necessary parameters for generating exam course marks reports
 */
public class ExamStructureReportPayload implements IReportPayload {

    private final int instituteId;
    private final int academicSessionId;
    private final UUID standardId;
    private final UUID selectedExamId;
    private final boolean coursesInColumns;
    private final UUID userId;

    public ExamStructureReportPayload(int instituteId, int academicSessionId,
                                     UUID standardId, UUID selectedExamId,
                                     boolean coursesInColumns, UUID userId) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.standardId = standardId;
        this.selectedExamId = selectedExamId;
        this.coursesInColumns = coursesInColumns;
        this.userId = userId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public UUID getSelectedExamId() {
        return selectedExamId;
    }

    public boolean isCoursesInColumns() {
        return coursesInColumns;
    }

    public UUID getUserId() {
        return userId;
    }

    @Override
    public List<String> getSelectedColumns() {
        return Collections.emptyList(); // No specific column selection for this report
    }

    @Override
    public String toString() {
        return "ExamStructureReportPayload{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", standardId=" + standardId +
                ", selectedExamId=" + selectedExamId +
                ", coursesInColumns=" + coursesInColumns +
                ", userId=" + userId +
                '}';
    }
}
