package com.lernen.cloud.core.lib.examination.documents;

import static com.lernen.cloud.core.lib.reports.ReportGenerator.BLACK_COLOR;
import static com.lernen.cloud.core.lib.reports.ReportGenerator.CONTENT_SIZE;
import static com.lernen.cloud.core.lib.reports.ReportGenerator.STRING;
import static com.lernen.cloud.core.lib.reports.ReportGenerator.WHITE_COLOR;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.embrate.cloud.core.api.report.layout.MergeArea;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamCourse;
import com.lernen.cloud.core.api.examination.ExamCoursesData;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamDimensionValues;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;
import com.lernen.cloud.core.api.examination.ExamMetaData;
import com.lernen.cloud.core.api.examination.ExamNode;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;

/**
 * ExamStructureDocument class for building exam course marks reports
 * Follows the pattern of StudyTrackerDetailsReportBuilder
 */
public class ExamStructureDocument extends ReportBuilder<ExamStructureReportPayload, ExamStructureReportStore> {

    private static final Logger logger = LogManager.getLogger(ExamStructureDocument.class);

    private final ExaminationManager examinationManager;
    private final InstituteManager instituteManager;

    private static final String EMPTY_TEXT = "";
    private static final String REPORT_HEADING = "Exam Course Marks Report";

    public ExamStructureDocument(UserPermissionManager userPermissionManager,
                                InstituteManager instituteManager,
                                ExaminationManager examinationManager) {
        super(userPermissionManager, instituteManager);
        this.examinationManager = examinationManager;
        this.instituteManager = instituteManager;
    }

    @Override
    public ReportMetadata getReportMetadata() {
        return new ReportMetadata("EXAM_COURSE_MARKS_REPORT",
                                 "Exam Course Marks Report",
                                 Module.EXAMINATION,
                                 "Exam Course Marks Report with configurable orientation");
    }

    @Override
    public List<ReportHeaderAttribute> getStaticHeaders() {
        return Arrays.asList(); // No static headers for this report
    }

    @Override
    public ExamStructureReportStore getStore(ExamStructureReportPayload payload) {
        int instituteId = payload.getInstituteId();
        int academicSessionId = payload.getAcademicSessionId();
        UUID standardId = payload.getStandardId();
        UUID selectedExamId = payload.getSelectedExamId();
        boolean coursesInColumns = payload.isCoursesInColumns();

        // Get exam course marks data using the existing logic from ExamReportGenerator
        ExamCourseMarksData examCourseMarksData = getExamCourseMarksData(instituteId, academicSessionId, standardId);

        String reportName = coursesInColumns ? "Exam Course Marks Report (Courses in Columns)"
                                            : "Exam Course Marks Report (Courses in Rows)";

        return new ExamStructureReportStore(
            examCourseMarksData.getExamCourseMaxMarksMap(),
            examCourseMarksData.getExamCourseMinMarksMap(),
            examCourseMarksData.getExamMetaDataMap(),
            examCourseMarksData.getCourseMap(),
            examCourseMarksData.getExamDimensionMap(),
            coursesInColumns,
            reportName
        );
    }

    @Override
    public String getDownloadReportName(ExamStructureReportPayload payload, ExamStructureReportStore store) {
        String orientation = store.isCoursesInColumns() ? "Courses_In_Columns" : "Courses_In_Rows";
        return "Exam_Course_Marks_Report_" + orientation;
    }

    @Override
    public ReportConfigs getConfigs(ExamStructureReportPayload payload, ExamStructureReportStore store) {
        return null;
    }

    @Override
    public ReportHeader getHeader(ExamStructureReportPayload payload, ExamStructureReportStore store) {
        List<ReportRow> rows = new ArrayList<>();
        List<MergeArea> mergeAreas = new ArrayList<>();

        // Add column headers based on orientation first to determine total columns
        if (store.isCoursesInColumns()) {
            generateCoursesInColumnsHeader(rows, mergeAreas, store);
        } else {
            generateCoursesInRowsHeader(rows, mergeAreas, store);
        }

        // Get total columns from the first header row
        int totalColumns = rows.isEmpty() ? 1 : rows.get(0).getColumns().size();

        // Add report title row with proper column count
        List<ReportCellDetails> titleRow = new ArrayList<>();
        titleRow.add(new ReportCellDetails(store.getReportName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

        // Fill remaining columns with empty cells to match total columns
        for (int i = 1; i < totalColumns; i++) {
            titleRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }

        // Insert title row at the beginning
        rows.add(0, new ReportRow(titleRow));

        // Add merge area for title row to span all columns
        if (totalColumns > 1) {
            mergeAreas.add(new MergeArea(0, 0, 0, totalColumns - 1));
        }

        return new ReportHeader(rows, mergeAreas);
    }

    @Override
    public ReportBody getBody(ExamStructureReportPayload payload, ExamStructureReportStore store) {
        List<ReportRow> rows = new ArrayList<>();
        List<MergeArea> mergeAreas = new ArrayList<>();

        if (store.isCoursesInColumns()) {
            generateCoursesInColumnsReport(rows, store);
        } else {
            generateCoursesInRowsReport(rows, store);
        }

        return new ReportBody(rows, mergeAreas);
    }

    @Override
    public void verifyAuthorisation(ExamStructureReportPayload payload) {
//        userPermissionManager.verifyAuthorisation(payload.getInstituteId(), payload.getUserId(),
//            AuthorisationRequiredAction.EXAM_EXCEL_REPORTS);
    }

    @Override
    public void payloadVerification(ExamStructureReportPayload payload) {
        // Add any payload validation logic here if needed
    }

    /**
     * Generates report with courses in columns (original format) - Body only
     */
    private void generateCoursesInColumnsReport(List<ReportRow> rows, ExamStructureReportStore store) {
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap = store.getExamCourseMaxMarksMap();
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap = store.getExamCourseMinMarksMap();
        Map<UUID, ExamMetaData> examMetaDataMap = store.getExamMetaDataMap();
        Map<UUID, Course> courseMap = store.getCourseMap();
        Map<Integer, ExamDimension> examDimensionMap = store.getExamDimensionMap();

        // Build column structure: each column represents a course-dimension combination
        List<CourseDimensionColumn> columns = ExamReportHelper.buildColumnStructure(examCourseMaxMarksMap, courseMap, examDimensionMap);

        // Sort exams by name
        List<ExamMetaData> sortedExams = new ArrayList<>(examMetaDataMap.values());
        Collections.sort(sortedExams, new Comparator<ExamMetaData>() {
            @Override
            public int compare(ExamMetaData e1, ExamMetaData e2) {
                return e1.getExamName().compareToIgnoreCase(e2.getExamName());
            }
        });

        // Data rows: One row per exam
        for (ExamMetaData examMetaData : sortedExams) {
            UUID examId = examMetaData.getExamId();
            List<ReportCellDetails> dataRow = new ArrayList<>();

            // First column: Exam name
            dataRow.add(new ReportCellDetails(examMetaData.getExamName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            // Subsequent columns: Marks for each course-dimension combination
            for (CourseDimensionColumn column : columns) {
                String marksDisplay = ExamReportHelper.getMarksDisplayForExamCourseDimension(
                    examId, column.getCourseId(), column.getDimensionId(),
                    examCourseMaxMarksMap, examCourseMinMarksMap, examDimensionMap);
                dataRow.add(new ReportCellDetails(marksDisplay, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }

            rows.add(new ReportRow(dataRow));
        }
    }

    /**
     * Generates report with courses in rows (new format) - Body only
     */
    private void generateCoursesInRowsReport(List<ReportRow> rows, ExamStructureReportStore store) {
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap = store.getExamCourseMaxMarksMap();
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap = store.getExamCourseMinMarksMap();
        Map<UUID, ExamMetaData> examMetaDataMap = store.getExamMetaDataMap();
        Map<UUID, Course> courseMap = store.getCourseMap();
        Map<Integer, ExamDimension> examDimensionMap = store.getExamDimensionMap();

        // Sort exams by name
        List<ExamMetaData> sortedExams = new ArrayList<>(examMetaDataMap.values());
        Collections.sort(sortedExams, new Comparator<ExamMetaData>() {
            @Override
            public int compare(ExamMetaData e1, ExamMetaData e2) {
                return e1.getExamName().compareToIgnoreCase(e2.getExamName());
            }
        });

        // Sort courses by sequence and name
        List<Course> sortedCourses = new ArrayList<>(courseMap.values());
        Collections.sort(sortedCourses);

        // Data rows: One row per course
        for (Course course : sortedCourses) {
            UUID courseId = course.getCourseId();
            List<ReportCellDetails> dataRow = new ArrayList<>();

            // First column: Course name
            dataRow.add(new ReportCellDetails(course.getCourseName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            // Subsequent columns: Total marks for each exam
            for (ExamMetaData examMetaData : sortedExams) {
                UUID examId = examMetaData.getExamId();
                String totalDisplay = ExamTotalCalculator.calculateTotalForExamCourse(examId, courseId,
                    examCourseMaxMarksMap, examCourseMinMarksMap, examDimensionMap);
                dataRow.add(new ReportCellDetails(totalDisplay, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
            }

            rows.add(new ReportRow(dataRow));
        }
    }

    /**
     * Generates header for courses in columns format
     */
    private void generateCoursesInColumnsHeader(List<ReportRow> rows, List<MergeArea> mergeAreas, ExamStructureReportStore store) {
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap = store.getExamCourseMaxMarksMap();
        Map<UUID, Course> courseMap = store.getCourseMap();
        Map<Integer, ExamDimension> examDimensionMap = store.getExamDimensionMap();

        // Build column structure: each column represents a course-dimension combination
        List<CourseDimensionColumn> columns = ExamReportHelper.buildColumnStructure(examCourseMaxMarksMap, courseMap, examDimensionMap);

        // Row 1: Course names header
        List<ReportCellDetails> courseHeaderRow = new ArrayList<>();
        courseHeaderRow.add(new ReportCellDetails("Exam/Course", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        for (CourseDimensionColumn column : columns) {
            courseHeaderRow.add(new ReportCellDetails(column.getCourseName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        rows.add(new ReportRow(courseHeaderRow));

        // Row 2: Dimension names header
        List<ReportCellDetails> dimensionHeaderRow = new ArrayList<>();
        dimensionHeaderRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        for (CourseDimensionColumn column : columns) {
            dimensionHeaderRow.add(new ReportCellDetails(column.getDimensionName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        rows.add(new ReportRow(dimensionHeaderRow));
    }

    /**
     * Generates header for courses in rows format
     */
    private void generateCoursesInRowsHeader(List<ReportRow> rows, List<MergeArea> mergeAreas, ExamStructureReportStore store) {
        Map<UUID, ExamMetaData> examMetaDataMap = store.getExamMetaDataMap();

        // Sort exams by name
        List<ExamMetaData> sortedExams = new ArrayList<>(examMetaDataMap.values());
        Collections.sort(sortedExams, new Comparator<ExamMetaData>() {
            @Override
            public int compare(ExamMetaData e1, ExamMetaData e2) {
                return e1.getExamName().compareToIgnoreCase(e2.getExamName());
            }
        });

        // Row 1: Exam names header
        List<ReportCellDetails> examHeaderRow = new ArrayList<>();
        examHeaderRow.add(new ReportCellDetails("Course/Exam", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        for (ExamMetaData examMetaData : sortedExams) {
            examHeaderRow.add(new ReportCellDetails(examMetaData.getExamName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        rows.add(new ReportRow(examHeaderRow));
    }

    /**
     * Gets exam course marks data - creates comprehensive maps for exam, course, dimension data
     * including max marks and min marks
     */
    private ExamCourseMarksData getExamCourseMarksData(int instituteId, int academicSessionId, UUID standardId) {
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap = new HashMap<>();
        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap = new HashMap<>();
        Map<UUID, ExamMetaData> examMetaDataMap = new HashMap<>();
        Map<UUID, Course> courseMap = new HashMap<>();
        Map<Integer, ExamDimension> examDimensionMap = new HashMap<>();

        try {
            UUID selectedExamId = null;

            // Get the system exam metadata if no specific exam is selected
            try {
                ExamMetaData examMetaData = examinationManager.getSystemExamMetadata(instituteId, academicSessionId, standardId);
                selectedExamId = examMetaData.getExamId();
            } catch (Exception e) {
                logger.warn("Could not get system exam metadata for institute {}, session {}, standard {}: {}",
                    instituteId, academicSessionId, standardId, e.getMessage());
            }

            // Get the exam structure for the standard
            ExamNode defaultExamStructureNode = getExamNode(instituteId, academicSessionId, standardId, selectedExamId);
            if (defaultExamStructureNode == null) {
                logger.error("No exam structure found for institute {}, session {}, standard {}",
                        instituteId, academicSessionId, standardId);
                return new ExamCourseMarksData(examCourseMaxMarksMap, examCourseMinMarksMap, examMetaDataMap, courseMap, examDimensionMap);
            }

            // Build a map of all exams in the forest
            Map<UUID, ExamNode> examForestMap = new HashMap<>();
            getExamForestMap(defaultExamStructureNode, examForestMap);

            // Get exam courses data
            final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(
                    instituteId, academicSessionId, standardId);

            if (CollectionUtils.isEmpty(examCoursesDataList)) {
                return new ExamCourseMarksData(examCourseMaxMarksMap, examCourseMinMarksMap, examMetaDataMap, courseMap, examDimensionMap);
            }

            StandardMetadata standardMetadata = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
            boolean isScholasticGradingEnabled = standardMetadata.isScholasticGradingEnabled();
            boolean isCoScholasticGradingEnabled = standardMetadata.isCoScholasticGradingEnabled();

            // Process each exam course data
            for (ExamCoursesData examCoursesData : examCoursesDataList) {
                ExamMetaData examMetaData = examCoursesData.getExamMetaData();
                if (examMetaData == null || examMetaData.getExamId() == null) {
                    continue;
                }

                UUID examId = examMetaData.getExamId();

                // Add exam metadata to map
                examMetaDataMap.put(examId, examMetaData);

                List<ExamCourse> examCourseList = examCoursesData.getExamCourses();

                if (CollectionUtils.isEmpty(examCourseList)) {
                    continue;
                }

                // Initialize the maps for this exam if not already present
                if (!examCourseMaxMarksMap.containsKey(examId)) {
                    examCourseMaxMarksMap.put(examId, new HashMap<>());
                }
                if (!examCourseMinMarksMap.containsKey(examId)) {
                    examCourseMinMarksMap.put(examId, new HashMap<>());
                }

                // Process each course in the exam
                for (ExamCourse examCourse : examCourseList) {
                    Course course = examCourse.getCourse();
                    if (course == null) {
                        continue;
                    }

                    UUID courseId = course.getCourseId();
                    CourseType courseType = course.getCourseType();

                    // Add course to map
                    courseMap.put(courseId, course);

                    List<ExamDimensionValues> examDimensionValuesList = examCourse.getExamDimensionValues();

                    if (CollectionUtils.isEmpty(examDimensionValuesList)) {
                        continue;
                    }

                    // Initialize the maps for this course if not already present
                    if (!examCourseMaxMarksMap.get(examId).containsKey(courseId)) {
                        examCourseMaxMarksMap.get(examId).put(courseId, new HashMap<>());
                    }
                    if (!examCourseMinMarksMap.get(examId).containsKey(courseId)) {
                        examCourseMinMarksMap.get(examId).put(courseId, new HashMap<>());
                    }

                    // Process each dimension for the course
                    for (ExamDimensionValues examDimensionValues : examDimensionValuesList) {
                        if (examDimensionValues == null || examDimensionValues.getExamDimension() == null) {
                            continue;
                        }

                        if(courseType == CourseType.SCHOLASTIC && !isScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                            continue;
                        }
                        if(courseType == CourseType.SCHOLASTIC && isScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                            continue;
                        }
                        if(courseType == CourseType.COSCHOLASTIC && !isCoScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                            continue;
                        }
                        if(courseType == CourseType.COSCHOLASTIC && isCoScholasticGradingEnabled
                                && examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                            continue;
                        }

                        ExamDimension examDimension = examDimensionValues.getExamDimension();
                        Integer dimensionId = examDimension.getDimensionId();
                        Double maxMarks = examDimensionValues.getMaxMarks();
                        Double minMarks = examDimensionValues.getMinMarks();

                        // Add exam dimension to map
                        examDimensionMap.put(dimensionId, examDimension);

                        // Add the dimension max marks and min marks to the maps
                        examCourseMaxMarksMap.get(examId).get(courseId).put(dimensionId, maxMarks == null ? "-" : maxMarks.toString());
                        examCourseMinMarksMap.get(examId).get(courseId).put(dimensionId, minMarks == null ? "-" : minMarks.toString());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error while creating exam course marks data for institute {}, session {}, standard {}",
                    instituteId, academicSessionId, standardId, e);
        }

        return new ExamCourseMarksData(examCourseMaxMarksMap, examCourseMinMarksMap, examMetaDataMap, courseMap, examDimensionMap);
    }

    /**
     * Helper method to get exam node structure
     */
    private ExamNode getExamNode(int instituteId, int academicSessionId, UUID standardId, UUID selectedExamId) {
        try {
            final List<ExamNode> examForest = examinationManager.getClassExamsForest(standardId, academicSessionId,
                    instituteId, true);

            if (selectedExamId == null) {
                // Return the first root node if no specific exam is selected
                return examForest.isEmpty() ? null : examForest.get(0);
            }

            for (ExamNode examNode : examForest) {
                if (examNode == null) {
                    continue;
                }
                if (examNode.getExamMetaData().getExamId().equals(selectedExamId)) {
                    return examNode;
                }
                if (!CollectionUtils.isEmpty(examNode.getChildren())) {
                    for (ExamNode childExamNode : examNode.getChildren()) {
                        if (childExamNode == null) {
                            continue;
                        }
                        ExamNode finalExamNode = getExamNodeRecursive(selectedExamId, childExamNode);
                        if (finalExamNode != null) {
                            return finalExamNode;
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            logger.error("Error while getting exam node for institute {}, session {}, standard {}, exam {}",
                instituteId, academicSessionId, standardId, selectedExamId, e);
            return null;
        }
    }

    /**
     * Helper method to recursively search for exam node
     */
    private ExamNode getExamNodeRecursive(UUID examId, ExamNode examNode) {
        if (examNode.getExamMetaData().getExamId().equals(examId)) {
            return examNode;
        }
        if (!CollectionUtils.isEmpty(examNode.getChildren())) {
            for (ExamNode childExamNode : examNode.getChildren()) {
                ExamNode finalExamNode = getExamNodeRecursive(examId, childExamNode);
                if (finalExamNode != null) {
                    return finalExamNode;
                }
            }
        }
        return null;
    }

    /**
     * Helper method to build exam forest map
     */
    private void getExamForestMap(ExamNode examNode, Map<UUID, ExamNode> examForestMap) {
        if (examNode == null) {
            return;
        }

        if (examNode.getExamMetaData() != null && examNode.getExamMetaData().getExamId() != null) {
            examForestMap.put(examNode.getExamMetaData().getExamId(), examNode);
        }

        if (examNode.getChildren() != null) {
            for (ExamNode childNode : examNode.getChildren()) {
                getExamForestMap(childNode, examForestMap);
            }
        }
    }
}
