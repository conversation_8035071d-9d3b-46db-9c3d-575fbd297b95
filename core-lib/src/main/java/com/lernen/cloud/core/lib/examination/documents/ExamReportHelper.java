package com.lernen.cloud.core.lib.examination.documents;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;

/**
 * Helper class containing utility methods for exam report generation
 */
public class ExamReportHelper {
    
    private static final Logger logger = LogManager.getLogger(ExamReportHelper.class);
    
    /**
     * Builds the column structure for the report where each column represents a course-dimension combination
     */
    public static List<CourseDimensionColumn> buildColumnStructure(Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap,
                                                                  Map<UUID, Course> courseMap,
                                                                  Map<Integer, ExamDimension> examDimensionMap) {
        List<CourseDimensionColumn> columns = new ArrayList<>();
        
        // Collect all unique course-dimension combinations across all exams
        Map<UUID, Set<Integer>> courseDimensionMap = new LinkedHashMap<>();
        
        for (Map<UUID, Map<Integer, String>> examCourses : examCourseMaxMarksMap.values()) {
            for (Map.Entry<UUID, Map<Integer, String>> courseEntry : examCourses.entrySet()) {
                UUID courseId = courseEntry.getKey();
                Map<Integer, String> dimensionMarks = courseEntry.getValue();
                
                if (!courseDimensionMap.containsKey(courseId)) {
                    courseDimensionMap.put(courseId, new LinkedHashSet<>());
                }
                courseDimensionMap.get(courseId).addAll(dimensionMarks.keySet());
            }
        }
        
        // Sort courses by sequence and name
        List<Course> sortedCourses = new ArrayList<>();
        for (UUID courseId : courseDimensionMap.keySet()) {
            Course course = courseMap.get(courseId);
            if (course != null) {
                sortedCourses.add(course);
            }
        }
        Collections.sort(sortedCourses);
        
        // Create columns for each course-dimension combination
        for (Course course : sortedCourses) {
            UUID courseId = course.getCourseId();
            Set<Integer> dimensionIds = courseDimensionMap.get(courseId);
            
            if (dimensionIds != null) {
                // Sort dimensions by max marks and isTotal flag
                List<Integer> sortedDimensionIds = sortDimensionsByMaxMarks(
                    new ArrayList<>(dimensionIds), courseId, examCourseMaxMarksMap, examDimensionMap);

                for (Integer dimensionId : sortedDimensionIds) {
                    ExamDimension examDimension = examDimensionMap.get(dimensionId);
                    String dimensionName = examDimension != null ? examDimension.getDimensionName() : "Dimension " + dimensionId;

                    columns.add(new CourseDimensionColumn(courseId, course.getCourseName(), dimensionId, dimensionName));
                }
            }
        }
        
        return columns;
    }
    
    /**
     * Sorts dimensions by max marks (ascending order) with isTotal=true dimensions at the end
     */
    public static List<Integer> sortDimensionsByMaxMarks(List<Integer> dimensionIds, UUID courseId,
                                                        Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap,
                                                        Map<Integer, ExamDimension> examDimensionMap) {
        // Calculate average max marks for each dimension across all exams for this course
        Map<Integer, Double> dimensionAvgMaxMarks = new HashMap<>();
        Map<Integer, Boolean> dimensionIsTotalMap = new HashMap<>();
        
        for (Integer dimensionId : dimensionIds) {
            double totalMaxMarks = 0.0;
            int examCount = 0;
            
            // Get isTotal flag for this dimension
            ExamDimension examDimension = examDimensionMap.get(dimensionId);
            boolean isTotal = examDimension != null && examDimension.isTotal();
            dimensionIsTotalMap.put(dimensionId, isTotal);
            
            // Calculate average max marks across all exams for this dimension
            for (Map<UUID, Map<Integer, String>> examCourses : examCourseMaxMarksMap.values()) {
                Map<Integer, String> courseDimensions = examCourses.get(courseId);
                if (courseDimensions != null && courseDimensions.containsKey(dimensionId)) {
                    String maxMarksStr = courseDimensions.get(dimensionId);
                    if (maxMarksStr != null) {
                        // Try to parse as double, if it fails treat as 0 (for grades)
                        try {
                            double maxMarks = Double.parseDouble(maxMarksStr);
                            totalMaxMarks += maxMarks;
                            examCount++;
                        } catch (NumberFormatException e) {
                            // For grade values, we'll use a default value or skip
                            // You can modify this logic based on your grade system
                            examCount++; // Count it but don't add to total
                        }
                    }
                }
            }
            
            double avgMaxMarks = examCount > 0 ? totalMaxMarks / examCount : 0.0;
            dimensionAvgMaxMarks.put(dimensionId, avgMaxMarks);
        }
        
        // Sort dimensions: non-total dimensions first (by max marks), then total dimensions (by max marks)
        Collections.sort(dimensionIds, new Comparator<Integer>() {
            @Override
            public int compare(Integer d1, Integer d2) {
                boolean isTotal1 = dimensionIsTotalMap.get(d1);
                boolean isTotal2 = dimensionIsTotalMap.get(d2);
                
                // If one is total and other is not, non-total comes first
                if (isTotal1 != isTotal2) {
                    return isTotal1 ? 1 : -1;
                }
                
                // If both have same isTotal status, sort by average max marks (ascending)
                Double avgMarks1 = dimensionAvgMaxMarks.get(d1);
                Double avgMarks2 = dimensionAvgMaxMarks.get(d2);
                
                if (avgMarks1 == null && avgMarks2 == null) {
                    return d1.compareTo(d2); // fallback to dimension ID
                }
                if (avgMarks1 == null) return 1;
                if (avgMarks2 == null) return -1;
                
                int marksComparison = Double.compare(avgMarks1, avgMarks2);
                return marksComparison != 0 ? marksComparison : d1.compareTo(d2);
            }
        });
        
        return dimensionIds;
    }
    
    /**
     * Helper method to get marks display in format (MinMarks/MaxMarks) for a specific exam, course, and dimension
     */
    public static String getMarksDisplayForExamCourseDimension(UUID examId, UUID courseId, Integer dimensionId,
                                                              Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMaxMarksMap,
                                                              Map<UUID, Map<UUID, Map<Integer, String>>> examCourseMinMarksMap,
                                                              Map<Integer, ExamDimension> examDimensionMap) {
        try {
            // Get max marks
            String maxMarks = null;
            Map<UUID, Map<Integer, String>> examCourses = examCourseMaxMarksMap.get(examId);
            if (examCourses != null) {
                Map<Integer, String> courseMaxMarks = examCourses.get(courseId);
                if (courseMaxMarks != null) {
                    maxMarks = courseMaxMarks.get(dimensionId);
                }
            }
            
            // Get min marks
            String minMarks = null;
            Map<UUID, Map<Integer, String>> examCoursesMin = examCourseMinMarksMap.get(examId);
            if (examCoursesMin != null) {
                Map<Integer, String> courseMinMarks = examCoursesMin.get(courseId);
                if (courseMinMarks != null) {
                    minMarks = courseMinMarks.get(dimensionId);
                }
            }
            
            // Format the display string
            if (maxMarks == null && minMarks == null) {
                return "-";
            }
            
            String minValue = StringUtils.isBlank(minMarks) ? "-" : minMarks;
            String maxValue = StringUtils.isBlank(maxMarks) ? "-" : maxMarks;

            return String.format("(%s/%s)", minValue, maxValue);
            
        } catch (Exception e) {
            logger.error("Error while getting marks display for exam {}, course {}, dimension {}", examId, courseId, dimensionId, e);
            return "-";
        }
    }
}
