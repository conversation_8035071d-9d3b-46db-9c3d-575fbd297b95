package com.embrate.cloud.core.lib.report.builder;

import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.embrate.cloud.core.api.report.layout.MergeArea;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.IReportPayload;
import com.embrate.cloud.core.api.report.util.IReportStore;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.report.CellIndexes;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportHorizontalTextAlignment;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import com.lernen.cloud.core.api.report.ReportVerticalTextAlignment;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.lernen.cloud.core.lib.reports.ReportGenerator.*;

/**
 * <AUTHOR>
 */
public abstract class ReportBuilder<P extends IReportPayload, S extends IReportStore> {
	private static final Logger logger = LogManager.getLogger(ReportBuilder.class);

	protected final UserPermissionManager userPermissionManager;

	protected final InstituteManager instituteManager;

	public ReportBuilder(UserPermissionManager userPermissionManager, InstituteManager instituteManager) {
		this.userPermissionManager = userPermissionManager;
		this.instituteManager = instituteManager;
	}

	public abstract ReportMetadata getReportMetadata();

	public abstract List<ReportHeaderAttribute> getStaticHeaders();

	/**
	 * Store object to persist in memory data required for report computation.
	 * This store will be shared in all methods to reuse the stored information
	 * instead of computing every time.
	 * This store would be populated in the beginning and all validation can be
	 * defined for required data
	 */
	public abstract void verifyAuthorisation(P payload);
	public abstract void payloadVerification(P payload);
	public abstract S getStore(P payload);

	public abstract String getDownloadReportName(P payload, S store);

	/**
	 * Total leaf level individual columns in single row. Every row must
	 * contain exactly these many columns in header and in body.
	 */
	private int getTotalColumns(P payload, S store) {
		ReportHeader reportHeader = getHeader(payload, store);
		return reportHeader.getRows().get(0).getColumns().size();
	}

	//todo: we have to give support of the page size
	public abstract ReportConfigs getConfigs(P payload, S store);

	public abstract ReportHeader getHeader(P payload, S store);

	public abstract ReportBody getBody(P payload, S store);

	public static ReportCellDetails createCell(String value, boolean isBold) {
        return new ReportCellDetails(value, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, isBold);
    }

	public static ReportCellDetails createCell(String value, int contentSize, boolean isBold) {
        return new ReportCellDetails(value, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, isBold);
    }

	public static ReportCellDetails createCell(String value, int contentSize, ReportHorizontalTextAlignment reportTextHorizontalAlignment, boolean isBold) {
        return new ReportCellDetails(value, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, isBold, reportTextHorizontalAlignment, ReportVerticalTextAlignment.MIDDLE);
    }

    public static void addEmptyCells(List<ReportCellDetails> row, int count) {
        for (int i = 0; i < count; i++) {
            row.add(createCell("", false));
        }
    }

    public static void addMerge(List<MergeArea> mergeAreas, int rowStart, int rowEnd, int colStart, int colEnd) {
        mergeAreas.add(new MergeArea(rowStart, rowEnd, colStart, colEnd));
    }

	public ReportDetails build(P payload) {
		verifyAuthorisation(payload);
		payloadVerification(payload);
		S store = getStore(payload);
//		if (!store.validData()) {
//			logger.error("Invalid data for report for institute {} ", payload.getInstituteId());
//			return null;
//		}
		String reportName = getDownloadReportName(payload, store);
		int totalColumns = getTotalColumns(payload, store);
		ReportConfigs configs = getConfigs(payload, store);
		ReportHeader header = getHeader(payload, store);
		if (invalidHeader(header, totalColumns)) {
			logger.error("Header is invalid for institute {} report {}", payload.getInstituteId(), reportName);
			return null;
		}
		ReportBody body = getBody(payload, store);
		if (invalidBody(body, totalColumns)) {
			logger.error("Body is invalid for institute {} report {}", payload.getInstituteId(), reportName);
			return null;
		}
		boolean isShowDateAndTime = configs != null && configs.getGeneralConfigs() != null && configs.getGeneralConfigs().isShowDateAndTime();
		boolean isShowCenterHeading = configs != null && configs.getGeneralConfigs() != null && configs.getGeneralConfigs().isShowCenterHeading();
		boolean isWrapText = configs != null && configs.getGeneralConfigs() != null && configs.getGeneralConfigs().isWrapText();
		boolean isShowInstituteName = configs != null && configs.getGeneralConfigs() != null && configs.getGeneralConfigs().isShowInstituteName();
		boolean isShowInstituteLetterHead = configs != null && configs.getGeneralConfigs() != null && configs.getGeneralConfigs().isShowInstituteLetterHead();
		PageSize pageSize = configs != null && configs.getPdfConfigs() != null ? configs.getPdfConfigs().getPageSize() : null;

		ReportSheetDetails reportSheetDetails = new ReportSheetDetails(reportName, pageSize, isShowDateAndTime,
				totalColumns, getMergedCellIndexes(header.getMergeAreaList(), null),
				getMergedCellIndexes(body.getMergeAreaList(), header.getRows().size()), getCells(header.getRows()), getCells(body.getRows()),
				isShowInstituteName, isShowInstituteLetterHead,
				null, totalColumns, isShowCenterHeading, isWrapText);

		return new ReportDetails(reportName, Arrays.asList(reportSheetDetails));
	}

	public List<CellIndexes> getMergedCellIndexes(List<MergeArea> mergeAreaList, Integer offsetHeaderRows) {
		List<CellIndexes> cellIndexes = new ArrayList<>();
		if (CollectionUtils.isEmpty(mergeAreaList)) {
			return cellIndexes;
		}
		for (MergeArea mergeArea : mergeAreaList) {
			cellIndexes.add(new CellIndexes(mergeArea.getStartRow() + (offsetHeaderRows == null ? 0 : offsetHeaderRows.intValue()), mergeArea.getEndRow() + (offsetHeaderRows == null ? 0 : offsetHeaderRows.intValue()), mergeArea.getStartCol(), mergeArea.getEndCol()));
		}
		return cellIndexes;
	}

	private boolean invalidHeader(ReportHeader header, int totalColumns) {
		//validate header and its rows that all must have columns and of equal size
		if (header == null || CollectionUtils.isEmpty(header.getRows())) {
			return true;
		}

		for (ReportRow row : header.getRows()) {
			if (CollectionUtils.isEmpty(row.getColumns())) {
				return true;
			}
			if (totalColumns != row.getColumns().size()) {
				return true;
			}
		}
		return false;
	}

	private boolean invalidBody(ReportBody body, int totalColumns) {
		//validate body and its rows that all must have columns and of equal size
		if (body == null) {
			return true;
		}
		if (CollectionUtils.isEmpty(body.getRows())) {
			return false;
		}

		int size = 0;
		logger.info("total column : {}", totalColumns);
		for (ReportRow row : body.getRows()) {
			logger.info("row column details : {}", CollectionUtils.isEmpty(row.getColumns()));
			if (CollectionUtils.isEmpty(row.getColumns())) {
				return true;
			}
			logger.info("row column size : {}", row.getColumns().size());
			if (totalColumns != row.getColumns().size()) {
				return true;
			}
		}
		return false;
	}

	private List<List<ReportCellDetails>> getCells(List<ReportRow> rows) {
		List<List<ReportCellDetails>> cells = new ArrayList<>();
		if (CollectionUtils.isEmpty(rows)) {
			return cells;
		}
		for (ReportRow row : rows) {
			cells.add(row.getColumns());
		}
		return cells;
	}

	public void addColumns(ReportRow row, String... values) {
		addColumns(row, CONTENT_SIZE, BLACK_COLOR, ReportHorizontalTextAlignment.LEFT, values);
	}

	public void addColumns(ReportRow row, int contentSize, String... values) {
		addColumns(row, contentSize, BLACK_COLOR, ReportHorizontalTextAlignment.LEFT, values);
	}

	public void addColumns(ReportRow row, int contentSize, String textColor, ReportHorizontalTextAlignment reportTextHorizontalAlignment, String... values){
		for (String value : values) {
			row.add(new ReportCellDetails(value, STRING, contentSize, textColor, WHITE_COLOR, false, reportTextHorizontalAlignment, ReportVerticalTextAlignment.MIDDLE));
		}
	}

	public void addColumns(ReportRow row, Integer... values) {
		for (Integer value : values) {
			row.add(new ReportCellDetails(value, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
		}
	}

	public void addColumns(ReportRow row, int contentSize, Integer... values) {
		addColumns(row, contentSize, BLACK_COLOR, ReportHorizontalTextAlignment.CENTER, values);
	}

	public void addColumns(ReportRow row, int contentSize, String textColor, ReportHorizontalTextAlignment reportTextHorizontalAlignment, Integer... values){
		for (Integer value : values) {
			row.add(new ReportCellDetails(value, INTEGER, contentSize, textColor, WHITE_COLOR, false, reportTextHorizontalAlignment, ReportVerticalTextAlignment.MIDDLE));
		}
	}
}
