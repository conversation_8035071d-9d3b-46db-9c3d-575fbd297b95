<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <!-- <context:property-placeholder location="classpath:utils-${my.env}.properties,
        classpath:core-pii-${my.env}.properties, classpath:core-cass-${my.env}.properties,
        classpath:core-iam-${my.env}.properties,classpath:core-lib-${my.env}.properties,
        classpath:core-emails-${my.env}.properties, classpath:emails.properties,classpath:event-messages.properties,classpath:core-event.properties,
        /WEB-INF/${my.env}.properties,classpath:sms-global.properties,classpath:sms-${my.env}.properties"
        system-properties-mode="OVERRIDE" /> -->

    <context:property-placeholder
            location="classpath*:core-lib-${lernen_env}.properties, classpath*:dao-tier-${lernen_env}.properties, classpath*:utils-${lernen_env}.properties"
            system-properties-mode="OVERRIDE"/>

    <import resource="classpath:oauth2-config.xml"/>
    <import resource="classpath:core-lib.xml"/>
    <import resource="classpath:emailer.xml"/>
    <import resource="classpath:pdf.xml"/>
    <import resource="classpath:sms.xml"/>
    <import resource="classpath:whatsapp.xml"/>
    <import resource="classpath:push-notifications.xml"/>
    <import resource="classpath:voicecall.xml"/>
    <import resource="classpath:lambda-utils.xml"/>


    <context:component-scan base-package="com.lernen.cloud.data.server, com.embrate.cloud.data.server"/>

    <bean id="testEndPoint" class="com.lernen.cloud.data.server.TestEndPoint">
        <constructor-arg name="helloWorld" ref="helloWorld"/>
        <constructor-arg name="testDao" ref="testDao"/>
        <constructor-arg name="firebaseMessagingServiceHandler" ref="firebaseMessagingServiceHandler"/>
    </bean>

    <bean id="userEndPoint" class="com.lernen.cloud.data.server.user.UserEndPoint">
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="regenerateUserCredentialSMSHandler" ref="regenerateUserCredentialSMSHandler"/>
        <constructor-arg name="userCredentailsEmailHandler" ref="userCredentailsEmailHandler"/>
        <constructor-arg name="resetPasswordEmailHandler" ref="resetPasswordEmailHandler"/>
        <constructor-arg name="staffUserCreationEmailHandler" ref="staffUserCreationEmailHandler"/>
        <constructor-arg name="userBirthdaySMSHandler" ref="userBirthdaySMSHandler"/>
        <constructor-arg name="birthdayPushNotificationHandler" ref="birthdayPushNotificationHandler"/>
        <constructor-arg name="userCredentialsPDFGenerator" ref="userCredentialsPDFGenerator"/>
    </bean>

    <bean id="libraryEndpoint" class="com.lernen.cloud.data.server.library.LibraryEndpoint">
        <constructor-arg name="libraryManager" ref="libraryManager"/>
    </bean>
     <bean id="lessonPlanEndPoint" class="com.lernen.cloud.data.server.lessonplan.LessonPlanEndPoint">
        <constructor-arg name="lessonPlanManager" ref="lessonPlanManager"/>
    </bean>
    <bean id="assessmentDetailsEndPoint" class="com.lernen.cloud.data.server.assessment.AssessmentDetailsEndPoint">
        <constructor-arg name="assessmentDetailsManager" ref="assessmentDetailsManager"/>
    </bean>
    <bean id="studyTrackerEndPoint" class="com.lernen.cloud.data.server.studytracker.StudyTrackerEndPoint">
        <constructor-arg name="studyTrackerManager" ref="studyTrackerManager"/>
    </bean>

    <bean id="storeInventoryEndpoint"
          class="com.lernen.cloud.data.server.inventory.StoreInventoryEndPoint">
        <constructor-arg name="storeInventoryManager" ref="storeInventoryManager"/>
    </bean>

    <bean id="productEndpoint" class="com.lernen.cloud.data.server.inventory.ProductEndpoint">
        <constructor-arg name="storeInventoryManager" ref="storeInventoryManager"/>
    </bean>

    <bean id="productTransactionsEndpoint"
          class="com.lernen.cloud.data.server.inventory.ProductTransactionsEndpoint">
        <constructor-arg name="productTransactionsManager"
                         ref="productTransactionsManager"/>
        <constructor-arg name="storeInventoryPDFInvoiceHandler"
                         ref="storeInventoryPDFInvoiceHandler"/>
    </bean>

    <bean id="inventoryOutletEndPoint"
          class="com.embrate.cloud.data.server.inventory.v2.InventoryOutletEndPoint">
        <constructor-arg name="inventoryOutletManager" ref="inventoryOutletManager"/>
    </bean>


    <bean id="inventoryProductEndpoint" class="com.embrate.cloud.data.server.inventory.v2.InventoryProductEndpoint">
        <constructor-arg name="inventoryProductManager" ref="inventoryProductManager"/>
    </bean>

    <bean id="inventoryTransactionsEndpoint"
          class="com.embrate.cloud.data.server.inventory.v2.InventoryTransactionsEndpoint">
        <constructor-arg name="inventoryTransactionsManager" ref="inventoryTransactionsManager"/>
        <constructor-arg name="storeInventoryPDFInvoiceHandlerV2" ref="storeInventoryPDFInvoiceHandlerV2"/>
    </bean>

    <bean id="inventoryMigrationEndPoint"
          class="com.embrate.cloud.data.server.inventory.v2.InventoryMigrationEndPoint">
        <constructor-arg name="inventoryMigrationManager" ref="inventoryMigrationManager"/>
    </bean>


    <bean id="reportGenerationEndPoint"
          class="com.lernen.cloud.data.server.inventory.ReportGenerationEndPoint">
        <constructor-arg name="reportGenerationManager" ref="reportGenerationManager"/>
    </bean>

    <!-- <bean id="ingestionEndpoint" class="com.lernen.cloud.data.server.inventory.IngestionEndpoint">
        <constructor-arg name="stockIngester" ref="stockIngester" /> </bean> -->

    <bean id="instituteEndPoint" class="com.lernen.cloud.data.server.institute.InstitueEndPoint">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="instituteManagementManager" ref="instituteManagementManager"/>
    </bean>

    <bean id="instituteConfigEndPoint" class="com.lernen.cloud.data.server.institute.InstituteConfigEndPoint">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="timetableManager" ref="timetableManager"/>
    </bean>

    <bean id="productGroupEndPoint"
          class="com.lernen.cloud.data.server.inventory.ProductGroupEndPoint">
        <constructor-arg name="productGroupManager" ref="productGroupManager"/>
    </bean>

    <bean id="inventoryProductGroupEndPoint"
          class="com.embrate.cloud.data.server.inventory.v2.InventoryProductGroupEndPoint">
        <constructor-arg name="inventoryProductGroupManager" ref="inventoryProductGroupManager"/>
    </bean>

    <bean id="feeConfigurationEndPoint"
          class="com.lernen.cloud.data.server.fees.FeeConfigurationEndPoint">
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="studentFeesChartHandler" ref="studentFeesChartHandler"/>
        <constructor-arg name="feeChallaHandler" ref="feeChallaHandler"/>
    </bean>

    <bean id="feeDiscountConfigurationEndPoint"
          class="com.lernen.cloud.data.server.fees.FeeDiscountConfigurationEndPoint">
        <constructor-arg name="feeDiscountConfigurationManager"
                         ref="feeDiscountConfigurationManager"/>
    </bean>

    <bean id="studentEndPoint" class="com.lernen.cloud.data.server.student.StudentEndPoint">
        <constructor-arg name="studentAdmissionManager" ref="studentAdmissionManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="admissionFormHandler" ref="admissionFormHandler"/>
        <constructor-arg name="studentIdentityCardHandler" ref="studentIdentityCardHandler"/>
        <constructor-arg name="studyCertificateDocumentHandler" ref="studyCertificateDocumentHandler"/>
        <constructor-arg name="transferCertificateHandler" ref="transferCertificateHandler"/>
        <constructor-arg name="regenerateUserCredentialSMSHandler" ref="regenerateUserCredentialSMSHandler"/>
        <constructor-arg name="promotionCertificateDocumentHandler" ref="promotionCertificateDocumentHandler"/>
        <constructor-arg name="studentSMSHandler" ref="studentSMSHandler"/>
        <constructor-arg name="characterCertificateDocumentHandler" ref="characterCertificateDocumentHandler"/>
        <constructor-arg name="dynamicDocumentHandler" ref="dynamicDocumentHandler"/>
        <constructor-arg name="tuitionFeesCertificateDocumentHandler" ref="tuitionFeesCertificateDocumentHandler"/>
        <constructor-arg name="studentIdentityCardLambdaHandler" ref="studentIdentityCardLambdaHandler"/>
        <constructor-arg name="boardRegistrationFormHandler" ref="boardRegistrationFormHandler"/>
        <constructor-arg name="birthdayCertificateDocumentHandler" ref="birthdayCertificateDocumentHandler"/>
        <constructor-arg name="studentAdmissionEmailHandler" ref="studentAdmissionEmailHandler"/>
        <constructor-arg name="bookReceiptDocumentHandler" ref="bookReceiptDocumentHandler"/>
        <constructor-arg name="bonafideCertificateDocumentHandler" ref="bonafideCertificateDocumentHandler"/>
    </bean>

    <bean id="feePaymentEndPoint" class="com.lernen.cloud.data.server.fees.FeePaymentEndPoint">
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager"/>
        <constructor-arg name="emailService" ref="emailService"/>
        <constructor-arg name="dueFeesSMSHandler" ref="dueFeesSMSHandler"/>
        <constructor-arg name="dueFeesVoiceHandler" ref="dueFeesVoiceHandler"/>
        <constructor-arg name="feePaymentSMSHandler" ref="feePaymentSMSHandler"/>
        <constructor-arg name="demandNoticeHandler" ref="demandNoticeHandler"/>
        <constructor-arg name="notificationManager" ref="notificationManager"/>
        <constructor-arg name="feeInvoiceHandler" ref="feeInvoiceHandler"/>
        <constructor-arg name="feePaymentPushNotificationHandler" ref="feePaymentPushNotificationHandler"/>
        <constructor-arg name="feePaymentReminderPushNotificationHandler"
                         ref="feePaymentReminderPushNotificationHandler"/>
        <constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
    </bean>

    <!--	<bean id="ingestStudentData" class="com.lernen.cloud.data.server.IngestStudentData">-->
    <!--		<constructor-arg name="ingestVidhyasthaliStudents"-->
    <!--			ref="ingestVidhyasthaliStudents" />-->
    <!--		<constructor-arg name="ingestSesomuStudents" ref="ingestSesomuStudents" />-->
    <!--		<constructor-arg name="ingestBalajiConventStudents"-->
    <!--			ref="ingestBalajiConventStudents" />-->
    <!--		<constructor-arg name="ingestRaghunathStudents" ref="ingestRaghunathStudents" />-->
    <!--	</bean>-->
    <bean id="followUpEndPoint"
          class="com.lernen.cloud.data.server.followup.FollowUpEndPoint">
        <constructor-arg name="followUpManager" ref="followUpManager"/>
    </bean>

    <bean id="feesReportGenerationEndPoint"
          class="com.lernen.cloud.data.server.fees.FeesReportGenerationEndPoint">
        <constructor-arg name="feeReportDetailsGenerator" ref="feeReportDetailsGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
    </bean>

    <bean id="staffReportEndpoint"
          class="com.lernen.cloud.data.server.staff.StaffReportEndpoint">
        <constructor-arg name="staffReportGenerator" ref="staffReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
    </bean>

    <bean id="transportConfigurationEndPoint"
          class="com.lernen.cloud.data.server.transport.TransportConfigurationEndPoint">
        <constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
        <constructor-arg name="vehicleDocumentRenewalReminderPushNotificationHandler" ref="vehicleDocumentRenewalReminderPushNotificationHandler"/>
    </bean>

    <bean id="studentDiaryEndPoint" class="com.lernen.cloud.data.server.diary.StudentDiaryEndPoint">
        <constructor-arg name="studentDiaryManager" ref="studentDiaryManager"/>
        <constructor-arg name="studentDiaryPushNotificationHandler" ref="studentDiaryPushNotificationHandler"/>
    </bean>

    <bean id="staffDiaryEndPoint"
          class="com.lernen.cloud.data.server.diary.StaffDiaryEndPoint">
        <constructor-arg name="staffDiaryManager" ref="staffDiaryManager"/>
        <constructor-arg name="staffDiaryPushNotificationHandler" ref="staffDiaryPushNotificationHandler"/>
    </bean>

    <bean id="transportFeesConfigurationEndPoint"
          class="com.lernen.cloud.data.server.transport.TransportFeesConfigurationEndPoint">
        <constructor-arg name="transportFeeConfigurationManager"
                         ref="transportFeeConfigurationManager"/>
    </bean>

    <bean id="transportAssignmentEndPoint"
          class="com.lernen.cloud.data.server.transport.TransportAssignmentEndPoint">
        <constructor-arg name="transportAssignmentManager"
                         ref="transportAssignmentManager"/>
    </bean>

    <bean id="transportReportsEndPoint"
          class="com.lernen.cloud.data.server.transport.TransportReportsEndPoint">
        <constructor-arg name="transportReportsGenerator" ref="transportReportsGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="studentReportsEndPoint"
          class="com.lernen.cloud.data.server.student.StudentReportsEndPoint">
        <constructor-arg name="studentReportsGenerator" ref="studentReportsGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="studentManagementReportsEndPoint"
          class="com.lernen.cloud.data.server.student.StudentManagementReportsEndpoint">
        <constructor-arg name="studentManagementReportsGenerator" ref="studentManagementReportsGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="courseEndPoint" class="com.embrate.cloud.data.server.courses.CourseEndPoint">
        <constructor-arg name="courseManager" ref="courseManager"/>
    </bean>

    <bean id="courseReportEndpoint" class="com.embrate.cloud.data.server.courses.CourseReportEndpoint">
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="courseReportGenerator" ref="courseReportGenerator"/>
    </bean>

    <bean id="courseConfigEndPoint" class="com.embrate.cloud.data.server.courses.CourseConfigEndPoint">
        <constructor-arg name="courseConfigManager" ref="courseConfigManager"/>
    </bean>


    <bean id="examinationEndPoint"
          class="com.lernen.cloud.data.server.examination.ExaminationEndPoint">
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="hpcFormManager" ref="hpcFormManager"/>
        <constructor-arg name="examGreenSheetManager" ref="examGreenSheetManager"/>
        <constructor-arg name="examReportCardManager" ref="examReportCardManager"/>
        <constructor-arg name="examReportSerivceProvider" ref="examReportSerivceProvider"/>
        <constructor-arg name="admitCardHandler" ref="admitCardHandler"/>
        <constructor-arg name="examGreenSheetHandler" ref="examGreenSheetHandler"/>
        <constructor-arg name="examinationStructureHelper" ref="examinationStructureHelper"/>
        <constructor-arg name="examSMSHandler" ref="examSMSHandler"/>
        <constructor-arg name="notificationManager" ref="notificationManager"/>
        <constructor-arg name="datesheetHandler" ref="datesheetHandler"/>
        <constructor-arg name="examReportLambdaServiceProvider" ref="examReportLambdaServiceProvider"/>
        <constructor-arg name="admitCardLambdaHandler" ref="admitCardLambdaHandler"/>
        <constructor-arg name="examGreenSheetLambdaHandler" ref="examGreenSheetLambdaHandler"/>
        <constructor-arg name="studentReportCardPublishPushNotificationHandler" ref="studentReportCardPublishPushNotificationHandler"/>
    </bean>

    <bean id="complainBoxEndPoint"
          class="com.lernen.cloud.data.server.complainbox.ComplainBoxEndPoint">
        <constructor-arg name="complainBoxManager" ref="complainBoxManager"/>
        <constructor-arg name="studentComplaintResponsePushNotificationHandler"
                         ref="studentComplaintResponsePushNotificationHandler"/>
        <constructor-arg name="studentAddComplainPushNotificationHandler" ref="studentAddComplainPushNotificationHandler"/>
    </bean>


    <bean id="customSMSSender" class="com.lernen.cloud.data.server.sms.CustomSMSSender">
        <constructor-arg name="smsSender" ref="pcExpertSMSSender"/>
        <constructor-arg name="customSMSHandler" ref="customSMSHandler"/>
        <constructor-arg name="notificationManager" ref="notificationManager"/>
    </bean>

    <bean id="msg91WebhookEndpoint"
          class="com.lernen.cloud.data.server.sms.service.MSG91WebhookEndpoint">
        <constructor-arg name="msg91SMSWebhookManager" ref="msg91SMSWebhookManager"/>
    </bean>

    <bean id="attendanceEndPoint" class="com.lernen.cloud.data.server.attendance.AttendanceEndPoint">
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="userAttendanceManager" ref="userAttendanceManager"/>
        <constructor-arg name="attendanceUpdatePushNotificationHandler" ref="attendanceUpdatePushNotificationHandler"/>
        <constructor-arg name="studentAttendanceSMSHandler" ref="studentAttendanceSMSHandler"/>
    </bean>

    <bean id="staffAttendanceEndPoint" class="com.lernen.cloud.data.server.attendance.staff.StaffAttendanceEndPoint">
        <constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager"/>
    </bean>

    <bean id="staffAttendanceEndPointV2"
          class="com.lernen.cloud.data.server.attendance.staff.StaffAttendanceEndPointV2">
        <constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager"/>
        <constructor-arg name="staffAttendancePushNotificationHandler" ref="staffAttendancePushNotificationHandler"/>
        <constructor-arg name="staffAttendanceSMSHandler" ref="staffAttendanceSMSHandler"/>
        <constructor-arg name="staffAttendanceStatusPushNotificationHandler" ref="staffAttendanceStatusPushNotificationHandler"/>
        <constructor-arg name="staffAttendanceStatusSMSHandler" ref="staffAttendanceStatusSMSHandler"/>
        <constructor-arg name="staffAttendancePushNotificationHandlerV2" ref="staffAttendancePushNotificationHandlerV2"/>
        <constructor-arg name="staffAttendanceSMSHandlerV2" ref="staffAttendanceSMSHandlerV2"/>
    </bean>

    <bean id="trackingEventsEndPoint"
          class="com.lernen.cloud.data.server.tracking.events.TrackingEventsEndPoint">
        <constructor-arg name="trackingEventsManager" ref="trackingEventsManager"/>
        <constructor-arg name="trackingEventPDFGenerator" ref="trackingEventPDFGenerator"/>
    </bean>

    <bean id="staffEndPoint" class="com.lernen.cloud.data.server.staff.StaffEndPoint">
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="staffUserCreationEmailHandler" ref="staffUserCreationEmailHandler"/>
        <constructor-arg name="staffIdentityCardHandler" ref="staffIdentityCardHandler"/>
        <constructor-arg name="experienceLetterDocumentHandler" ref="experienceLetterDocumentHandler"/>
    </bean>

    <bean id="incomeExpenseEndPoint" class="com.lernen.cloud.data.server.incomeexpense.IncomeExpenseEndPoint">
        <constructor-arg name="incomeExpenseManager" ref="incomeExpenseManager"/>
    </bean>

    <bean id="userPermissionEndpoint" class="com.lernen.cloud.data.server.permissions.UserPermissionEndpoint">
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="incomeExpenseReportGenerationEndPoint"
          class="com.lernen.cloud.data.server.incomeexpense.IncomeExpenseReportGenerationEndPoint">
        <constructor-arg name="incomeExpenseReportGenerator" ref="incomeExpenseReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="auditLogEndpoint" class="com.lernen.cloud.data.server.audit.log.AuditLogEndpoint">
        <constructor-arg name="auditLogManager" ref="auditLogManager"/>
    </bean>

    <bean id="examinationReportEndPoint" class="com.lernen.cloud.data.server.examination.ExaminationReportEndPoint">
        <constructor-arg name="examReportGenerator" ref="examReportGenerator"/>
        <constructor-arg name="studentMarksFeedDocumentHandler" ref="studentMarksFeedDocumentHandler"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="examGraphicalReportGenerator" ref="examGraphicalReportGenerator"/>
        <constructor-arg name="examinationDocuments" ref="examinationDocuments"/>
    </bean>

    <bean id="clientPaymentEndpoint" class="com.lernen.cloud.data.server.client.payment.ClientPaymentEndpoint">
        <constructor-arg name="clientPaymentManager" ref="clientPaymentManager"/>
    </bean>

    <bean id="studentRegistrationEndPoint" class="com.lernen.cloud.data.server.student.StudentRegistrationEndPoint">
        <constructor-arg name="studentRegistrationManager" ref="studentRegistrationManager"/>
    </bean>

    <bean id="websiteSupportEndPoint" class="com.lernen.cloud.data.server.website.WebsiteSupportEndPoint">
        <constructor-arg name="websiteContactEmailHandler" ref="websiteContactEmailHandler"/>
    </bean>

    <bean id="userPreferenceEndPoint" class="com.embrate.cloud.data.server.user.preference.UserPreferenceEndPoint">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="userWalletEndPoint" class="com.embrate.cloud.data.server.wallet.UserWalletEndPoint">
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="walletInvoiceHandler" ref="walletInvoiceHandler"/>
    </bean>

    <bean id="salaryEndPoint" class="com.embrate.cloud.data.server.salary.SalaryConfigurationEndPoint">
        <constructor-arg name="salaryConfigurationManager" ref="salaryConfigurationManager"/>
    </bean>

    <bean id="lectureEndPoint" class="com.embrate.cloud.data.server.lecture.LectureEndPoint">
        <constructor-arg name="lectureManager" ref="lectureManager"/>
        <constructor-arg name="onlineLectureBroadcastPushNotificationHandler"
                         ref="onlineLectureBroadcastPushNotificationHandler"/>
    </bean>

    <bean id="discussionBoardEndPoint" class="com.embrate.cloud.data.server.discussionboard.DiscussionBoardEndPoint">
        <constructor-arg name="discussionBoardManager" ref="discussionBoardManager"/>
        <constructor-arg name="discussionBoardBroadcastPushNotificationHandler"
                         ref="discussionBoardBroadcastPushNotificationHandler"/>
    </bean>

    <bean id="mobileApplicationManagementEndpoint"
          class="com.lernen.cloud.data.server.application.mobile.MobileApplicationManagementEndpoint">
        <constructor-arg name="mobileApplicationManager" ref="mobileApplicationManager"/>
        <constructor-arg name="mobileConfigurationManager" ref="mobileConfigurationManager"/>
    </bean>

    <bean id="attendanceReportGenerationEndpoint"
          class="com.lernen.cloud.data.server.attendance.AttendanceReportGenerationEndpoint">
        <constructor-arg name="attendanceReportGenerator" ref="attendanceReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="staffAttendanceReportGenerationEndpoint"
          class="com.lernen.cloud.data.server.attendance.staff.StaffAttendanceReportGenerationEndpoint">
        <constructor-arg name="staffAttendanceReportGenerator" ref="staffAttendanceReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>
    <bean id="studyTrackerReportGenerationEndpoint"
          class="com.lernen.cloud.data.server.studytracker.StudyTrackerReportGenerationEndPoint">
        <constructor-arg name="studyTrackerReportGenerator" ref="studyTrackerReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="homeworkEndPoint" class="com.embrate.cloud.data.server.homework.HomeworkEndPoint">
        <constructor-arg name="homeworkManager" ref="homeworkManager"/>
        <constructor-arg name="homeworkBroadcastPushNotificationHandler"
                         ref="homeworkBroadcastPushNotificationHandler"/>
        <constructor-arg name="homeworkMarkingUpdatePushNotificationHandler"
                         ref="homeworkMarkingUpdatePushNotificationHandler"/>
    </bean>

    <bean id="lectureReportGenerationEndpoint"
          class="com.embrate.cloud.data.server.lecture.LectureReportGenerationEndpoint">
        <constructor-arg name="lectureReportGenerator" ref="lectureReportGenerator"/>
    </bean>

    <bean id="noticeBoardEndPoint" class="com.embrate.cloud.data.server.noticeboard.NoticeBoardEndPoint">
        <constructor-arg name="noticeBoardManager" ref="noticeBoardManager"/>
        <constructor-arg name="noticeBoardBroadcastPushNotificationHandler"
                         ref="noticeBoardBroadcastPushNotificationHandler"/>
    </bean>

    <bean id="pushNotificationEndpoint"
          class="com.embrate.cloud.data.server.push.notification.PushNotificationEndpoint">
        <constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
        <constructor-arg name="customPushNotificationHandler" ref="customPushNotificationHandler"/>
    </bean>

    <bean id="instituteOnBoardingEndPoint"
          class="com.embrate.cloud.data.server.institute.onboarding.InstituteOnBoardingEndPoint">
        <constructor-arg name="instituteOnBoardingManager" ref="instituteOnBoardingManager"/>
    </bean>

    <bean id="timetableEndPoint" class="com.embrate.cloud.data.server.timetable.TimetableEndPoint">
        <constructor-arg name="timetableManager" ref="timetableManager"/>
        <constructor-arg name="timetablePublishedPushNotificationHandler"
                         ref="timetablePublishedPushNotificationHandler"/>
        <constructor-arg name="timetableHandler" ref="timetableHandler"/>
    </bean>

    <bean id="configurationEndPoint" class="com.embrate.cloud.data.server.configs.ConfigurationEndPoint">
        <constructor-arg name="configurationManager" ref="configurationManager"/>
    </bean>

    <bean id="voiceCallEndPoint" class="com.embrate.cloud.data.server.voicecall.VoiceCallEndPoint">
        <constructor-arg name="customVoiceCallHandler" ref="customVoiceCallHandler"/>
    </bean>

    <bean id="notificationEndpoint" class="com.embrate.cloud.data.server.notification.NotificationEndpoint">
        <constructor-arg name="notificationManager" ref="notificationManager"/>
        <constructor-arg name="smsStatusUpdateHandler" ref="smsStatusUpdateHandler"/>
        <constructor-arg name="voiceStatusUpdateHandler" ref="voiceStatusUpdateHandler"/>
    </bean>

    <bean id="notificationTemplateEndpoint"
          class="com.embrate.cloud.data.server.notification.NotificationTemplateEndpoint">
        <constructor-arg name="notificationTemplateManager" ref="notificationTemplateManager"/>
        <constructor-arg name="voiceCallTemplateHandler" ref="voiceCallTemplateHandler"/>
        <constructor-arg name="voiceStatusUpdateHandler" ref="voiceStatusUpdateHandler"/>
    </bean>

    <bean id="paymentGatewayEndpoint" class="com.embrate.cloud.data.server.payment.gateway.PaymentGatewayEndpoint">
        <constructor-arg name="paymentGatewayManager" ref="paymentGatewayManager"/>
        <constructor-arg name="scratchCardBroadcastPushNotificationHandler"
                         ref="scratchCardBroadcastPushNotificationHandler"/>
    </bean>

    <bean id="paymentGatewayWebhookEndpoint"
          class="com.embrate.cloud.data.server.payment.gateway.PaymentGatewayWebhookEndpoint">
        <constructor-arg name="paymentGatewayWebhookManager" ref="paymentGatewayWebhookManager"/>
    </bean>

    <bean id="paymentGatewayMerchantEndpoint"
          class="com.embrate.cloud.data.server.payment.gateway.merchant.PaymentGatewayMerchantEndpoint">
        <constructor-arg name="paymentGatewayMerchantManager" ref="paymentGatewayMerchantManager"/>
    </bean>

    <bean id="studentPortalEndPoint" class="com.embrate.cloud.data.server.portal.student.StudentPortalEndPoint">
        <constructor-arg name="studentPortalManager" ref="studentPortalManager"/>
    </bean>

    <bean id="transportTrackingEndpoint"
          class="com.embrate.cloud.data.server.transport.tracking.TransportTrackingEndpoint">
        <constructor-arg name="transportTrackingManger" ref="transportTrackingManger"/>
        <constructor-arg name="transportTrackingPushNotificationHandler"
                         ref="transportTrackingPushNotificationHandler"/>
    </bean>

    <bean id="scratchCardEndpoint" class="com.embrate.cloud.data.server.scratchcard.ScratchCardEndpoint">
        <constructor-arg name="scratchCardManager" ref="scratchCardManager"/>
    </bean>

    <bean id="excelNotificationEndpoint" class="com.embrate.cloud.data.server.notification.ExcelNotificationEndpoint">
        <constructor-arg name="excelNotificationManager" ref="excelNotificationManager"/>
        <constructor-arg name="excelSMSHandler" ref="excelSMSHandler"/>
        <constructor-arg name="excelVoiceCallHandler" ref="excelVoiceCallHandler"/>
    </bean>

    <bean id="userWalletReportsEndPoint" class="com.embrate.cloud.data.server.wallet.UserWalletReportsEndPoint">
        <constructor-arg name="userWalletReportsManager" ref="userWalletReportsManager"/>
    </bean>

    <bean id="holidayCalendarEndpoint" class="com.embrate.cloud.data.server.calendar.holiday.HolidayCalendarEndpoint">
        <constructor-arg name="calendarManager" ref="calendarManager"/>
    </bean>

    <bean id="calendarEndpoint" class="com.embrate.cloud.data.server.calendar.CalendarEndpoint">
        <constructor-arg name="calendarManager" ref="calendarManager"/>
    </bean>

    <bean id="inventoryReportGenerationEndPoint"
          class="com.embrate.cloud.data.server.inventory.v2.InventoryReportGenerationEndPoint">
        <constructor-arg name="inventoryReportGenerationManager" ref="inventoryReportGenerationManager"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

    <bean id="salaryReportGenerationEndPoint"
          class="com.embrate.cloud.data.server.salary.SalaryReportGenerationEndPoint">
        <constructor-arg name="salaryReportGenerationManager" ref="salaryReportGenerationManager"/>
    </bean>

    <bean id="attendanceDeviceEndpoint"
          class="com.embrate.cloud.data.server.attendance.AttendanceDeviceEndpoint">
        <constructor-arg name="attendanceDeviceManager" ref="attendanceDeviceManager"/>
    </bean>

    <bean id="camsUnitDeviceAttendanceEndpoint"
          class="com.embrate.cloud.data.server.attendance.service.CamsUnitDeviceAttendanceEndpoint">
        <constructor-arg name="camsUnitDeviceManager" ref="camsUnitDeviceManager"/>
        <constructor-arg name="attendanceUpdatePushNotificationHandler" ref="attendanceUpdatePushNotificationHandler"/>
        <constructor-arg name="staffAttendancePushNotificationHandler" ref="staffAttendancePushNotificationHandler"/>
        <constructor-arg name="studentAttendanceSMSHandler" ref="studentAttendanceSMSHandler"/>
        <constructor-arg name="staffAttendanceSMSHandler" ref="staffAttendanceSMSHandler"/>
    </bean>

    <bean id="mantraDeviceAttendanceEndpoint"
          class="com.embrate.cloud.data.server.attendance.service.MantraDeviceAttendanceEndpoint">
        <constructor-arg name="mantraDeviceManager" ref="mantraDeviceManager"/>
        <constructor-arg name="attendanceUpdatePushNotificationHandler" ref="attendanceUpdatePushNotificationHandler"/>
        <constructor-arg name="staffAttendancePushNotificationHandler" ref="staffAttendancePushNotificationHandler"/>
        <constructor-arg name="studentAttendanceSMSHandler" ref="studentAttendanceSMSHandler"/>
        <constructor-arg name="staffAttendanceSMSHandler" ref="staffAttendanceSMSHandler"/>
    </bean>

    <bean id="frontDeskEndpoint" class="com.embrate.cloud.data.server.fontdesk.FrontDeskEndpoint">
        <constructor-arg name="frontDeskManager" ref="frontDeskManager"/>
        <constructor-arg name="frontDeskSMSHandler" ref="frontDeskSMSHandler"/>
        <constructor-arg name="gatePassPushNotificationHandler" ref="gatePassPushNotificationHandler"/>
        <constructor-arg name="gatePassHandler" ref="gatePassHandler"/>
    </bean>

    <bean id="institutePreferenceEndPoint"
          class="com.embrate.cloud.data.server.institute.management.InstitutePreferenceEndPoint">
        <constructor-arg name="institutePreferenceManager" ref="institutePreferenceManager"/>
    </bean>

    <bean id="timetableReportEndpoint" class="com.embrate.cloud.data.server.timetable.TimetableReportEndpoint">
        <constructor-arg name="timetableReportGenerator" ref="timetableReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
    </bean>

    <bean id="dashboardEndpoint" class="com.embrate.cloud.data.server.dashboards.DashboardEndpoint">
        <constructor-arg name="dashboardManager" ref="dashboardManager"/>
    </bean>

    <bean id="bulkDocumentEndpoint" class="com.embrate.cloud.data.server.document.BulkDocumentEndpoint">
        <constructor-arg name="bulkDocumentManager" ref="bulkDocumentManager"/>
    </bean>

    <bean id="paymentDiscountEndPoint" class="com.embrate.cloud.data.server.payment.PaymentDiscountEndPoint">
        <constructor-arg name="paymentDiscountManager" ref="paymentDiscountManager"/>
    </bean>

    <bean id="leaveConfigurationEndpoint"
          class="com.embrate.cloud.data.server.leave.management.LeaveConfigurationEndpoint">
        <constructor-arg name="leaveConfigurationManager" ref="leaveConfigurationManager"/>
    </bean>

    <bean id="userLeavePolicyEndpoint"
          class="com.embrate.cloud.data.server.leave.management.UserLeavePolicyEndpoint">
        <constructor-arg name="userLeavePolicyManager" ref="userLeavePolicyManager"/>
    </bean>


    <bean id="userSalaryEndpoint"
          class="com.embrate.cloud.data.server.salary.UserSalaryEndpoint">
        <constructor-arg name="userSalaryManager" ref="userSalaryManager"/>
    </bean>

    <bean id="examConfigEndPoint"
          class="com.embrate.cloud.data.server.examination.ExamConfigEndPoint">
        <constructor-arg name="examConfigManager" ref="examConfigManager"/>
    </bean>

    <bean id="studentLeaveManagementEndpoint"
          class="com.embrate.cloud.data.server.leave.management.StudentLeaveManagementEndpoint">
        <constructor-arg name="studentLeaveManagementManager" ref="studentLeaveManagementManager"/>
        <constructor-arg name="studentLeaveReviewPushNotificationHandler"
                         ref="studentLeaveReviewPushNotificationHandler"/>
        <constructor-arg name="studentLeaveApplicationPushNotificationHandler" ref="studentLeaveApplicationPushNotificationHandler"/>
    </bean>

    <bean id="staffLeaveManagementEndpoint"
          class="com.embrate.cloud.data.server.leave.management.StaffLeaveManagementEndpoint">
        <constructor-arg name="staffLeaveManagementManager" ref="staffLeaveManagementManager"/>
        <constructor-arg name="staffLeaveApplicationPushNotificationHandler" ref="staffLeaveApplicationPushNotificationHandler"/>
        <constructor-arg name="staffLeaveReviewPushNotificationHandler"
                         ref="staffLeaveReviewPushNotificationHandler"/>
    </bean>

    <bean id="feeSetupUtilityEndPoint"
          class="com.embrate.cloud.data.server.fees.FeeSetupUtilityEndPoint">
        <constructor-arg name="feeSetupUtilityManager" ref="feeSetupUtilityManager"/>
    </bean>

    <bean id="communicationUtilityEndpoint"
          class="com.embrate.cloud.data.server.communication.utils.CommunicationUtilityEndpoint">
        <constructor-arg name="communicationUtilityManager" ref="communicationUtilityManager"/>
    </bean>

    <bean id="organisationReportEndpoint"
          class="com.embrate.cloud.data.server.organisation.OrganisationReportEndpoint">
        <constructor-arg name="organisationReportGenerator" ref="organisationReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
    </bean>

    <bean id="transportSetupUtilityEndPoint"
          class="com.embrate.cloud.data.server.transport.TransportSetupUtilityEndPoint">
        <constructor-arg name="transportSetupUtilityManager" ref="transportSetupUtilityManager"/>
    </bean>

    <bean id="appointmentDetailsEndpoint" class="com.lernen.cloud.data.server.appointment.AppointmentDetailsEndpoint">
        <constructor-arg name="appointmentDetailsManager" ref="appointmentDetailsManager"/>
        <constructor-arg name="appointmentDetailsAddPushNotificationHandler" ref="appointmentDetailsAddPushNotificationHandler"/>
        <constructor-arg name="appointmentStatusUpdatePushNotificationHandler" ref="appointmentStatusUpdatePushNotificationHandler"/>
    </bean>

    <bean id="visitorDetailsEndPoint" class="com.lernen.cloud.data.server.visitor.VisitorDetailsEndPoint">
        <constructor-arg name="visitorDetailsManager" ref="visitorDetailsManager"/>
        <constructor-arg name="visitorDetailsAddPushNotificationHandler" ref="visitorDetailsAddPushNotificationHandler"/>
        <constructor-arg name="visitorStatusUpdatePushNotificationHandler" ref="visitorStatusUpdatePushNotificationHandler"/>
        <constructor-arg name="visitorIdentityCardHandler" ref="visitorIdentityCardHandler"/>
    </bean>

    <bean id="transportAttendanceEndpoint" class="com.lernen.cloud.data.server.transport.TransportAttendanceEndpoint">
        <constructor-arg name="transportAttendanceManager" ref="transportAttendanceManager"/>
    </bean>


    <bean id="enquiryDetailsEndpoint" class="com.lernen.cloud.data.server.enquiry.EnquiryDetailsEndpoint">
        <constructor-arg name="enquiryDetailsManager" ref="enquiryDetailsManager"/>
    </bean>

    <bean id="hostelManagementEndPoint" class="com.embrate.cloud.data.server.hostel.management.HostelManagementEndPoint">
        <constructor-arg name="hostelManagementManager" ref="hostelManagementManager"/>
        <constructor-arg name="gatePassPushNotificationHandler" ref="gatePassPushNotificationHandler"/>
        <constructor-arg name="gatePassHandler" ref="gatePassHandler"/>
    </bean>

    <bean id="hpcConfigurationEndpoint" class="com.embrate.cloud.data.server.hpc.HPCConfigurationEndpoint">
        <constructor-arg name="hpcConfigurationManager" ref="hpcConfigurationManager" />
    </bean>

    <bean id="hpcFormEndpoint" class="com.embrate.cloud.data.server.hpc.HPCFormEndpoint">
        <constructor-arg name="hpcFormManager" ref="hpcFormManager" />
        <constructor-arg name="hpcPdfServiceProvider" ref="hpcPdfServiceProvider" />
        <constructor-arg name="hpcLambdaServiceProvider" ref="hpcLambdaServiceProvider" />
    </bean>

    <bean id="transportVehicleLogEndpoint" class="com.lernen.cloud.data.server.transport.TransportVehicleTransactionEndpoint">
        <constructor-arg name="transportVehicleTransactionManager" ref="transportVehicleTransactionManager" />
    </bean>

    <bean id="customWhatsappSender" class="com.embrate.cloud.data.server.whatsapp.CustomWhatsappSender">
        <constructor-arg name="whatsappSender" ref="webPayWhatsappSender"/>
        <constructor-arg name="customWhatsappHandler" ref="customWhatsappHandler"/>
        <constructor-arg name="whatsappManager" ref="whatsappManager"/>
        <constructor-arg name="notificationManager" ref="notificationManager"/>
    </bean>


    <bean id="pathRegistryInit" class="com.embrate.cloud.core.utils.prometheus.PathRegistryInit">
    </bean>

    <bean id="homeworkReportEndPoint" class="com.embrate.cloud.data.server.homework.HomeworkReportEndPoint">
        <constructor-arg name="homeworkReportGenerator" ref="homeworkReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
    </bean>

    <bean id="studentFinanceEndPoint" class="com.embrate.cloud.data.server.student.finance.StudentFinanceEndPoint">
        <constructor-arg name="studentFinanceManager" ref="studentFinanceManager"/>
    </bean>

    <bean id="admissionDashboardEndpoint" class="com.embrate.cloud.data.server.dashboards.admission.UserDashboardEndpoint">
        <constructor-arg name="userDashboardManager" ref="userDashboardManager"/>
    </bean>

    <bean id="attendanceDashboardEndpoint" class="com.embrate.cloud.data.server.dashboards.attendance.AttendanceDashboardEndpoint">
        <constructor-arg name="attendanceDashboardManager" ref="attendanceDashboardManager"/>
    </bean>

    <bean id="feesDashboardEndpoint" class="com.embrate.cloud.data.server.dashboards.fees.FeesDashboardEndpoint">
        <constructor-arg name="feesDashboardManager" ref="feesDashboardManager"/>
    </bean>

    <bean id="studentFinanceReportEndpoint"
          class="com.embrate.cloud.data.server.student.finance.StudentFinanceReportEndpoint">
        <constructor-arg name="studentFinanceReportGenerator" ref="studentFinanceReportGenerator"/>
        <constructor-arg name="pdfReportGenerator" ref="pdfReportGenerator"/>
        <constructor-arg name="excelReportGenerator" ref="excelReportGenerator"/>
    </bean>

</beans>
