package com.lernen.cloud.data.server.appointment;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.push.notifications.handler.AppointmentDetailsAddPushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.AppointmentStatusUpdatePushNotificationHandler;
import com.lernen.cloud.core.api.appointment.StudentAppointmentDetails;
import com.lernen.cloud.core.api.appointment.AppointmentDetailsPayload;
import com.lernen.cloud.core.api.appointment.AppointmentStatus;

import com.lernen.cloud.core.api.appointment.DatewiseAppointmentDetails;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.appointment.AppointmentDetailsManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;


@Path("/2.0/appointment-management")
public class AppointmentDetailsEndpoint {

    private static final Logger logger = LogManager.getLogger(AppointmentDetailsEndpoint.class);
    private final AppointmentDetailsManager appointmentDetailsManager;
    private final AppointmentDetailsAddPushNotificationHandler appointmentDetailsAddPushNotificationHandler;
    private final AppointmentStatusUpdatePushNotificationHandler appointmentStatusUpdatePushNotificationHandler;
    public AppointmentDetailsEndpoint(AppointmentDetailsManager appointmentDetailsManager, AppointmentDetailsAddPushNotificationHandler appointmentDetailsAddPushNotificationHandler, AppointmentStatusUpdatePushNotificationHandler appointmentStatusUpdatePushNotificationHandler) {
        this.appointmentDetailsManager = appointmentDetailsManager;
        this.appointmentDetailsAddPushNotificationHandler = appointmentDetailsAddPushNotificationHandler;
        this.appointmentStatusUpdatePushNotificationHandler = appointmentStatusUpdatePushNotificationHandler;
    }

    @POST
    @Path("appointment")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addAppointmentDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                          AppointmentDetailsPayload appointmentDetailsPayload) {
        final UUID appointmentId = appointmentDetailsManager.addAppointmentDetails(instituteId, userId, appointmentDetailsPayload);
        if (appointmentId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_APPOINTMENT_DETAILS, "Unable to add appointment details."));
        }
        appointmentDetailsAddPushNotificationHandler.sendAppointmentDetailsAddNotificationsAsync(instituteId, appointmentId, appointmentDetailsPayload.getAcademicSessionId(), userId);
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @PUT
    @Path("appointment-detail")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateAppointmentDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                             AppointmentDetailsPayload appointmentDetailsPayload) {
        final boolean updateAppointment = appointmentDetailsManager.updateAppointmentDetails(instituteId, userId, appointmentDetailsPayload);
        if (!updateAppointment) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_APPOINTMENT_DETAILS, "Unable to update appointment details."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("appointment-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAppointmentDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                          @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_type") UserType userType, @QueryParam("appointment_status_str") String appointmentStatusSetStr, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
        Set<AppointmentStatus> appointmentStatusSet = AppointmentStatus.getAppointmentStatusSet(appointmentStatusSetStr);
        final List<StudentAppointmentDetails> studentAppointmentDetailsList = appointmentDetailsManager.getAppointmentDetails(instituteId, userId, academicSessionId, userType, appointmentStatusSet, limit, offset);
        return Response.status(Response.Status.OK.getStatusCode()).entity(studentAppointmentDetailsList).build();
    }

    @GET
    @Path("appointment-details-with-pagination")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAppointmentDetailsPagination(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                    @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_type") UserType userType, @QueryParam("appointment_status_str") String appointmentStatusSetStr, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
        Set<AppointmentStatus> appointmentStatusSet = AppointmentStatus.getAppointmentStatusSet(appointmentStatusSetStr);
        final SearchResultWithPagination<StudentAppointmentDetails> appointmentDetailsList = appointmentDetailsManager.getAppointmentDetailsWithPagination(instituteId, userId, academicSessionId, userType, appointmentStatusSet, limit, offset);
        return Response.status(Response.Status.OK.getStatusCode()).entity(appointmentDetailsList).build();
    }

    @GET
    @Path("datewise-appointment-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAppointmentDetailsDatewise(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_type") UserType userType, @QueryParam("appointment_status_str") String appointmentStatusSetStr, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
        Set<AppointmentStatus> appointmentStatusSet = AppointmentStatus.getAppointmentStatusSet(appointmentStatusSetStr);
        List<DatewiseAppointmentDetails> appointmentDetailsList = appointmentDetailsManager.getAppointmentDetailsDatewise(instituteId, userId, academicSessionId, userType, appointmentStatusSet, limit, offset);
        return Response.status(Response.Status.OK.getStatusCode()).entity(appointmentDetailsList).build();
    }

    @GET
    @Path("appointment-detail/{appointment_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAppointmentDetailsById(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                              @QueryParam("academic_session_id") int academicSessionId, @PathParam("appointment_id") UUID appointmentId) {
        final StudentAppointmentDetails studentAppointmentDetails = appointmentDetailsManager.getAppointmentDetailsByAppointmentId(instituteId, userId, academicSessionId, appointmentId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(studentAppointmentDetails).build();
    }

    @PUT
    @Path("appointment-status/{appointment_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateAppointmentStatus(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId,
                                            @PathParam("appointment_id") UUID appointmentId, @QueryParam("appointment_status") AppointmentStatus appointmentStatus, @QueryParam("user_type") UserType userType, @QueryParam("reason") String reason) {
        final boolean updateAppointmentStatus = appointmentDetailsManager.updateAppointmentStatus(instituteId, userId, academicSessionId, appointmentId,
                appointmentStatus, userType, reason);
        if (!updateAppointmentStatus) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to update appointment status."));
        }

        //Notification not send when appointment is deleted
        if(appointmentStatus != AppointmentStatus.DELETED) {
            appointmentStatusUpdatePushNotificationHandler.sendAppointmentStatusUpdateNotificationsAsync(instituteId, appointmentId, academicSessionId, userId);
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("appointment-details-by-status")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAppointmentDetailsByStatus(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                    @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_type") UserType userType) {
        final Map<AppointmentStatus, List<StudentAppointmentDetails>> appointmentDetailsByStatusMap = appointmentDetailsManager.getAppointmentDetailsByStatus(instituteId, userId, academicSessionId, userType);
        return Response.status(Response.Status.OK.getStatusCode()).entity(appointmentDetailsByStatusMap).build();
    }

}

