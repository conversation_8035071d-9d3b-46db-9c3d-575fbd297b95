package com.lernen.cloud.data.server.examination;

import com.embrate.cloud.core.api.examination.utility.ExamReportFiltrationCriteria;
import com.embrate.cloud.pdf.exam.document.studentmarks.StudentMarksFeedDocumentHandler;
import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportType;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.examination.ExamGraphicalReportGenerator;
import com.lernen.cloud.core.lib.examination.ExamReportGenerator;
import com.lernen.cloud.core.lib.examination.documents.ExaminationDocuments;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */

@Path("/2.0/examination-reports/")
public class ExaminationReportEndPoint {
	private static final Logger logger = LogManager.getLogger(ExaminationReportEndPoint.class);

	private final ExamReportGenerator examReportGenerator;
	private final StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler;
	private final ExcelReportGenerator excelReportGenerator;
	private final PdfReportGenerator pdfReportGenerator;
	private final ExamGraphicalReportGenerator examGraphicalReportGenerator;
	private final ExaminationDocuments examinationDocuments;

	public ExaminationReportEndPoint(ExamReportGenerator examReportGenerator,
									 StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler,
									 ExcelReportGenerator excelReportGenerator,
									 PdfReportGenerator pdfReportGenerator,
									 ExamGraphicalReportGenerator examGraphicalReportGenerator,
									 ExaminationDocuments examinationDocuments) {
		this.examReportGenerator = examReportGenerator;
		this.studentMarksFeedDocumentHandler = studentMarksFeedDocumentHandler;
		this.excelReportGenerator = excelReportGenerator;
		this.pdfReportGenerator = pdfReportGenerator;
		this.examGraphicalReportGenerator = examGraphicalReportGenerator;
		this.examinationDocuments = examinationDocuments;
	}

	@GET
	@Path("headers/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportHeader(@PathParam("report_type") ExamReportType reportType) {
		if (reportType == null) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.entity(examReportGenerator.getReportHeader(reportType)).build();
	}

	@GET
	@Path("headers")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllReportHeader() {
		return Response.status(Response.Status.OK.getStatusCode()).entity(examReportGenerator.getReportHeader())
				.build();
	}


	@GET
	@Path("{institute_id}/view-report/exam/{exam_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getExamReportViewData(@PathParam("institute_id") int instituteId, @PathParam("exam_id") UUID examId,
										  @QueryParam("section_id_str") String classSectionIdStr,
										  @QueryParam("exclude_coscholastic_subjects") boolean excludeCoScholasticSubjects,
										  @QueryParam("show_dimensions") boolean showDimensions,
										  @QueryParam("show_total_column_dimension") boolean showTotalColumnDimension,
										  @QueryParam("show_coscholastic_grade") boolean showCoScholasticGrade,
										  @QueryParam("show_scholastic_grade") boolean showScholasticGrade,
										  @QueryParam("requiredHeaders") String requiredHeaders,
										  @QueryParam("download_format") DownloadFormat downloadFormat,
										  @QueryParam("reportType") ExamReportType reportType,
										  @QueryParam("user_id") UUID userId) {

		final ReportDetails reportDetails = examReportGenerator.getClassExamReport(instituteId, examId, classSectionIdStr,
				excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, showCoScholasticGrade,
				showScholasticGrade, requiredHeaders, reportType, downloadFormat, userId);

		if(reportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

	}

	@GET
	@Path("{institute_id}/exam/{exam_id}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response getExamReport(@PathParam("institute_id") int instituteId, @PathParam("exam_id") UUID examId,
								  @QueryParam("section_id_str") String classSectionIdStr,
								  @QueryParam("exclude_coscholastic_subjects") boolean excludeCoScholasticSubjects,
								  @QueryParam("show_dimensions") boolean showDimensions,
								  @QueryParam("show_total_column_dimension") boolean showTotalColumnDimension,
								  @QueryParam("show_coscholastic_grade") boolean showCoScholasticGrade,
								  @QueryParam("show_scholastic_grade") boolean showScholasticGrade,
								  @QueryParam("requiredHeaders") String requiredHeaders,
								  @QueryParam("reportType") ExamReportType reportType,
								  @QueryParam("download_format") DownloadFormat downloadFormat,
								  @QueryParam("user_id") UUID userId) throws IOException {

		final ReportDetails reportDetails = examReportGenerator.getClassExamReport(instituteId, examId, classSectionIdStr,
				excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, showCoScholasticGrade,
				showScholasticGrade, requiredHeaders, reportType, downloadFormat, userId);

		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.EXAMINATION);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}

	@GET
	@Path("marks-feed-document/{exam_id}/pdf")
	@Produces("application/pdf")
	public Response getStudentMarksReport(@PathParam("exam_id") UUID examId, @QueryParam("course_id") UUID courseId,
										  @QueryParam("institute_id") int instituteId, @QueryParam("section_id") Integer sectionId,
										  @QueryParam("single_column_student_count") int singleColumnStudentCount,
										  @QueryParam("user_id") UUID userId, @QueryParam("add_relieved_students") boolean addRelievedStudents,
										  @QueryParam("academic_session_id") int academicSessionId,
										  @QueryParam("standard_id") UUID standardId, @QueryParam("student_sorting_parameters") StudentSortingParameters studentSortingParameters) {

		final DocumentOutput documentOutput = studentMarksFeedDocumentHandler.generateStudentMarksFeedDocument(
				instituteId, academicSessionId, examId, courseId, standardId, sectionId, userId, singleColumnStudentCount, addRelievedStudents, studentSortingParameters);
		if (documentOutput == null) {
			logger.error("Null report output for instituteId {}, examId {}", instituteId, examId);
			return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
		}
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{standard_id}/view-report/course/{exam_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getCourseReportViewData(@PathParam("standard_id") UUID standardId, @PathParam("exam_id") UUID examId,
											@QueryParam("institute_id") int instituteId,
											@QueryParam("academic_session_id") Integer academicSessionId,
											@QueryParam("section_id") Integer sectionId,
											@QueryParam("exclude_coscholastic_subjects") boolean excludeCoScholasticSubjects,
											@QueryParam("show_dimensions") boolean showDimensions,
											@QueryParam("show_total_column_dimension") boolean showTotalColumnDimension,
											@QueryParam("download_format") DownloadFormat downloadFormat,
											@QueryParam("user_id") UUID userId) {

		final ReportDetails reportDetails = examReportGenerator.generateExamConsolidatedReport(instituteId, academicSessionId, standardId, sectionId,
				examId, excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, downloadFormat, userId);

		if(reportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

	}

	@GET
	@Path("{standard_id}/course/{exam_id}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response getCourseReport(@PathParam("standard_id") UUID standardId, @PathParam("exam_id") UUID examId,
									@QueryParam("institute_id") int instituteId,
									@QueryParam("academic_session_id") Integer academicSessionId,
									@QueryParam("section_id") Integer sectionId,
									@QueryParam("exclude_coscholastic_subjects") boolean excludeCoScholasticSubjects,
									@QueryParam("show_dimensions") boolean showDimensions,
									@QueryParam("show_total_column_dimension") boolean showTotalColumnDimension,
									@QueryParam("download_format") DownloadFormat downloadFormat,
									@QueryParam("user_id") UUID userId) throws IOException {

		final ReportDetails reportDetails = examReportGenerator.generateExamConsolidatedReport(instituteId, academicSessionId, standardId, sectionId,
				examId, excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, downloadFormat, userId);

		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.EXAMINATION);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}

	@POST
	@Path("{institute_id}/view-report/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportViewData(@PathParam("institute_id") int instituteId, @PathParam("report_type") ExamReportType reportType,
									  @QueryParam("user_id") UUID userId, ExamReportFiltrationCriteria examReportFiltrationCriteria) {

		final ReportDetails reportDetails = examReportGenerator.generateReport(instituteId,
				reportType, userId, examReportFiltrationCriteria);

		if(reportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

	}

	@POST
	@Path("{institute_id}/generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId, @PathParam("report_type") ExamReportType reportType,
								   @QueryParam("user_id") UUID userId, ExamReportFiltrationCriteria examReportFiltrationCriteria) throws IOException {

		final ReportDetails reportDetails = examReportGenerator.generateReport(instituteId,
				reportType, userId, examReportFiltrationCriteria);
		DownloadFormat downloadFormat = examReportFiltrationCriteria.getDownloadFormat();
		if(downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.EXAMINATION);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}

	@POST
	@Path("{institute_id}/view-graphical-report/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getGraphicalReportViewData(@PathParam("institute_id") int instituteId, @PathParam("report_type") ExamReportType reportType,
									  @QueryParam("user_id") UUID userId, ExamReportFiltrationCriteria examReportFiltrationCriteria) {

		final GraphicalReportDetails graphicalReportDetails = examGraphicalReportGenerator.generateGraphicalReport(instituteId,
				reportType, userId, examReportFiltrationCriteria);

		if(graphicalReportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(graphicalReportDetails).build();

	}
}