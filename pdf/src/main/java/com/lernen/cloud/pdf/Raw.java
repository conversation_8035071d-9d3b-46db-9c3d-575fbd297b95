package com.lernen.cloud.pdf;

import com.embrate.cloud.core.api.homework.HomeworkReportType;
import com.embrate.cloud.core.lib.homework.HomeworkReportGenerator;
import com.embrate.cloud.core.lib.student.finance.StudentFinanceReportGenerator;
import com.embrate.cloud.pdf.fees.FeeChallaHandler;
import com.embrate.cloud.pdf.inventory.store.v2.StoreInventoryPDFInvoiceHandler;
import com.embrate.cloud.pdf.reports.PDFReportGeneratorV2;
import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceReportType;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.documents.DocumentOutput;

import com.lernen.cloud.core.api.fees.FeeReportDataType;
import com.lernen.cloud.core.api.fees.FeesReportType;
import com.lernen.cloud.core.api.fees.payment.FeePaymentStatus;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportRowColumnDataVisibility;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.FilterationCriteria;
import com.lernen.cloud.core.api.student.StudentDocumentStatus;
import com.lernen.cloud.core.api.student.StudentReportType;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.transport.TransportReportType;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceReportGenerator;
import com.lernen.cloud.core.lib.examination.ExamReportGenerator;
import com.lernen.cloud.core.lib.examination.documents.ExaminationDocuments;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.lib.fees.payment.FeeReportDetailsGenerator;
import com.lernen.cloud.core.lib.reports.transport.TransportReportsGenerator;
import com.lernen.cloud.core.lib.student.StudentReportsGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import com.lernen.cloud.pdf.base.PDFGenerator;
import com.lernen.cloud.pdf.certificates.transfer.TransferCertificateHandler;
import com.lernen.cloud.pdf.exam.reports.ExamReportSerivceProvider;
import com.lernen.cloud.pdf.identitycard.staff.StaffIdentityCardHandler;
import com.lernen.cloud.pdf.identitycard.student.StudentIdentityCardHandler;
import com.lernen.cloud.pdf.invoice.fee.FeeInvoiceHandler;
import com.lernen.cloud.pdf.timetable.TimetableHandler;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import javax.ws.rs.core.Response;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class Raw {

	public static final String sourceFolder = "/Users/<USER>/Desktop/Embrate/OutputPDF/PDF.pdf";
	public static final String sourceFolder1 = "/Users/<USER>/Embrate/outputPdfHTML/exam_report_card.pdf";
	public static final String sourceFolderReport = "/Users/<USER>/Embrate/outputPdfHTML/invoice.pdf";
	public static final String DEST1 = "/Users/<USER>/Embrate/outputPdfHTML/report12.pdf";
	public static final String DEST2 = "C:\\Users\\<USER>\\Documents\\Embrate\\outputPdfHTML/reportcard.pdf";
	public static final String DEST11 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/reportcard10360.pdf";
	public static final String DEST15 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/reportcard10320.pdf";
	public static final String DEST12 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/IDCard101.pdf";
	public static final String DEST13 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/staffIDCard101.pdf";
	public static final String DEST16 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/TC.pdf";
	public static final String DEST6 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/gatepass.pdf";
	public static final String DEST7 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/IDCard180.pdf";
	public static final String DEST9 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/Bonafide.pdf";
	public static final String DEST8 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/AdmitCard10295.pdf";
	public static final String DEST10 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/TuitionFee.pdf";
	public static final String DEST18 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/GlobalMoneyReceiptwithBankcopy.pdf";
	public static final String DEST3 = "C:\\Users\\<USER>\\OneDrive\\Desktop\\Embrate\\outputPdfHTML/report4.pdf";
	public static final String DEST4 = "/F:\\Embrate2\\outputPdfHTML/report30.pdf";
	public static final String DEST5 = "/mnt/B018FE7718FE3BC2/Embrate/outputPdfHTML/idcard.pdf";


	public static final String IMAGE_LOGO = "/Users/<USER>/Lernen/lernen-frontend/lernen/core/static/core/images/balaji_convent_logo.png";
	public static final float[] FEE_HEAD_HEADER_WIDTH = {0.06f, 0.5f, 0.2f, 0.2f};
	public final PdfFont regular;
	public final PdfFont bold;

	public Raw() throws IOException {
		this.regular = PdfFontFactory.createFont(StandardFonts.TIMES_ROMAN);
		this.bold = PdfFontFactory.createFont(StandardFonts.TIMES_BOLD);
	}

	//    http://127.0.0.1:8000/********-f58a-4b15-9005-e80c604962b5/examination/generate-reports/SUBJECT_WISE_RANK_REPORT?academic_session_id=110&standard_id=9cb5ed05-dffd-4e06-94bc-31a9d515e126&section_id=&exam_id=fbdda064-3846-490c-966c-948ae31805af&course_type=&course_id=&staffId=&reportTypeName=&sectionIdsStr=&examIdsStr=fbdda064-3846-490c-966c-948ae31805af&courseIdsStr=&compareCumulativeExamIdsStr=&rankTill=1&exclude_coscholastic_subjects=false&requiredHeaders=&sort_student_on_rank=false&additionalCourseIdsStr=&scholasticMarksDisplayTypeStr=&coScholasticMarksDisplayTypeStr=&show_class_average_details=false&show_staff_details=false&downloadReport=true&downloadFormat=PDF
	public static void main(String[] args) throws Exception {

		Raw raw = new Raw();
		final ApplicationContext context = new ClassPathXmlApplicationContext("pdf.xml");


		/* FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
		StudentFinanceReportGenerator studentFinanceReportGenerator = context.getBean(StudentFinanceReportGenerator.class);
//		final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(110, "STAFF_ATTENDANCE_DETAILS_IN_A_DAY", "", "", "ONBOARD", **********, **********, UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), DownloadFormat.PDF);


		final ReportDetails reportDetails = studentFinanceReportGenerator.generateReport(
				101, 317, "STUDENT_FINANCE_DETAILS_REPORT",
				UUID.fromString("c4e40026-9bb2-492e-81bd-9d65d25d1200"),
				1748736000, 1750464000, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), DownloadFormat.PDF);


		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STUDENT_FINANCE);
		ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST18))) {
			byteArrayOutputStream.writeTo(outputStream);
		} */
//		final TransportReportsGenerator transportReportsGenerator = context.getBean(TransportReportsGenerator.class);
		final PDFReportGeneratorV2 pdfReportGenerator = context.getBean(PDFReportGeneratorV2.class);
////
//////		/data-server/2.0/transport/reports/10030/view-report/STUDENT_TRANSPORT_FEES_LEVEL_DUES?academic_session_id=32&requiredHeaders=serial_number,student_admission_number,student_name,student_status,student_class_name,student_father_name,student_primary_contact_number,father_contact_number,mother_contact_number,transport_pickup_route_name,transport_drop_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_fees_name,transport_route_area_assigned_amount,transport_route_area_amount_collected,transport_route_area_given_discount,transport_route_area_discount_to_be_given,transport_route_area_due_amount,transport_paid_fine_amount,transport_due_fine_amount&requiredStandards=&fee_ids=&only_enrolled_students=false&parent_area_ids=&area_ids=&pickup_ids=&drop_ids=&user_id=2a3a725b-163a-4dcc-a1f6-8fe5c1377262
////
////>>>>>>> 23ffb529d31b4dde429748e26af706f0f809b9b5
//		int instituteId = 110;
//		int academicSessionId = 255;
//		TransportReportType transportReportType = TransportReportType.STOPPAGE_FEES_CHART;
//		Integer start = -1;;
//		Integer end = -1;
//		String requiredHeaders = "serial_number,student_admission_number,student_name,student_status,student_class_name,student_father_name,student_primary_contact_number,transport_pickup_route_name,transport_drop_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_fees_name,transport_route_area_assigned_amount,transport_route_area_amount_collected,transport_route_area_given_discount,transport_route_area_discount_to_be_given,transport_route_area_due_amount";
//		String requiredStandardsCSV = null;
//		String feeIdsStr = null;
//		boolean onlyEnrolledStudents = false;
//		String parentAreaIdsStr = null;
//		String areaIdStr = null;
//		String pickupIdsStr = null;
//		String dropIdsStr = null;
//		DownloadFormat downloadFormat = DownloadFormat.PDF;
//		UUID userId = UUID.fromString("2a3a725b-163a-4dcc-a1f6-8fe5c1377262");
//		ReportDetails reportDetails = transportReportsGenerator.generateReport(instituteId, academicSessionId,
//				transportReportType, start, end, requiredHeaders, requiredStandardsCSV, feeIdsStr, onlyEnrolledStudents, parentAreaIdsStr,
//				areaIdStr, pickupIdsStr, dropIdsStr, downloadFormat, userId);
////
//		DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.TRANSPORT);
////
//
		// Updated to use new ExaminationDocuments flow
		ExaminationDocuments examinationDocuments = context.getBean(ExaminationDocuments.class);
		UUID userId = UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"); // Sample user ID
		ReportDetails reportDetails = examinationDocuments.generateExamCourseMarksReport(10225,
				110, UUID.fromString("a211b728-2752-42ad-abc3-8c5decfef26a"), null,
				true, userId, DownloadFormat.PDF);

		DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.EXAMINATION);

//		c78382f7-3583-4498-89c3-0ab3e9460539
		//d98b918c-e111-4dc5-ad33-e8e43e286280
//
////		final UserCredentialsPDFGenerator userCredentialsPDFGenerator = context.getBean(UserCredentialsPDFGenerator.class);
////		DocumentOutput documentOutput = userCredentialsPDFGenerator.generateUserCredentialsPDF(101, 190, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), "User Credentials", UserType.ADMIN);
////
		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
		try (OutputStream outputStream = Files.newOutputStream(Paths.get(sourceFolderReport))) {
			byteArrayOutputStream.writeTo(outputStream);
		}
//	ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("ef6d4bbb-871e-4888-8d81-0db2909876d7"), "TERM_I", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
////
//////
//			ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//			try (OutputStream outputStream = new FileOutputStream(DEST15)) {
//				byteArrayOutputStream.writeTo(outputStream);
// 			}
//		FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//		StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//		final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(110, "STAFF_ATTENDANCE_DETAILS_IN_A_DAY", "", "", "ONBOARD", **********, **********, UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), DownloadFormat.PDF);
//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STAFF_ATTENDANCE);
//		ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//		int instituteId = 110;
//		int academicSessionId = 268;
//		FeesReportType reportType = FeesReportType.STUDENT_AGGREGATED_PAYMENT;
//		Integer start = -1;
//		Integer end = -1;
//		Integer feeDueDate = -1;
//		String feeIdStr = null;
//		String requiredStandards = null;
//		FeePaymentTransactionStatus feePaymentTransactionStatus = null;
//		String requiredHeaders = "sr_no,registration_no,admission_no,name,status,class,father_name,primary_contact,whatsapp_number,father_contact,mother_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount";
//		UUID userId = UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51");
//		DownloadFormat downloadFormat = DownloadFormat.PDF;
//		String studentStatusCSV = null;
//		String feeHeadIdsStr = null;
//		String multipleAcademicSessionIdsStr = null;
//		FeeReportDataType feeReportDataType = FeeReportDataType.ALL;
//		String transactionModeStr = null;
//		boolean hideStudentWithZeroFeesAssignment = true;
//		ReportRowColumnDataVisibility reportRowColumnDataVisibility = null;

//		final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
//				reportType, start, end, feeDueDate, feeIdStr, requiredStandards, feePaymentTransactionStatus,requiredHeaders,
//				userId, downloadFormat,studentStatusCSV,feeHeadIdsStr,multipleAcademicSessionIdsStr, feeReportDataType,
//				transactionModeStr, hideStudentWithZeroFeesAssignment, reportRowColumnDataVisibility);

//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.FEES);
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(sourceFolderReport))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}

//		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//		DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101,
//				UUID.fromString("0d6a9d06-2078-4530-b00d-a8e1f3106bdd"),
//				true, null);
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(sourceFolder))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		FeeChallaHandler feeChallaHandler = context.getBean(FeeChallaHandler.class);
//		TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
//		DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(10130,
//				UUID.fromString("f593ab9d-acf4-404b-96cc-d890b6c3f5e8"));

//		StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//
//		final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(110,
//				"STAFF_ATTENDANCE_DETAILS", "", "", "ONBOARD", **********, **********,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), DownloadFormat.PDF);
//		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//		System.out.println(htmlReportTable);


//		StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//
//
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(110, 255,
//				UUID.fromString("ed5a059c-3912-487c-9168-bbf48eb36a67"), "ANNUAL", true,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), true);
//
////		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STAFF_ATTENDANCE);

//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}
//		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//		StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//
//
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(110, 255,
//				UUID.fromString("ed5a059c-3912-487c-9168-bbf48eb36a67"), "ANNUAL", true,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), true);

//		HomeworkReportGenerator homeworkReportGenerator = context.getBean(HomeworkReportGenerator.class);
//		final ReportDetails reportDetails = homeworkReportGenerator.generateReport(110, 153,
//				null, null, null, DownloadFormat.PDF,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), HomeworkReportType.HOMEWORK_CONSOLIDATED_REPORT,
//				**********, **********, null, null);
//
//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.HOMEWORK_MANAGEMENT);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//				try (OutputStream outputStream = Files.newOutputStream(Paths.get(sourceFolder1))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//	}
//		TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
//		DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(10265,
//				UUID.fromString("347540ef-1c73-4145-8e2f-a1f0b4b0fed9"));
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//				try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}

//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), HPCExamType.TERM1, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10402, 286,
//				UUID.fromString("45be79e6-0f13-444c-a0c3-eb742bf15d9a"), "ANNUAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), true);
//
////
//
		/* final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10355, 237,
				UUID.fromString("b0c27953-2eac-4d0c-b167-2755a471815b"), "ANNUAL", true,
				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), true); */
////
////////
////
////		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
////		DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(110,
////				UUID.fromString("e17563db-14bb-4148-8011-00d263c525dd"),
////				true, null);
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//		StoreInventoryPDFInvoiceHandler storeInventoryPDFInvoiceHandler = context.getBean(StoreInventoryPDFInvoiceHandler.class);
//		DocumentOutput documentOutput = storeInventoryPDFInvoiceHandler.generateInvoice(110, UUID.fromString("7b8fe6c7-dda4-41a7-bb43-6f873e144f3f"), true, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10350, 235,
//				UUID.fromString("a5d229e5-fcd3-4e5c-972b-0a6662977a9a"), "ANNUAL", true,
//				UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"), true);
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}
//		 ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(110, 255,
//				UUID.fromString("ed5a059c-3912-487c-9168-bbf48eb36a67"), "ANNUAL", true,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), true);
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}

//		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//		DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101,
//				UUID.fromString("f1275067-b15e-49b2-87c7-f98712ad968a"),
//				true, null);
//		 ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}

		/* ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
				UUID.fromString("c4e40026-9bb2-492e-81bd-9d65d25d1200"), "I_TERMINAL", true,
>>>>>>> a635de0f23078d473e172c068a1fc699bb879277
				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
=======
//		TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
//		DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(101,
//				UUID.fromString("b2538495-29a1-4317-b092-e19313c1cf31"));
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//
//		 ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("c4e40026-9bb2-492e-81bd-9d65d25d1200"), "I_TERMINAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
>>>>>>> 7dd58b95cb05c9a3560705f5b75c282985989268
//

////
//			ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//			try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//				byteArrayOutputStream.writeTo(outputStream);
			}
//			final StaffIdentityCardHandler staffIdentityCardHandler = context.getBean(StaffIdentityCardHandler.class);
//			/* final DocumentOutput documentOutput = staffIdentityCardHandler.generateIdentityCard(101, UUID.fromString("78d3d7f2-7bdf-4616-924d-297d202ab07e"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//			       ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//					try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST13))) {
//						byteArrayOutputStream.writeTo(outputStream);
//					} */
//					final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//							final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(101,
//									256, UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"),
//									UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//					//
//							ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//							try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST7))) {
//					//
//								byteArrayOutputStream.writeTo(outputStream);
//							}
////							}
//		StoreInventoryPDFInvoiceHandler storeInventoryPDFInvoiceHandler = context.getBean(StoreInventoryPDFInvoiceHandler.class);
//		DocumentOutput documentOutput = storeInventoryPDFInvoiceHandler.generateInvoice(110, UUID.fromString("7b8fe6c7-dda4-41a7-bb43-6f873e144f3f"), true, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10350, 235,
//				UUID.fromString("a5d229e5-fcd3-4e5c-972b-0a6662977a9a"), "ANNUAL", true,
//				UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"), true);

		/* FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
		DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(10350,
				UUID.fromString("f5e98fed-6b26-4ae2-ac1a-eaccee96fd72"),
				true, null); */
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(sourceFolder))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}
//<<<<<<< HEAD
//	}
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}
//=======
//	}}
		/* ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
			byteArrayOutputStreamReport.writeTo(outputStream);
		} */
//>>>>>>> e625ad59902566c92f486100a4548ea5c248ec24
//	}}

//		Docume
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10350, 235,
//				UUID.fromString("a5d229e5-fcd3-4e5c-972b-0a6662977a9a"), "ANNUAL", true,
//				UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"), true);

//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}
//
//		TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
//		DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(101,
//                UUID.fromString("b2538495-29a1-4317-b092-e19313c1cf31"));
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		} }}

/* 		 final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
				 UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
				 UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
<<<<<<< HEAD
>>>>>>> aabe9efd2f003ed637e93ce07db6c84376e57d18
//
=======

>>>>>>> edd4596a36fb337f7809ecbc54a4b31f81b8e60e
		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
		DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(10390,
				UUID.fromString("11b68efc-c4d2-4309-870c-f6a0943c8352"),
				true, null);
//

		 ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
		 try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
			 byteArrayOutputStreamReport.writeTo(outputStream);
	 } */


// 		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);


//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10215, 226,
//				UUID.fromString("46dcf866-b93f-4654-aeee-6285b0a186f6"), "ANNUAL", true,
//				UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"), true);


//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}
//  		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//		ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(sourceFolder))) {
//			byteArrayOutputStreamReport.writeTo(outputStream);
//		}


//
//
//<<<<<<< HEAD
////		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//=======
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("c4e40026-9bb2-492e-81bd-9d65d25d1200"), "ANNUAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//
////
//			ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//			try (OutputStream outputStream = new FileOutputStream(DEST15)) {
//				byteArrayOutputStream.writeTo(outputStream);
//			}
//
//		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//>>>>>>> 1836fc8140eb9cab8ffc0631d6fd17fea1b8c6e0
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10295, 177,
//				UUID.fromString("0c25bb1d-ea50-4d80-a74b-c1f92cc972d1"), "ANNUAL", true,
//				UUID.fromString("9e19e819-ba37-4e12-b2e3-ff1e05269b2d"));

//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);}
//=======
////		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10275, 204,
//				UUID.fromString("cbd42513-8d8f-4ad2-aba3-3ecf69ee34dd"), "ANNUAL", true,
//				UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"));
//
//		 ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		 try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//			 byteArrayOutputStream.writeTo(outputStream);
//>>>>>>> 6a2f80b6df31ca679580c6841fc697c630daca35
//	 }

//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), HPCExamType.TERM1, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), UserType.STUDENT);
// 		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);

// 		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(101, 256,
// 				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), HPCExamType.TERM1, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), UserType.STUDENT);
//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(110, 153,
//				UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d"), HPCExamType.TERM2,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));


////		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);

//		HPCWebForm form = hpcFormManager.getHPCWebForm(110, 153,
//				UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d"), HPCExamType.TERM2);
//
//		System.out.println(form.getParentSectionFieldStatus());
//		System.out.println(form.getTeacherSectionFieldStatus());

//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(110, 153,
//				UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d"), HPCExamType.TERM2,
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));


//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);

//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10350, 235,
//				UUID.fromString("a5d229e5-fcd3-4e5c-972b-0a6662977a9a"), "ANNUAL", true,
//				UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"), true);

//          List<UUID> uuidList = new ArrayList<>();
//		  uuidList.add(UUID.fromString("68c0a852-e593-4c49-9427-cdcaed926115"));
//		  uuidList.add(UUID.fromString("0ce1abf0-3192-4887-a2b0-3f171eaf03c7"));
//		DocumentOutput documentOutput = examReportSerivceProvider.getClassExamReport(10275, 204,
//				UUID.fromString("fc96a559-d536-4db7-83e3-96f14d900671"), null,"ANNUAL", UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"), null, uuidList, "BULK");


//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10205, 203,
//				UUID.fromString("0f5abaa5-e2d6-4a58-9638-3ff71a77533c"), "ANNUAL", true,
//				UUID.fromString("e1982041-d931-4663-9f9e-e07fb6f92d55"),true);


//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10385, 261,
//				UUID.fromString("25a2bc91-a734-49ee-a2c3-4aa98e4f92de"), "ANNUAL", true,
//				UUID.fromString("246385e4-da71-498f-a691-6eeae8ebeaaf"), true);
//=======
//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10140, 227,
//				UUID.fromString("7fd0a195-bfb6-48c6-aa71-5fe5ec2d5274"), "ANNUAL", true,
//				UUID.fromString("bbaaf148-d90d-4341-9062-fb750b7d181f"),true);
//>>>>>>> f2b6d4791fd26a98c5c373d95ac17976cbf0bbae
////

//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolderReport)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//
//			byteArrayOutputStream.writeTo(outputStream);
//		}

//
//		881145b3-7366-4298-b080-9304acd8c91c

//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);}}}

//		HPCPdfServiceProvider hpcPdfServiceProvider = context.getBean(HPCPdfServiceProvider.class);
//		f4f7e7e5-935d-4994-94ca-fa0d2c0e72d5
//		1b982ee3-20cd-49a3-8811-426e1ae84b01
//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(10275, 204,

//				UUID.fromString("1b982ee3-20cd-49a3-8811-426e1ae84b01"), HPCExamType.TERM1, UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"));
////				final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
////				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "TERM_I", true,
////				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true);
////
//		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput.getContent();
//		try (OutputStream outputStream1 = new FileOutputStream(DEST2)) {
//			byteArrayOutputStream1.writeTo(outputStream1);
//		}
//		List<UUID> uuidList = new ArrayList<>();
//		uuidList.add(UUID.fromString("1b982ee3-20cd-49a3-8811-426e1ae84b01"));
//		uuidList.add(UUID.fromString("1e0ddee7-c36d-4380-bc7c-121b57ca6d07"));
//		uuidList.add(UUID.fromString("bae57b48-a31d-4bec-9a8c-69eef49ce17e"));
//		uuidList.add(UUID.fromString("8e585653-8ce5-44b2-881f-7439cca4ab95"));
//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getBulkStudentHPC(10275, 204,
//				uuidList, HPCExamType.TERM2, UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"));

//				final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true);

//		DocumentOutput documentOutput = feeChallaHandler.generateChallanDocument(101, 256, UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//
//
//		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput.getContent();
//		try (OutputStream outputStream1 = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream1.writeTo(outputStream1);
//		}
//		List<UUID> uuidList = new ArrayList<>();
// <<<<<<< PD-4688

// 				/* final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
// 				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
// 				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true); */

// //				final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
// //				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
// //				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true);
// =======
// 				final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
// 				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
// 				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true);
// >>>>>>> release

//
//		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput.getContent();
//		try (OutputStream outputStream1 = new FileOutputStream(DEST2)) {
//			byteArrayOutputStream1.writeTo(outputStream1);
//		}
		/* List<UUID> uuidList = new ArrayList<>();
>>>>>>> ebf5df6dc9b2be50210cc9314cfc83ef0641ffa1
//		uuidList.add(UUID.fromString("1b982ee3-20cd-49a3-8811-426e1ae84b01"));
//		uuidList.add(UUID.fromString("9ce9b415-0f03-481a-941c-5b8dd8270424"));
//		uuidList.add(UUID.fromString("c120abe6-8231-412d-83ca-a25ae29ececc"));
		uuidList.add(UUID.fromString("f4f7e7e5-935d-4994-94ca-fa0d2c0e72d5"));
		uuidList.add(UUID.fromString("cf25afeb-4aa4-4e2d-aeea-16b8823a668c"));
		uuidList.add(UUID.fromString("c1843f48-dbee-45db-beee-7cdd1a0d8b29")); */
//		List<UUID> uuidList = new ArrayList<>();
//		uuidList.add(UUID.fromString("1b982ee3-20cd-49a3-8811-426e1ae84b01"));
//		uuidList.add(UUID.fromString("9ce9b415-0f03-481a-941c-5b8dd8270424"));
//		uuidList.add(UUID.fromString("c120abe6-8231-412d-83ca-a25ae29ececc"));
//		uuidList.add(UUID.fromString("f4f7e7e5-935d-4994-94ca-fa0d2c0e72d5"));
//		uuidList.add(UUID.fromString("cf25afeb-4aa4-4e2d-aeea-16b8823a668c"));
//		uuidList.add(UUID.fromString("c1843f48-dbee-45db-beee-7cdd1a0d8b29"));
//		uuidList.add(UUID.fromString("8e585653-8ce5-44b2-881f-7439cca4ab95"));
//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getBulkStudentHPC(10275, 204,
//				uuidList, HPCExamType.TERM2, UUID.fromString("0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df"));
//				final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "TERM_I", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),true);

//		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput.getContent();
//		try (OutputStream outputStream1 = new FileOutputStream(DEST2)) {
//			byteArrayOutputStream1.writeTo(outputStream1);
//		}


//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10390, 262,
//				UUID.fromString("2e93c37e-8c03-434b-8129-e00f799c90b2"), "ANNUAL", true,
//				UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"), true);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}


//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10001, 142,
//				UUID.fromString("9e926c5f-9fbb-4eb3-8df1-84127135e14e"), "ANNUAL", true,
//				UUID.fromString("61c6759f-74d3-427c-851a-55b28698e04c"), true);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}


//	}
//		ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//		http://127.0.0.1:8000/6ed4092c-2f0b-40b1-8565-b6cff64cf797/examination/generate-reports/MULTIPLE_EXAM_MARKS_REPORT?academic_session_id=261&standard_id=b8cb7fd3-99ec-4008-a941-f2d6a8054640&section_id=&exam_id=69599288-ea0d-48c0-b392-829d574ead6a&course_type=&course_id=&staffId=&reportTypeName=&sectionIdsStr=&examIdsStr=69599288-ea0d-48c0-b392-829d574ead6a&courseIdsStr=&compareCumulativeExamIdsStr=&rankTill=&exclude_coscholastic_subjects=false&requiredHeaders=sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks&sort_student_on_rank=false&additionalCourseIdsStr=&scholasticMarksDisplayTypeStr=MARKS&coScholasticMarksDisplayTypeStr=MARKS&show_class_average_details=false&show_staff_details=false

//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10365, 243,
// <<<<<<< PD-4688
// //				UUID.fromString("5a6fae95-7883-4af0-8663-63dafdc99715"), "ANNUAL", true,
// //				UUID.fromString("72b07226-3011-4ee0-a19a-c39650ffeb23"), true);


// //		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
// //		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
// //			byteArrayOutputStream.writeTo(outputStream);
// //		}
// =======
// //				UUID.fromString("6d61822e-6e13-44a7-858e-803daa51cc9a"), "ANNUAL", true,
// //				UUID.fromString("72b07226-3011-4ee0-a19a-c39650ffeb23"), false);
// //
// 		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
// 		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
// 			byteArrayOutputStream.writeTo(outputStream);
// 		}
// >>>>>>> release


//	}
//}

//		AdmitCardHandler admitCardHandler = context.getBean(AdmitCardHandler.class);
//		HPCPdfServiceProvider hpcPdfServiceProvider = context.getBean(HPCPdfServiceProvider.class);
//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getStudentHPC(101, 190,
//				UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"), HPCExamType.TERM1, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//				final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 225,
//				UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"), "ANNUAL", true,
//				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),true);


//		final DocumentOutput documentOutput = admitCardHandler.generateAdmitCards(101, UUID.fromString("0a30eb8e-8cb9-431b-8a16-bb806c04da3a"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"),
//				AdmitCardType.ADMIT_CARD_WITH_DATESHEET, new ArrayList<>(Collections.singletonList(UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"))), 0)


//				final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(110, 255,
//						FeesReportType.STUDENT_FEES_HEAD_PAYMENT,
//						-1, -1, -1, null, null, null,
//						"sr_no,registration_no,admission_no,name,status,class,father_name,primary_contact,whatsapp_number,father_contact,mother_contact,fees_head_name,assigned_amount,amount_paid,given_discount,discount_to_be_given,due_amount",
//				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//						DownloadFormat.PDF,null,null,null,
//						FeeReportDataType.ALL,null,false,
//						ReportRowColumnDataVisibility.COLUMN_WISE);

//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);

//
//		final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//
//		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(10390,
//				262, UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), null,
//				null);


//		final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(10020,
//				UUID.fromString("8131dff8-4dde-4ced-b1b6-122c584db4b0"), true, null);


//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolderReport)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}


//		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//		DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101,
//				UUID.fromString("59b74d40-559f-499d-b7da-9b4ba04527f0"),
//				true, null);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(DEST18)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//
////		TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
//=======
//		TrackingEventPDFGenerator trackingEventPDFGenerator = context.getBean(TrackingEventPDFGenerator.class);
//		DocumentOutput documentOutput = trackingEventPDFGenerator.generateTrackingEventsPDF(101, "12/12/2024", "02/02/2025", "Tracking Event");
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//>>>>>>> 12ef72ad7ca985c63ce54642571dd30c39553acc
//		final DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(101,
//				UUID.fromString("e2608d93-b99c-435e-add6-b08dbbb113f0"));
//
//		final DocumentOutput documentOutput = hpcPdfServiceProvider.getBulkStudentHPC(110, 268,
//				Arrays.asList(
////						UUID.fromString("6db470ee-47e4-4ef5-8dd1-bbb7cda87703")
////						,
//						UUID.fromString("d12f3fa4-71e6-487e-bb71-646f64265e0f")
//				),
//				HPCExamType.TERM2, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//

//

//

//		/data-server/2.0/fee-payment/invoice/d4da65d1-7cae-412e-b6b3-e25d47f45961/pdf-summary?institute_id=10390&office_copy=True


//		WalletInvoiceHandler walletInvoiceHandler = context.getBean(WalletInvoiceHandler.class);
//		final DocumentOutput documentOutput = walletInvoiceHandler.generateWalletInvoice(
//				10390, UUID.fromString("a54fa5d0-b1c3-4dea-a3c0-eaa0682b6f3b"), null);
//
////		WalletInvoiceHandler walletInvoiceHandler = context.getBean(WalletInvoiceHandler.class);
////		final DocumentOutput documentOutput = walletInvoiceHandler.generateWalletInvoice(
////				10390, UUID.fromString("a54fa5d0-b1c3-4dea-a3c0-eaa0682b6f3b"), null);
//

//		final DocumentOutput documentOutput1 = hpcPdfServiceProvider.getBulkStudentHPC(110, 268,
//				Arrays.asList(
////						UUID.fromString("6db470ee-47e4-4ef5-8dd1-bbb7cda87703")
////						,
//						UUID.fromString("d12f3fa4-71e6-487e-bb71-646f64265e0f")
//				),
//				HPCExamType.TERM1, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//
//		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput1.getContent();
//		try (OutputStream outputStream1 = new FileOutputStream(sourceFolder1)) {
//			byteArrayOutputStream1.writeTo(outputStream1);
//		}

//		ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);

//		String json = "{\"academicSessionId\": \"110\", \"standardId\": \"9cb5ed05-dffd-4e06-94bc-31a9d515e126\", \"sectionId\": null, \"examId\": \"fbdda064-3846-490c-966c-948ae31805af\", \"courseId\": null, \"courseType\": null, \"downloadFormat\": \"PDF\", \"reportCardType\": \"\", \"staffId\": \"\", \"sectionIdSet\": null, \"examIdSet\": [\"fbdda064-3846-490c-966c-948ae31805af\"], \"courseIdSet\": null, \"compareCummulativeWithExamIdSet\": null, \"rankTill\": \"1\", \"excludeCoScholasticSubjects\": \"false\", \"requiredHeaders\": \"\", \"sortStudentOnRank\": \"false\", \"additionalCoursesSet\": null, \"scholasticMarksDisplayTypeSet\": null, \"coScholasticMarksDisplayTypeSet\": null, \"showClassAverageDetails\": \"false\", \"showStaffDetails\": \"false\"}";
//		String json = "{\"academicSessionId\": \"110\", \"standardId\": \"9cb5ed05-dffd-4e06-94bc-31a9d515e126\", \"sectionId\": null, \"examId\": \"fbdda064-3846-490c-966c-948ae31805af\", \"courseId\": null, \"courseType\": null, \"downloadFormat\": \"PDF\", \"reportCardType\": \"\", \"staffId\": \"\", \"sectionIdSet\": null, \"examIdSet\": [\"fbdda064-3846-490c-966c-948ae31805af\"], \"courseIdSet\": null, \"compareCummulativeWithExamIdSet\": [\"64958be0-cdee-4c37-ab1d-42334c79fce3\", \"d53d2b0d-a775-4834-99f8-af042b653dbe\"], \"rankTill\": \"1\", \"excludeCoScholasticSubjects\": \"false\", \"requiredHeaders\": \"\", \"sortStudentOnRank\": \"false\", \"additionalCoursesSet\": null, \"scholasticMarksDisplayTypeSet\": null, \"coScholasticMarksDisplayTypeSet\": null, \"showClassAverageDetails\": \"false\", \"showStaffDetails\": \"false\"}";
//

//		String json = "{\"instituteId\": 110, \"academicSessionId\": 255, \"studentAcademisSessionParametersList\": [\"ROLL_NUMBER\"], \"studentAcademicDetailsList\": [{\"studentId\": \"00afd901-01f4-450e-aef3-31ec45099d02\", \"rollNumber\": \"STUID1\"}, {\"studentId\": \"01821281-8b6b-4c1c-af7f-dba5b01ae4ba\", \"rollNumber\": \"STUID2\"}, {\"studentId\": \"06a02299-013f-4dfc-93b5-342ba87f5c78\", \"rollNumber\": \"STUID3\"}, {\"studentId\": \"06ca73c8-0663-4cae-b01b-9c0779dee9d8\", \"rollNumber\": \"STUID4\"}, {\"studentId\": \"07f56df2-f04e-43b9-a453-cbbccb6a84e2\", \"rollNumber\": \"STUID5\"}, {\"studentId\": \"08eb830c-45dc-43a2-bc96-d14334d07977\", \"rollNumber\": \"STUID6\"}, {\"studentId\": \"09b9d973-cf66-4a67-834c-51a96aadd33e\", \"rollNumber\": \"STUID7\"}, {\"studentId\": \"0ab83126-47bc-43e0-8210-5648be2353cc\", \"rollNumber\": \"STUID8\"}, {\"studentId\": \"0b3f0f4c-86cc-426c-88e0-0c4cae8a8793\", \"rollNumber\": \"STUID9\"}, {\"studentId\": \"0f6a88fc-3b0d-4013-ad5d-e0a884f86f41\", \"rollNumber\": \"STUID10\"}, {\"studentId\": \"101cebf8-ab78-4d0b-a4d7-da8eaf683b7e\", \"rollNumber\": \"STUID11\"}, {\"studentId\": \"104d7ca0-4287-4c27-9e6f-0c044d00fa49\", \"rollNumber\": \"STUID12\"}, {\"studentId\": \"10fd11b9-a95a-4275-be68-9c51300617f9\", \"rollNumber\": \"STUID13\"}, {\"studentId\": \"1164295c-14d5-4cf6-bf60-a166463ed758\", \"rollNumber\": \"STUID14\"}, {\"studentId\": \"132df7b6-db87-4ca2-8cf9-1ff314ba26a0\", \"rollNumber\": \"STUID15\"}, {\"studentId\": \"13ad413e-8c2c-4584-82bd-a966807b5d58\", \"rollNumber\": \"STUID16\"}, {\"studentId\": \"1525cd68-fcba-45b7-858b-785cb80ec1f9\", \"rollNumber\": \"STUID17\"}, {\"studentId\": \"1762e0dc-285f-4e06-b602-ff725dbad86d\", \"rollNumber\": \"STUID18\"}, {\"studentId\": \"18ca658e-c395-4614-bf2e-f22186983e72\", \"rollNumber\": \"STUID19\"}, {\"studentId\": \"18fd7074-7bc5-442a-a530-e59e99762759\", \"rollNumber\": \"STUID20\"}, {\"studentId\": \"19d25716-79c5-4026-8229-0637d6fdaf98\", \"rollNumber\": \"STUID21\"}, {\"studentId\": \"1fc19128-08b2-4349-bd14-47092d31ee24\", \"rollNumber\": \"STUID22\"}, {\"studentId\": \"20e97db7-dd2c-41fe-b1da-5244b16747f2\", \"rollNumber\": \"STUID23\"}, {\"studentId\": \"223b832c-bb8b-4303-a633-9521d2c9643c\", \"rollNumber\": \"STUID24\"}, {\"studentId\": \"22cd0927-74bb-41f5-9a3d-065e428e6042\", \"rollNumber\": \"STUID25\"}, {\"studentId\": \"283ca52f-a238-435a-8511-9f957fb85b98\", \"rollNumber\": \"STUID26\"}, {\"studentId\": \"2bf15a17-502a-4380-be19-4b92cd6a95d5\", \"rollNumber\": \"STUID27\"}, {\"studentId\": \"309f0487-4700-4309-91e6-ad156b0db664\", \"rollNumber\": \"STUID28\"}, {\"studentId\": \"325bb1e6-826c-49b7-96cc-dd4f7275829f\", \"rollNumber\": \"STUID29\"}, {\"studentId\": \"37bb5c06-37d8-4378-b273-32a40b76ec57\", \"rollNumber\": \"STUID30\"}, {\"studentId\": \"390f7f2b-e150-48db-bec9-4e81c43b8718\", \"rollNumber\": \"STUID31\"}, {\"studentId\": \"3da28293-3def-4221-94ff-cc967333efcf\", \"rollNumber\": \"STUID32\"}, {\"studentId\": \"414d2699-092c-4cdb-b189-2ea68b4ca666\", \"rollNumber\": \"STUID33\"}, {\"studentId\": \"44fcef60-6b25-4098-abb2-78a2721f3a78\", \"rollNumber\": \"STUID34\"}, {\"studentId\": \"48ff1961-6d14-4ee1-885d-30560d373b07\", \"rollNumber\": \"STUID35\"}, {\"studentId\": \"49f102b9-8e26-468f-a991-95143f3823ab\", \"rollNumber\": \"STUID36\"}, {\"studentId\": \"501dabcb-f307-47b0-9e29-cad879cd29c5\", \"rollNumber\": \"STUID37\"}, {\"studentId\": \"5051d2a6-e9b3-41e5-9852-67e567e56888\", \"rollNumber\": \"STUID38\"}, {\"studentId\": \"50ed95c9-c24e-4787-813a-d77a884e2203\", \"rollNumber\": \"STUID39\"}, {\"studentId\": \"53730b37-0217-427f-8ecc-68e12a0380b9\", \"rollNumber\": \"STUID40\"}, {\"studentId\": \"55fbc861-2d01-4e9e-b6ed-a13f6ed532cc\", \"rollNumber\": \"STUID41\"}, {\"studentId\": \"5746bbaa-cfaa-4813-ac70-44b28899de32\", \"rollNumber\": \"STUID42\"}, {\"studentId\": \"5e1122c0-8afd-4f65-8538-bc4314467aa3\", \"rollNumber\": \"STUID43\"}, {\"studentId\": \"61f614c6-5dd5-4ba2-a173-68efac1ce8f6\", \"rollNumber\": \"STUID44\"}, {\"studentId\": \"628911d3-1beb-442b-a83b-c05dba91f8c7\", \"rollNumber\": \"STUID45\"}, {\"studentId\": \"654f7bf3-f7a0-4ba7-b7e0-0320fc921d3b\", \"rollNumber\": \"STUID46\"}, {\"studentId\": \"6668a630-ec80-4f2c-bd35-b5ef68417697\", \"rollNumber\": \"STUID47\"}, {\"studentId\": \"*************-46ef-952f-26aba43ac05c\", \"rollNumber\": \"STUID48\"}, {\"studentId\": \"6b602762-c7e7-4a1a-a7af-ddf682576172\", \"rollNumber\": \"STUID49\"}, {\"studentId\": \"6bdff490-53e8-41ae-b332-c16606021ab7\", \"rollNumber\": \"STUID50\"}, {\"studentId\": \"6bf02553-980b-449d-95f9-bb6c261f77b3\", \"rollNumber\": \"STUID51\"}, {\"studentId\": \"7216d433-56f2-4784-89e5-79b4d415f32a\", \"rollNumber\": \"STUID52\"}, {\"studentId\": \"735021d1-b706-418e-998b-9f5d774bac89\", \"rollNumber\": \"STUID53\"}, {\"studentId\": \"77753aa6-7d08-4456-897c-d6033e956042\", \"rollNumber\": \"STUID54\"}, {\"studentId\": \"77ea3aef-e2c2-48e9-b2cb-eef52e4ddeac\", \"rollNumber\": \"STUID55\"}, {\"studentId\": \"7894963d-03bb-4da0-947e-adf5ac3c220c\", \"rollNumber\": \"STUID56\"}, {\"studentId\": \"78eeed97-f00a-46b6-ab23-325abdb64cab\", \"rollNumber\": \"STUID57\"}, {\"studentId\": \"78f1e0ba-d495-48f8-a593-01a7a3d2bdc0\", \"rollNumber\": \"STUID58\"}, {\"studentId\": \"79574598-9dfb-4d47-924c-8168c9a1f6e4\", \"rollNumber\": \"STUID59\"}, {\"studentId\": \"7af49516-ebff-43f2-83c1-dbdbe32169a5\", \"rollNumber\": \"STUID60\"}, {\"studentId\": \"7dd15326-3354-4c8c-8ac2-4873e6a362bd\", \"rollNumber\": \"STUID61\"}, {\"studentId\": \"83e51b02-e9c0-42bf-8e7a-55dc182f35be\", \"rollNumber\": \"STUID62\"}, {\"studentId\": \"87f40b38-d429-4fd8-912d-1ca08377e978\", \"rollNumber\": \"STUID63\"}, {\"studentId\": \"8a9a6d9a-667a-42cb-8c3f-2f25ba3183a3\", \"rollNumber\": \"STUID64\"}, {\"studentId\": \"8bd5abcf-5885-4328-9c3c-11811ce2f6b4\", \"rollNumber\": \"STUID65\"}, {\"studentId\": \"9020b508-23c5-487f-86ba-143a74302108\", \"rollNumber\": \"STUID66\"}, {\"studentId\": \"90dc84c2-5cc6-4384-93ed-3674f44bf044\", \"rollNumber\": \"STUID67\"}, {\"studentId\": \"915682b0-97b0-438b-8a62-c9834013749e\", \"rollNumber\": \"STUID68\"}, {\"studentId\": \"919325df-81e8-4468-b366-10ee384186d9\", \"rollNumber\": \"STUID69\"}, {\"studentId\": \"92927897-300f-4e45-9837-6d8ac112a253\", \"rollNumber\": \"STUID70\"}, {\"studentId\": \"976a9689-85ee-4483-b936-346e1b4e69c7\", \"rollNumber\": \"STUID71\"}, {\"studentId\": \"9af3dc00-4d3f-4448-bd91-e98319c04dc6\", \"rollNumber\": \"STUID72\"}, {\"studentId\": \"9b31db18-6aab-4872-9b27-9b888450396d\", \"rollNumber\": \"STUID73\"}, {\"studentId\": \"9b6647aa-f200-4034-81a0-199a4048a599\", \"rollNumber\": \"STUID74\"}, {\"studentId\": \"9bab5185-027c-4089-925b-667fc775909b\", \"rollNumber\": \"STUID75\"}, {\"studentId\": \"9c4c492b-0ef5-4848-a79a-58e836bdd229\", \"rollNumber\": \"STUID76\"}, {\"studentId\": \"9fe349d1-edbd-4fb7-936e-1683e6625148\", \"rollNumber\": \"STUID77\"}, {\"studentId\": \"a05b99c1-8f2c-4e47-9351-a87ce4f8b759\", \"rollNumber\": \"STUID78\"}, {\"studentId\": \"a32decce-2d67-4fdc-a09f-da319931fcb5\", \"rollNumber\": \"STUID79\"}, {\"studentId\": \"a353d78a-8538-4006-b8af-6ea09849b3e7\", \"rollNumber\": \"STUID80\"}, {\"studentId\": \"a4e88874-dabb-4d45-8e97-7522ab4287b5\", \"rollNumber\": \"STUID81\"}, {\"studentId\": \"a574db85-1b28-49ab-b0bb-df4c1dfc5cfd\", \"rollNumber\": \"STUID82\"}, {\"studentId\": \"a59c0f40-c150-41ca-b6ad-74e4e8439c17\", \"rollNumber\": \"STUID83\"}, {\"studentId\": \"a70fc122-e0d2-4742-a3ff-654d7404e05e\", \"rollNumber\": \"STUID84\"}, {\"studentId\": \"ab162b72-1f4c-444c-b2fd-90c1c3fe5a82\", \"rollNumber\": \"STUID85\"}, {\"studentId\": \"ab25c913-8217-4e76-ad34-a1810fa7ca43\", \"rollNumber\": \"STUID86\"}, {\"studentId\": \"acbe2b02-3a68-4818-a0a8-727c73e31495\", \"rollNumber\": \"STUID87\"}, {\"studentId\": \"adfcd386-b00f-4642-af43-9b40bb302619\", \"rollNumber\": \"STUID88\"}, {\"studentId\": \"b106d637-22ae-460f-ac30-ca21da954017\", \"rollNumber\": \"STUID89\"}, {\"studentId\": \"b64ceed5-177f-4678-88bc-db50e4d9fe71\", \"rollNumber\": \"STUID90\"}, {\"studentId\": \"c2d2dbc4-77dc-4bf7-a865-b40285f40858\", \"rollNumber\": \"STUID91\"}, {\"studentId\": \"c494172b-4207-4df8-b6e2-1365b67ac96e\", \"rollNumber\": \"STUID92\"}, {\"studentId\": \"cd9bdfd7-d425-4a70-865f-d653e2cd895e\", \"rollNumber\": \"STUID93\"}, {\"studentId\": \"d154fb7d-5fc6-44ea-921a-fbb4b5be6ace\", \"rollNumber\": \"STUID94\"}, {\"studentId\": \"d1902c2d-bee3-48b2-b428-6ce91ffd6de2\", \"rollNumber\": \"STUID95\"}, {\"studentId\": \"d2591346-4bba-404c-8dbb-af467944ff0f\", \"rollNumber\": \"STUID96\"}, {\"studentId\": \"d2cc06fb-b6b2-43c7-9158-6f5793dba530\", \"rollNumber\": \"STUID97\"}, {\"studentId\": \"d344eef9-e3eb-4e12-a518-6808714a701c\", \"rollNumber\": \"STUID98\"}, {\"studentId\": \"d688a8ad-e113-49f5-84d7-756e324e11da\", \"rollNumber\": \"STUID99\"}, {\"studentId\": \"d6b819ec-cb89-4867-ad9a-acedb2d84043\", \"rollNumber\": \"STUID100\"}, {\"studentId\": \"da48418b-b6f0-4278-a7a4-edf61d207cc4\", \"rollNumber\": \"STUID101\"}, {\"studentId\": \"da5e5f67-943e-4baa-83e0-19049c0f5439\", \"rollNumber\": \"STUID102\"}, {\"studentId\": \"dc5006a4-93e1-4f06-9795-a333fb86989c\", \"rollNumber\": \"STUID103\"}, {\"studentId\": \"e1db36ea-836b-40f6-a254-2a8cc32efa60\", \"rollNumber\": \"STUID104\"}, {\"studentId\": \"e46a0ab2-fddf-4a4c-a084-d4fbbd0aeccb\", \"rollNumber\": \"STUID105\"}, {\"studentId\": \"e5359742-bd95-4e27-ae61-3fae44a272e3\", \"rollNumber\": \"STUID106\"}, {\"studentId\": \"e9430db9-9de0-4f39-86b8-2269f32a6fd3\", \"rollNumber\": \"STUID107\"}, {\"studentId\": \"ed5a059c-3912-487c-9168-bbf48eb36a67\", \"rollNumber\": \"STUID108\"}, {\"studentId\": \"ef93fa58-078e-495b-b8dd-93708c201cbc\", \"rollNumber\": \"STUID109\"}, {\"studentId\": \"efe3a5cf-24d3-42fa-baf3-4a2d976f01cb\", \"rollNumber\": \"STUID110\"}, {\"studentId\": \"f1da0630-0875-4d1c-abdd-73f6f8eac17b\", \"rollNumber\": \"STUID111\"}, {\"studentId\": \"f1de810a-50fb-45a4-945c-5fa40908f905\", \"rollNumber\": \"STUID112\"}, {\"studentId\": \"f25a23b6-3e2e-4b87-9744-97d49a685d1d\", \"rollNumber\": \"STUID113\"}, {\"studentId\": \"f48510b7-ee86-4f37-97c8-22375ffab41a\", \"rollNumber\": \"STUID114\"}, {\"studentId\": \"f4ba5e6e-e7ff-4777-a835-bb30202c79d6\", \"rollNumber\": \"STUID115\"}, {\"studentId\": \"f7438410-2e6b-4808-8574-2bdb7620ef17\", \"rollNumber\": \"STUID116\"}, {\"studentId\": \"f90bc090-a032-426b-9061-c950dcdde73e\", \"rollNumber\": \"STUID117\"}, {\"studentId\": \"fb333b8d-ac33-42b5-a9d1-900ce70e2d79\", \"rollNumber\": \"STUID118\"}, {\"studentId\": \"fbd1de83-c909-4da3-b41b-223804faca4a\", \"rollNumber\": \"STUID119\"}, {\"studentId\": \"fcfc8aca-6db6-4de9-bb3a-8ef58cc029f2\", \"rollNumber\": \"STUID120\"}, {\"studentId\": \"ff5d3bd7-32cb-4697-898a-fbaf71a436d6\", \"rollNumber\": \"STUID121\"}]}";
//		StudentAcademicSessionPayload criteria = null;
//		try {
//			ObjectMapper objectMapper = new ObjectMapper();
//
//			// Deserialize JSON into ExamReportFiltrationCriteria instance
//			criteria = objectMapper.readValue(json, StudentAcademicSessionPayload.class);
//
//			// Output the created instance
//			System.out.println(criteria);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

//		ExamReportFiltrationCriteria criteria = new ExamReportFiltrationCriteria(
//				110, UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), null,
//				UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"), null,
//				null, DownloadFormat.PDF, null, null, new HashSet<>(Arrays.asList(3542)),
//				new HashSet<>(Arrays.asList(UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"))),
//				null, null, null, false,
//				"sr_no,admission_no,roll_no,name,father_name,class,percentage,grade,division,result,rank,attendance",
//				false, null,
//				new HashSet<>(Arrays.asList(MarksDisplayType.MARKS)),
//				new HashSet<>(Arrays.asList(MarksDisplayType.MARKS)),
//				true, true);
//		final ReportDetails reportDetails1 = examReportGenerator.generateReport(10225,
//				ExamReportType.SUBJECT_WISE_RANK_REPORT, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), criteria);
//
//		final DocumentOutput documentOutput1 = pdfReportGenerator.generateReport(reportDetails1);

		/* final ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 225,
				UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"), "ANNUAL", true,
				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),true);

		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput.getContent();

		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
			byteArrayOutputStream1.writeTo(outputStream);
		} */
//		ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//
//		final ReportDetails reportDetails = examReportGenerator.getClassExamReport(101, UUID.fromString("9a4f2c3b-c02e-40f7-8e2d-9ba3a922a7c1"), null,
//				false, false, false, false,
//				false, "sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks", ExamReportType.EXAM_MARKS_REPORT, DownloadFormat.PDF, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.EXAMINATION);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 256,
//				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), "ANNUAL", true,
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));

//
//			ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//			try (OutputStream outputStream = new FileOutputStream(DEST15)) {
//				byteArrayOutputStream.writeTo(outputStream);
//			}
////
//		System.gc();
//		ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//				262, UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), null,
//				UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"), null,
//				null, DownloadFormat.PDF, null, null, new HashSet<>(Arrays.asList(3542)),
//				new HashSet<>(Arrays.asList(UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"))),
//				null, null, null, false,
//				"sr_no,admission_no,roll_no,name,father_name,class,percentage,grade,division,result,rank,attendance",
//				false, null,
//				new HashSet<>(Arrays.asList(MarksDisplayType.MARKS)),
//				new HashSet<>(Arrays.asList(MarksDisplayType.MARKS)),
//				true, true);
//		final ReportDetails reportDetails = examReportGenerator.generateReport(10390,
//				ExamReportType.MULTIPLE_EXAM_MARKS_REPORT, UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"),
//				examReportFiltrationCriteria);
//
//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
//		ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//				262, UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), null,
//				UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"), null,
//				null, DownloadFormat.PDF, null, null, new HashSet<>(Arrays.asList(3542)),
//				new HashSet<>(Arrays.asList(UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"))),
//				null, null, null, false,
//				"sr_no,admission_no,roll_no,name,father_name,class,percentage,grade,division,result,rank,attendance",
//				false, null,
//				new HashSet<>(Arrays.asList(MarksDisplayType.MARKS)),
//				new HashSet<>(Arrays.asList(MarksDisplayType.MARKS)),
//				true, true);
//		final ReportDetails reportDetails = examReportGenerator.generateReport(10390,
//				ExamReportType.MULTIPLE_EXAM_MARKS_REPORT, UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"),
//				examReportFiltrationCriteria);

//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}


////		final ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 190,
////				UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"), "PERIODIC_TEST_2", true,
////				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),true);
////
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
//	}
//}
//		/data-server/2.0/examination-reports/marks-feed-document/b711202a-aca6-4e2f-b83c-71cc5892b34f/pdf?
//		institute_id=10001&section_id=2264&single_column_student_count=40&add_relieved_students=false
//		&user_id=61c6759f-74d3-427c-851a-55b28698e04c&academic_session_id=185&standard_id=009fc7f3-f9b4-40bc-994d-0222af062eb9
//		&course_id=8de76c9d-21ad-43b7-8734-4b824046c8f4

//        final StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler = context.getBean(StudentMarksFeedDocumentHandler.class);
//        final DocumentOutput documentOutput = studentMarksFeedDocumentHandler.generateStudentMarksFeedDocument(
//                10390, 262,
////				UUID.fromString("328402c2-d66b-4d5a-80a3-ab8352f71e31"),
//                UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"),
//                null,
////                UUID.fromString("8de76c9d-21ad-43b7-8734-4b824046c8f4"),
//                UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"),
//                3542, UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"),
//                40, false, StudentSortingParameters.STUDENT_NAME);
//
//        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//            byteArrayOutputStream.writeTo(outputStream);
//        }

//        ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//                262, UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), null,
//                UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"), null,
//                null, DownloadFormat.PDF, "PRE_BOARD_1",
//                UUID.fromString("0aaa8d43-4209-4f19-b10f-5d7a34a14676"), null,
//                new HashSet<>(Arrays.asList(UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"))),
//                null, null, null, false,
//                "sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks",
//                false, null,
//                new HashSet<>(Arrays.asList(MarksDisplayType.MARKS, MarksDisplayType.GRADE, MarksDisplayType.PERCENTAGE)),
//                new HashSet<>(Arrays.asList(MarksDisplayType.MARKS, MarksDisplayType.GRADE, MarksDisplayType.PERCENTAGE)),
//                true, true);
//        final ReportDetails reportDetails = examReportGenerator.generateReport(10390,
//                ExamReportType.MULTIPLE_EXAM_MARKS_REPORT,
//                UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"), examReportFiltrationCriteria);
//<<<<<<< HEAD
////        final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////        DocumentOutput documentOutputReport = pdfReportGenerator.generateReport(reportDetails);
//
//
////        127.0.0.1 - - [20/Dec/2024:14:30:58 +0530] "GET /data-server/2.0/examination/marks-report/47a37229-0326-4cec-9d83-46f06bdcf1c4/ANNUAL/pdf?institute_id=10225&academic_session_id=110&compute_rank=true&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7 HTTP/1.1" 500 7048
//
//        ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//         DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10225, 110,
//                 UUID.fromString("47a37229-0326-4cec-9d83-46f06bdcf1c4"), "ANNUAL", true,
//                 UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"));
//
//        ByteArrayOutputStream byteArrayOutputStreamReport = documentOutput.getContent();
//        try (OutputStream outputStream = new FileOutputStream(sourceFolderReportCard)) {
//            byteArrayOutputStreamReport.writeTo(outputStream);
//        }
//
////        final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//=======
//
//			//    final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//			//    DocumentOutput documentOutputReport = pdfReportGenerator.generateReport(reportDetails);
//>>>>>>> b7cc49eee749a79827e146ed5077509a97d2523b
//
//        ByteArrayOutputStream byteArrayOutputStreamReport = documentOutputReport.getContent();
//        try (OutputStream outputStream = new FileOutputStream(sourceFolderReport)) {
//            byteArrayOutputStreamReport.writeTo(outputStream);
//        }

//        final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);

//        final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//        final StaffIdentityCardHandler staffIdentityCardHandler = context.getBean(StaffIdentityCardHandler.class);

//		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(10001,
//				185, UUID.fromString("002ad7db-6d85-432d-bacf-fb174515b5f6"),
//				null);
//        final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(101,
//				190, UUID.fromString("00f7461d-68d0-4307-ac2a-83c72c3a9bde"),
//				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//        List<UUID> ids = new ArrayList<>();
//////
//		ids.add(UUID.fromString("07984426-6efc-4e30-8b20-3ded2fb545c6"));
//		ids.add(UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"));
//		ids.add(UUID.fromString("489a6179-b2b9-4c9f-ba39-4d1f0db81bb5"));
//		ids.add(UUID.fromString("93ace6ad-e4e8-42f2-906c-53440491b381"));
//		ids.add(UUID.fromString("99d8db8a-bf60-4bdb-afc2-86b6db321612"));
//		ids.add(UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"));
//		ids.add(UUID.fromString("c286977b-90d0-4c7d-930f-4d14b8478902"));
//		ids.add(UUID.fromString("e3dd4c3d-5b98-492f-b8de-e1bbaf1b6767"));
//ids.add(UUID.fromString("00f7461d-68d0-4307-ac2a-83c72c3a9bde"));
//		ids.add(UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"));
//		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(101, 190, ids, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
////
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}

//        List<UUID> ids = new ArrayList<>();
////
//		ids.add(UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"));
//	    final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(101, 256, ids, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
////
//       //final DocumentOutput documentOutput = staffIdentityCardHandler.generateIdentityCard(101, UUID.fromString("78d3d7f2-7bdf-4616-924d-297d202ab07e"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST12))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}

//        List<UUID> ids = new ArrayList<>();
//////
//		ids.add(UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"));
//    final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(101, 256, ids, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//
//       final DocumentOutput documentOutput = staffIdentityCardHandler.generateIdentityCard(101, UUID.fromString("78d3d7f2-7bdf-4616-924d-297d202ab07e"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST13))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}

//
//        final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
////		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(10001,
////				185, UUID.fromString("002ad7db-6d85-432d-bacf-fb174515b5f6"),
////				null);
////        final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(101,
////				190, UUID.fromString("00f7461d-68d0-4307-ac2a-83c72c3a9bde"),
////				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//        List<UUID> ids = new ArrayList<>();
//////
//		ids.add(UUID.fromString("07984426-6efc-4e30-8b20-3ded2fb545c6"));
//		ids.add(UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"));
//		ids.add(UUID.fromString("489a6179-b2b9-4c9f-ba39-4d1f0db81bb5"));
//		ids.add(UUID.fromString("93ace6ad-e4e8-42f2-906c-53440491b381"));
//		ids.add(UUID.fromString("99d8db8a-bf60-4bdb-afc2-86b6db321612"));
//		ids.add(UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"));
//		ids.add(UUID.fromString("c286977b-90d0-4c7d-930f-4d14b8478902"));
//		ids.add(UUID.fromString("e3dd4c3d-5b98-492f-b8de-e1bbaf1b6767"));
//ids.add(UUID.fromString("00f7461d-68d0-4307-ac2a-83c72c3a9bde"));
//		ids.add(UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"));
//		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(101, 190, ids, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
////
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
////
//

//    }
//}
//        final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(10001,
//				185, UUID.fromString("002ad7db-6d85-432d-bacf-fb174515b5f6"),
//				null);
//    }
//}

////     	final GatePassHandler gatePassHandler = context.getBean(GatePassHandler.class);
////     	final FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//
//
////		final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101, UUID.fromString("03c4527f-7b6d-45c7-911c-856e57728f05"), true,
////				null);
//
//
////		 final DocumentOutput documentOutput = feeInvoiceHandler.generateBulkInvoices(10225, 194, false,
////                UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), "466c8a4e-8c03-484d-b81d-f079f20919ab,66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//
////		final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(110, UUID.fromString("ef57a5ee-347f-4abd-924d-8a39bcc0a30e"),
////				null, null);
//
////		boolean onlyNewAdmissionStudents = true;
////		FilterationCriteria filterationCriteria = new FilterationCriteria(null, null, null, null,
////				null, null, null, null, onlyNewAdmissionStudents, null);
////		final DocumentOutput documentOutput = admissionFormHandler.generateBulkAdmissionForm(10131, 233, filterationCriteria);
////
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////		AdmitCardHandler admitCardHandler = context.getBean(AdmitCardHandler.class);
////		List<UUID> studentUUIDs = new ArrayList<>();
////		studentUUIDs.add(UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"));
////		studentUUIDs.add(UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"));
////		studentUUIDs.add(UUID.fromString("e51a43ae-7a80-4d03-8b74-e4bc4071273f"));
////
//////		http://127.0.0.1:8000/b1812075-526a-4d5c-817b-d8eb7953f6bc/examination/generate-admitcard/bc652973-477a-4a97-8ae8-398db2707995/ADMIT_CARD_WITHOUT_DATESHEET?studentIds=e5b75d5d-7683-41e3-b952-4dd2fc48c625,84a1080c-108f-4124-94ec-b76a4d24997f,e51a43ae-7a80-4d03-8b74-e4bc4071273f,07984426-6efc-4e30-8b20-3ded2fb545c6
////		final DocumentOutput documentOutput = admitCardHandler.generateAdmitCards(101,
////				UUID.fromString("bc652973-477a-4a97-8ae8-398db2707995"),
////				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), AdmitCardType.ADMIT_CARD_WITHOUT_DATESHEET, studentUUIDs, 6);
////		ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
////		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
//
//
////		final DocumentOutput documentOutput = gatePassHandler.generateGatePassDocument(101, UUID.fromString("1fc039fb-7ced-415e-aa03-57d5737240df"), UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), true);
//        final ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//
//        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 225,
//                UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"), "ANNUAL", true,
//                UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), true);
//
//        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//        try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
//            byteArrayOutputStream.writeTo(outputStream);
//        }
//		}
//	}

//
////		final StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler = context.getBean(StudentMarksFeedDocumentHandler.class);
////		final DocumentOutput documentOutput = studentMarksFeedDocumentHandler.generateStudentMarksFeedDocument(
////				110, 255, UUID.fromString("e587dbe7-ad86-4613-9ee0-d59f22ba9a4a"),
////				UUID.fromString("26a15bd6-adb6-4e39-ae80-94b85e56aab5"), UUID.fromString("f28b4368-97dc-4000-9fbb-b23e0a5d3b57"), null,
////				UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), 40, false);
////
//
//		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 190,
//				UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"), "PRE_BOARD_1", true,
//				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),true);
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}}
//////
//
//
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////
//
////		final DocumentOutput documentOutput = gatePassHandler.generateGatePassDocument(101, UUID.fromString("1fc039fb-7ced-415e-aa03-57d5737240df"), UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), true);
////
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////		final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101, UUID.fromString("03c4527f-7b6d-45c7-911c-856e57728f05"), true,
////				null);
//
////<<<<<<< HEAD
//////		final TransportReportsGenerator transportReportsGenerator = context.getBean(TransportReportsGenerator.class);
//////		final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//////
////////		/data-server/2.0/transport/reports/10030/view-report/STUDENT_TRANSPORT_FEES_LEVEL_DUES?academic_session_id=32&requiredHeaders=serial_number,student_admission_number,student_name,student_status,student_class_name,student_father_name,student_primary_contact_number,father_contact_number,mother_contact_number,transport_pickup_route_name,transport_drop_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_fees_name,transport_route_area_assigned_amount,transport_route_area_amount_collected,transport_route_area_given_discount,transport_route_area_discount_to_be_given,transport_route_area_due_amount,transport_paid_fine_amount,transport_due_fine_amount&requiredStandards=&fee_ids=&only_enrolled_students=false&parent_area_ids=&area_ids=&pickup_ids=&drop_ids=&user_id=2a3a725b-163a-4dcc-a1f6-8fe5c1377262
//////
////=======
//		final TransportReportsGenerator transportReportsGenerator = context.getBean(TransportReportsGenerator.class);
//		final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//////
////////		/data-server/2.0/transport/reports/10030/view-report/STUDENT_TRANSPORT_FEES_LEVEL_DUES?academic_session_id=32&requiredHeaders=serial_number,student_admission_number,student_name,student_status,student_class_name,student_father_name,student_primary_contact_number,father_contact_number,mother_contact_number,transport_pickup_route_name,transport_drop_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_fees_name,transport_route_area_assigned_amount,transport_route_area_amount_collected,transport_route_area_given_discount,transport_route_area_discount_to_be_given,transport_route_area_due_amount,transport_paid_fine_amount,transport_due_fine_amount&requiredStandards=&fee_ids=&only_enrolled_students=false&parent_area_ids=&area_ids=&pickup_ids=&drop_ids=&user_id=2a3a725b-163a-4dcc-a1f6-8fe5c1377262
//////
//////>>>>>>> 23ffb529d31b4dde429748e26af706f0f809b9b5
//		int instituteId = 10030;
//		int academicSessionId = 32;
//		TransportReportType transportReportType = TransportReportType.STOPPAGE_FEES_CHART;
//		Integer start = -1;;
//		Integer end = -1;
//		String requiredHeaders = "serial_number,student_admission_number,student_name,student_status,student_class_name,student_father_name,student_primary_contact_number,transport_pickup_route_name,transport_drop_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_fees_name,transport_route_area_assigned_amount,transport_route_area_amount_collected,transport_route_area_given_discount,transport_route_area_discount_to_be_given,transport_route_area_due_amount";
//		String requiredStandardsCSV = null;
//		String feeIdsStr = null;
//		boolean onlyEnrolledStudents = false;
//		String parentAreaIdsStr = null;
//		String areaIdStr = null;
//		String pickupIdsStr = null;
//		String dropIdsStr = null;
//		DownloadFormat downloadFormat = DownloadFormat.PDF;
//		UUID userId = UUID.fromString("2a3a725b-163a-4dcc-a1f6-8fe5c1377262");
//		ReportDetails reportDetails = transportReportsGenerator.generateReport(instituteId, academicSessionId,
//				transportReportType, start, end, requiredHeaders, requiredStandardsCSV, feeIdsStr, onlyEnrolledStudents, parentAreaIdsStr,
//				areaIdStr, pickupIdsStr, dropIdsStr, downloadFormat, userId);
//////
//		DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
//////
////
////
//////		final UserCredentialsPDFGenerator userCredentialsPDFGenerator = context.getBean(UserCredentialsPDFGenerator.class);
//////		DocumentOutput documentOutput = userCredentialsPDFGenerator.generateUserCredentialsPDF(101, 190, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), "User Credentials", UserType.ADMIN);
//////
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}

//
//
//		/*DocumentOutput documentOutput = examReportSerivceProvider.getClassExamReport(101, 33,
//				UUID.fromString("113cb82e-d827-11ed-9f73-8f324eeebff2"), null, "ANNUAL",
//				UUID.fromString("2a3a725b-163a-4dcc-a1f6-8fe5c1377262"), null,
//				Collections.singletonList(UUID.fromString("9a0503f3-75da-410c-b7be-4a4a582e9158")), "BULK");
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//
//		try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		} */
//
//		/* final FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
//
//
//		final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101,
//				UUID.fromString("2043676a-2069-4cd1-9917-a4efa303f21a"), true,
//				null);
//
//		/* final GatePassHandler gatePassHandler = context.getBean(GatePassHandler.class);
//		final DocumentOutput documentOutput = gatePassHandler.generateGatePassDocument(101, UUID.fromString("e57a9fd0-051c-405b-ba25-7481b363c836"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(DEST6)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		} */
//
////		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
////		final  DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101, UUID.fromString("fd2a2072-d1a1-4061-906a-4d64a68f6eca"), false, "ADMIN");
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		System.out.println("in");
////		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////		/2.0/examination-reports/marks-feed-document/a9e7ea6c-65b1-46a0-8b3f-a8697be3ba4c/pdf?institute_id=101&section_id=2392&single_column_student_count=40&add_relieved_students=false&user_id=08dc0556-b8a1-4acd-a6fa-759ca864df9e&academic_session_id=190&standard_id=4dbc60bd-b6ea-44a1-8a4f-3505f0390d65
////        StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler = context.getBean(StudentMarksFeedDocumentHandler.class);
////		final  DocumentOutput documentOutput = studentMarksFeedDocumentHandler.generateStudentMarksFeedDocument(
////				101, 190, UUID.fromString("3d634ea6-22b9-4a11-bc0b-06e8b980d3e5"), null , UUID.fromString("4dbc60bd-b6ea-44a1-8a4f-3505f0390d65"), 2392,UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 40, false);
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		System.out.println("in");
////		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////		FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
////		final  DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(101, UUID.fromString("fd2a2072-d1a1-4061-906a-4d64a68f6eca"), false, "ADMIN");
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		System.out.println("in");
////		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
//
////        final StudentFeesChartHandler studentFeesChartHandler = context.getBean(StudentFeesChartHandler.class);
////		final BookReceiptDocumentHandler bookReceiptDocumentHandler = context.getBean(BookReceiptDocumentHandler.class);
////
////		final DocumentOutput documentOutput = bookReceiptDocumentHandler.generateBookReceipt(
////				10225, UUID.fromString("5b5465dd-3305-4a99-9c72-80ed98ef5734"), 194);
//
////		DocumentOutput documentOutput = studentFeesChartHandler.generateStudentFeesChart(10225, 194,
////				new HashSet<>(Arrays.asList(UUID.fromString("b5e80269-f66f-4836-abd8-d267fcf465b5"))),
////				1, true, true, true, true);
//
////		127.0.0.1 - - [02/Jul/2024:17:43:49 +0530] "POST /data-server/2.0/fees/generate-fees-charts/?institute_id=10225&academic_session_id=194&student_per_page=1&due_amount=true&collected_amount=true&disounted_amount=true HTTP/1.1" 400 122
//
//
//		final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//final StudentIdentityCardHandler studentIdentityCardHandler = c
//		List<UUID> ids = new ArrayList<>();
////
////		ids.add(UUID.fromString("07984426-6efc-4e30-8b20-3ded2fb545c6"));
////		ids.add(UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"));
////		ids.add(UUID.fromString("489a6179-b2b9-4c9f-ba39-4d1f0db81bb5"));
////		ids.add(UUID.fromString("93ace6ad-e4e8-42f2-906c-53440491b381"));
////		ids.add(UUID.fromString("99d8db8a-bf60-4bdb-afc2-86b6db321612"));
////		ids.add(UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"));
////		ids.add(UUID.fromString("c286977b-90d0-4c7d-930f-4d14b8478902"));
////		ids.add(UUID.fromString("e3dd4c3d-5b98-492f-b8de-e1bbaf1b6767"));
////
////		ids.add(UUID.fromString("84a1080c-108f-4124-94ec-b76a4d24997f"));
////		System.out.println(ids);
////		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(101,
////				190, UUID.fromString("93ace6ad-e4e8-42f2-906c-53440491b381"),
////				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//
////		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(101, 190, ids, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//////
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
//
//
//
//
////        final DocumentOutput documentOutput = feeInvoiceHandler.generateBulkInvoices(10225, 194, false,
////                UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), "466c8a4e-8c03-484d-b81d-f079f20919ab,66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
///*
//		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
//
//        DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10225, 110,
//                UUID.fromString("954e5692-68cc-488b-8e47-c5aac0206b47"), "ANNUAL",
//                true, null); */
//
////
//
//
////        AdmissionFormHandler admissionFormHandler = context.getBean(AdmissionFormHandler.class);
//
////        final TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
////        DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(10295,
////                UUID.fromString("000c210c-a18e-43b6-885a-70037626d1d5"));
//
////        final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////        int instituteId = 10225;
////        int academicSessionId = 110;
////        ExamReportType examReportType = ExamReportType.TEACHER_WISE_GRADE_RESULT_ANALYSIS_REPORT;
////        UUID standardId = null;
////        Integer sectionId = null;
////        UUID examId = null;
////        UUID courseId = null;
////        CourseType courseType = null;
////        UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
////        UUID staffId = UUID.fromString("6486597a-7d2a-4814-aa29-1e415732fc7f");
////        String reportCardType = "ANNUAL";
////        String sectionIdSetStr = null;
////        String examIdSetStr = null;
////        String courseIdSetStr = null;
////        String compareCumulativeWithExamIdSetStr = null;
////        Integer rankTill = null;
////        boolean excludeCoScholasticSubjects = false;
////        boolean showCoScholasticGrade = false;
////        boolean showScholasticGrade = false;
////        boolean isSortStudentOnRank = false;
////        String requiredHeaders = null;
////        String additionalCoursesStr = null;
////        DownloadFormat downloadFormat = DownloadFormat.EXCEL;
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(instituteId, academicSessionId,
////                studentId, reportType, computeRank, userId);
////                ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////
//////                final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10065, 163,
//////                    UUID.fromString("0551a517-59a1-4ffc-936e-afd99b9d516c"), "HALF_YEARLY", true,
//////                    UUID.fromString("47e21660-ce8e-477c-a8e3-effdb3c6c464"));
////                 DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 190,
////
//
////                final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10290, 176,
////                UUID.fromString("0a70baea-2edc-41b9-bd79-ca2b37f7449c"), "ANNUAL", true,
////                UUID.fromString("8ea201ff-5f41-41d0-bdbe-0d782f558ceb"));
//
//
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 190,
////                UUID.fromString("12e0c129-f132-4e4d-a3ed-1fed35863a11"), "ANNUAL", true,
////                UUID.fromString("85c34583-3545-4c8b-b3e0-ab041eac3559"));
//
////        final FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
////////        DocumentOutput documentOutput = admissionFormHandler.generateStaticAdmissionForm(10030, 32);
//////
////        final DocumentOutput documentOutput = feeInvoiceHandler.generateBulkInvoices(10030, 32, false,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), "5463eccd-9d27-4d10-8244-bce609b4c428,b0fe4dfb-b931-4bf6-b4d1-161f136bb8b8");
////
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
////        final ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId,
////                examReportType, standardId, sectionId, examId, courseId, courseType, userId, downloadFormat,
////                reportCardType, staffId, sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr,
////                rankTill, excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders,
////                isSortStudentOnRank, additionalCoursesStr);
//
////        final DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(110,
////                UUID.fromString("13e943b6-b038-4fe0-93b8-0602862c2fa8"));
//
////       final BoardRegistrationFormHandler boardRegistrationFormHandler = context.getBean(BoardRegistrationFormHandler.class);
////		final BookReceiptDocumentHandler bookReceiptDocumentHandler = context.getBean(BookReceiptDocumentHandler.class);
////        final StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler = context.getBean(StudentMarksFeedDocumentHandler.class);
////
////        }
////		rt/1b3992a3-e16d-4bd5-8c62-19be84fa751e/ANNUAL/pdf?institute_id=10105&academic_session_id=165
//
////        //        &compute_rank=true&user_id=f1b7f80b-c0a9-40b8-bd91-c501b0795e39
////<<<<<<< HEAD
////        /2.0/examination/marks-report/3c548e15-5419-4454-909a-0843234f41f6/HALF_YEARLY/pdf?institute_id=10205&academic_session_id=131&compute_rank=true&user_id=e1982041-d931-4663-9f9e-e07fb6f92d55
//
////        /data-server/2.0/examination/marks-report/b7d8d6be-7ac5-4417-b9d9-035ada7d25d7/ANNUAL/pdf?institute_id=10160&academic_session_id=148
////        &compute_rank=true&user_id=16f903a4-d1f7-47b8-a90a-0bd8e7e04339
////        /data-server/2.0/examination/marks-report/487c6eee-66a9-4e0d-921d-f135d8a26324/ANNUAL/pdf?institute_id=10001&academic_session_id=142
////        &compute_rank=true&user_id=61c6759f-74d3-427c-851a-55b28698e04c
//
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10160, 148,
////                UUID.fromString("00a12775-a5d6-4131-ac91-d52a7310c8be"), "ANNUAL", true,
////                UUID.fromString("16f903a4-d1f7-47b8-a90a-0bd8e7e04339"));
//
////        final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(101,
////                190, UUID.fromString("93ace6ad-e4e8-42f2-906c-53440491b381"), UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10160, 148,
////                UUID.fromString("00a12775-a5d6-4131-ac91-d52a7310c8be"), "ANNUAL", true,
////                UUID.fromString("16f903a4-d1f7-47b8-a90a-0bd8e7e04339"));
////=======
////>>>>>>> 47e5b0c05cc6cd8b3dd40f9df8fb0798ef13eb52
//
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10290, 176,
////                UUID.fromString("bf5f3d96-cfa9-4854-acf7-fc29317f9dc9"), "ANNUAL", true,
////                UUID.fromString("8ea201ff-5f41-41d0-bdbe-0d782f558ceb"));
//
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10215, 101,
////                UUID.fromString("8ed3222b-fa21-4b45-9e9d-eaa4af3b2c19"), "ASSESSMENT_2", true,
////                UUID.fromString("a58c3354-d35e-446b-a76d-31496a07620f"));
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10205, 131,
////                UUID.fromString("3c548e15-5419-4454-909a-0843234f41f6"), "HALF_YEARLY", true,
////                UUID.fromString("e1982041-d931-4663-9f9e-e07fb6f92d55"));
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10105, 165,
////                UUID.fromString("1b3992a3-e16d-4bd5-8c62-19be84fa751e"), "ANNUAL", true,
////                UUID.fromString("f1b7f80b-c0a9-40b8-bd91-c501b0795e39"));
//
////=======
////        //        &compute_rank=true&user_id=f1b7f80b-c0a9-40b8-bd91-c501b0795e39
//////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10105, 165,
//////                UUID.fromString("1b3992a3-e16d-4bd5-8c62-19be84fa751e"), "ANNUAL", true,
//////                UUID.fromString("f1b7f80b-c0a9-40b8-bd91-c501b0795e39"));
////>>>>>>> ce9ae388ac054706d9487b1109d685f5f96f36f7
//
////        final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
////        DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(10030, 32,
////                UUID.fromString("dc0f1339-7d86-4e33-b38e-924ac5e6d468"), UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
////        final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
//
//
////        ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 33,
////                UUID.fromString("9e1c53d5-9473-4d2b-8ee0-5992ab4e12a0"), "UNIT_TEST_I", true,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
////        ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 1,
////                UUID.fromString("12e0c129-f132-4e4d-a3ed-1fed35863a11"), "ANNUAL", true,
////                UUID.fromString("85c34583-3545-4c8b-b3e0-ab041eac3559"));
//
////        UUID examId = UUID.fromString("5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6");
////        final DocumentOutput documentOutput = admitCardHandler.generateAdmitCards(101, examId,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), AdmitCardType.ADMIT_CARD_WITH_DATESHEET, null);
//
//
//		//      StudentMarksFeedDocumentHandler studentMarksFeedDocumentHandler = context.getBean(StudentMarksFeedDocumentHandler.class);
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 33,
////                UUID.fromString("9e1c53d5-9473-4d2b-8ee0-5992ab4e12a0"), "ANNUAL", true,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
////        UUID examId = UUID.fromString("5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6");
////        final DocumentOutput documentOutput = studentMarksFeedDocumentHandler.generateStudentMarksFeedDocument(
////                101, examId, null, null,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"),
////                0, false);
////        [04/Mar/2024 13:53:34] "GET /********-f58a-4b15-9005-e80c604962b5/examination/generate-feed-marks-sheet/c0416c98-7385-4d50-91f8-7510ba3facb7?section_id=1072&student_count=29&add_relieved_students=false HTTP/1.1" 200 1195530
////        UUID examId = UUID.fromString("c0416c98-7385-4d50-91f8-7510ba3facb7");
////        final DocumentOutput documentOutput = studentMarksFeedDocumentHandler.generateStudentMarksFeedDocument(
////                10225, examId, null, 1072,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"),
////                0, false);
//
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
////            byteArrayOutputStream.writeTo(outputStream);
//
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////            byteArrayOutputStream.writeTo(outputStream);
////
////        }
////			case STUDENT_ENROLLMENT_BY_AGE:
////				return generateStudentEnrollmentByAgeReport(instituteId, academicSessionId);
////			case CAST_REPORT:
////				return generateCastWiseStudentReport(instituteId, academicSessionId);
////			case RELIGION_REPORT:
////				return generateReligionWiseStudentReport(instituteId, academicSessionId);
////			case MOBILE_APP_TRACK:
////				return generateStudentMobileAppTrackReport(studentReportType, instituteId, academicSessionId,
////						requiredHeaderAttributes, requiredStandards, filterationCriteria);
////			case GENDER_REPORT:
////				return generateGenderWiseStudentReport(instituteId, academicSessionId);
////			case HOUSE_SUMMARY_REPORT:
////				return generateHouseSummaryReport(instituteId,academicSessionId);
////			case STUDENT_DOCUMENT_REPORT:
////				return getStudentDocumentReport(instituteId, academicSessionId, requiredStandards, studentStatusList, documentTypes, documentStatus);
////			case SIBLING_DETAILS_REPORT:
////				return generateStudentsSiblingsDetailsReport(instituteId,academicSessionId,requiredStandards,studentStatusList);
		/* PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
		int instituteId = 110;
		int academicSessionId = 295;
		UUID userId = UUID.fromString("1755b257-3ee5-4bd2-858e-7962dbd2144d");
		StudentReportType studentReportType = StudentReportType.STUDENT_DETAILS_REPORT;
		DownloadFormat downloadFormat = DownloadFormat.PDF;
		String requiredStandards = "";
		String studentStatus = null;
		String documentTypes = null;
		StudentDocumentStatus documentStatus = null;
		String requiredHeaders = "serial_number,student_admission_number,student_session_status,student_name,student_father_name,student_mother_name,student_class_name,student_house_name,student_admission_date,student_dob,student_gender,student_primary_contact_number,student_category,student_religion,student_caste,student_rte,student_specially_abled,student_specially_abled_type,student_bpl,student_registration_number,student_relieve_date,student_birth_place,student_aadhar_number,student_nationality,student_mother_tongue,student_area_type,student_permanent_address,student_present_address,student_city,student_state,student_zipcode,student_present_city,student_present_state,student_present_zipcode,student_primary_email,student_sponsored,student_whatsapp_number,student_admission_in_class,student_present_post_office,student_permanent_post_office,student_present_police_station,student_permanent_police_station,student_academic_session";
		FilterationCriteria filterationCriteria = new FilterationCriteria(null, null, null, null ,null ,null, null, null, false, null, null, null, StudentSortingParameters.ADMISSION_DATE);
		boolean newAdmission = false;
		final StudentReportsGenerator studentReportsGenerator = context.getBean(StudentReportsGenerator.class);
		final ReportDetails reportDetails = studentReportsGenerator.generateReport(instituteId,
				academicSessionId, userId, studentReportType, downloadFormat, requiredStandards,
				studentStatus, studentStatus, documentTypes, documentStatus, requiredHeaders, filterationCriteria, newAdmission);
		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.ADMISSION);
		ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
			byteArrayOutputStream.writeTo(outputStream);
		} */
////        33/9e1c53d5-9473-4d2b-8ee0-5992ab4e12a0/UNIT_TEST_I
//
////        final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////        final AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
////        final FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
////        DocumentOutput documentOutput = admissionFormHandler.generateStaticAdmissionForm(10030, 32);
//
////        final DocumentOutput documentOutput = feeInvoiceHandler.generateBulkInvoices(10030, 32, false,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), "5463eccd-9d27-4d10-8244-bce609b4c428,b0fe4dfb-b931-4bf6-b4d1-161f136bb8b8");
//
////        int instituteId = 10001;
////        int academicSessionId = 110;
////        UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
////        String attendanceReportTypeStr = "ATTENDANCE_DETAILS_IN_A_MONTH";
////        String standardIdStr = "";
////        String attendanceStatusStr = "";
////        String attendanceTypeIdStr = "";
////        int start = 1704047400;
////        int end = 1706639400;
////        int singleDate = -1;
////        DownloadFormat downloadFormat = DownloadFormat.PDF;
////        final ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId,
////                academicSessionId, attendanceReportTypeStr, standardIdStr, attendanceStatusStr, attendanceTypeIdStr,
////                start, end, singleDate, userId, downloadFormat);
////        final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
////            byteArrayOutputStream.writeTo(outputStream);
////
////        }
//
////        final FeeInvoiceHandler feeInvoiceHandler = context.getBean(FeeInvoiceHandler.class);
////        DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(10030,
////                UUID.fromString("2f62158e-6515-487f-8f2a-02e11826f442"), true, "ADMIN");
//
////
//
////		TransferCertificateGeneratorFactory transferCertificateGeneratorFactory = context.getBean(TransferCertificateGeneratorFactory.class)
////		TransferCertificateHandler transferCertificateHandler = context.getBean(TransferCertificateHandler.class);
////		final DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(101,
////                UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"));
////		StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//
////		StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
////		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//
//		final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(110, "STAFF_ATTENDANCE_DETAILS_IN_A_DAY", "", "", "ONBOARD", 1717180200, 1719685800, UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), DownloadFormat.PDF);
//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STAFF_ATTENDANCE);
//        ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
//        try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
////		AdmitCardGeneratorFactory admitCardGeneratorFactory = context.getBean(AdmitCardGeneratorFactory.class);
//
////
////
//
//

///
///
//
//
////		BonafideCertificateDocumentHandler bonafideCertificateDocumentHandler = context.getBean(BonafideCertificateDocumentHandler.class);
////		final DocumentOutput documentOutput = bonafideCertificateDocumentHandler.generateBonafideCertificate(101,
////				UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), 256);
////
//		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.ADMISSION);
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//			byteArrayOutputStream.writeTo(outputStream);
//		}
////
//		/* final StudentIdentityCardHandler studentIdentityCardHandler = context.getBean(StudentIdentityCardHandler.class);
//		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(101,
//				256, UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"),
//				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//
//		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST7))) {
//
//			byteArrayOutputStream.writeTo(outputStream);
//		} */
//
//// 		AdmitCardHandler admitCardHandler = context.getBean(AdmitCardHandler.class);
//// 		List<UUID> studentUUIDs = new ArrayList<>();
//// 		studentUUIDs.add(UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"));
//// 		studentUUIDs.add(UUID.fromString("b2538495-29a1-4317-b092-e19313c1cf31"));
//// 		final DocumentOutput documentOutput = admitCardHandler.generateAdmitCards(101,
//// 				UUID.fromString("0a30eb8e-8cb9-431b-8a16-bb806c04da3a"),
//// 				UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), AdmitCardType.ADMIT_CARD_WITH_DATESHEET, studentUUIDs);
//// 		ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
////         try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST8))) {
//// 			byteArrayOutputStream.writeTo(outputStream);
//// 		}
////		StaffIdentityCardHandler staffIdentityCardHandler = context.getBean(StaffIdentityCardHandler.class);
////		final DocumentOutput documentOutput = staffIdentityCardHandler.generateIdentityCard(101, UUID.fromString("10371b87-f773-4c1a-a663-43720db6a9f3"), UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
////		ByteArrayOutputStream byteArrayOutputStream =  documentOutput.getContent();
////        try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////                UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"));
////        /********-f58a-4b15-9005-e80c604962b5/examination/student-report-card/110/847f35ad-11ed-4bdf-8509-15a6f5c2ab2b/HALF_YEARLY_EXAM
////            /f8df05a7-f8b6-4c9e-913c-c10ba0fd759c/examination/student-report-card/177/356013c0-4506-4866-a8d9-d42b440063ae/ANNUA
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////
//////        /********-f58a-4b15-9005-e80c604962b5/examination/student-report-card/110/847f35ad-11ed-4bdf-8509-15a6f5c2ab2b/HALF_YEARLY_EXAM
//////            /f8df05a7-f8b6-4c9e-913c-c10ba0fd759c/examination/student-report-card/177/356013c0-4506-4866-a8d9-d42b440063ae/ANNUA
//////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//////        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
////
////            byteArrayOutputStream.writeTo(outputStream);
////        }
//
////		final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
////				reportType, start, end, feeDueDate, feeIds, requiredStandards, feePaymentTransactionStatus,requiredHeaders,
////				userId, downloadFormat,studentStatusCSV,feeHeadIds,multipleAcademicSessionIds, feeReportDataType,transactionMode,skipStudentWithZeroAssignment);
//
////		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////		final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 190,
////				UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"), "HALF_YEARLY_II", true,
////				UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
////		System.out.println("heloo");
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//
//
////        /********-f58a-4b15-9005-e80c604962b5/examination/student-report-card/110/847f35ad-11ed-4bdf-8509-15a6f5c2ab2b/HALF_YEARLY_EXAM
////            /f8df05a7-f8b6-4c9e-913c-c10ba0fd759c/examination/student-report-card/177/356013c0-4506-4866-a8d9-d42b440063ae/ANNUA
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//
//
////
////
//////        /********-f58a-4b15-9005-e80c604962b5/examination/student-report-card/110/847f35ad-11ed-4bdf-8509-15a6f5c2ab2b/HALF_YEARLY_EXAM
//////            /f8df05a7-f8b6-4c9e-913c-c10ba0fd759c/examination/student-report-card/177/356013c0-4506-4866-a8d9-d42b440063ae/ANNUA
//////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//////        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
////
////
//////
////////        /********-f58a-4b15-9005-e80c604962b5/examination/student-report-card/110/847f35ad-11ed-4bdf-8509-15a6f5c2ab2b/HALF_YEARLY_EXAM
////////            /f8df05a7-f8b6-4c9e-913c-c10ba0fd759c/examination/student-report-card/177/356013c0-4506-4866-a8d9-d42b440063ae/ANNUA
////////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////////        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
//////
//////
////
////            byteArrayOutputStream.writeTo(outputStream);
////		}
//
////		CourseReportGenerator courseReportGenerator = context.getBean(CourseReportGenerator.class);
////		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////		String sectionIds = "18,19";
////		String courseId = "c98ac247-f49a-494b-bba9-4ba8153b82b1,10f4e85d-8517-490e-88cc-0a17945c499f";
//////
////		CellIndexes cellIndexes = new CellIndexes(0, 0, 0, 3);
////		List<CellIndexes> cellIndexesList = new ArrayList<>();
////		cellIndexesList.add(cellIndexes);
////		 List<List<ReportCellDetails>> reportCellDetails = Arrays.asList(
////				Arrays.asList(
////						new ReportCellDetails("Class: 11th EM | Section: A, B, C | Subject: ", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f)
////				),
////				Arrays.asList(
////						new ReportCellDetails("S.No.", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f),
////						new ReportCellDetails("Admission No", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f),
////						new ReportCellDetails("Roll No", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f),
////						new ReportCellDetails("Name", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f)
////				),
////				Arrays.asList(
////						new ReportCellDetails("Total", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.BOTTOM, 0.0f),
////						new ReportCellDetails("", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f),
////						new ReportCellDetails("", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f),
////						new ReportCellDetails("", "String", 11, "#000000", "#ffffff", true,
////								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f)
////				)
////		);
//		/* CourseReportGenerator courseReportGenerator = context.getBean(CourseReportGenerator.class);
//		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//		String sectionIds = "18,19";
//		String courseId = "c98ac247-f49a-494b-bba9-4ba8153b82b1,10f4e85d-8517-490e-88cc-0a17945c499f";
////
//		CellIndexes cellIndexes = new CellIndexes(0, 0, 0, 3);
//		List<CellIndexes> cellIndexesList = new ArrayList<>();
//		cellIndexesList.add(cellIndexes);
//		 List<List<ReportCellDetails>> reportCellDetails = Arrays.asList(
//				Arrays.asList(
//						new ReportCellDetails("Class: 11th EM | Section: A, B, C | Subject: ", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f)
//				),
//				Arrays.asList(
//						new ReportCellDetails("S.No.", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f),
//						new ReportCellDetails("Admission No", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f),
//						new ReportCellDetails("Roll No", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f),
//						new ReportCellDetails("Name", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.MIDDLE, 0.0f)
//				),
//				Arrays.asList(
//						new ReportCellDetails("Total", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.CENTER, ReportVerticalTextAlignment.BOTTOM, 0.0f),
//						new ReportCellDetails("", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f),
//						new ReportCellDetails("", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f),
//						new ReportCellDetails("", "String", 11, "#000000", "#ffffff", true,
//								ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM, 0.0f)
//				)
//		); */
////		Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap = new HashMap<>();
////            instituteMetadataVariablesMap.put(AFFILIATION_NUMBER, "16");
////          instituteMetadataVariablesMap.put(InstituteMetadataVariables.DISE_CODE, "18");
////          System.out.println(instituteMetadataVariablesMap);
////			Institute institute= new institute(101, "embrate", "", "Gaushala Road",
////                    "Jhunjhunu (Rajasthan)", "" , "head 2", "New DELHI", "DELHI", "INDIA", "110077",
////                    "LANDMARK", "", "", "<EMAIL>", "01592- 230112", instituteMetadataVariablesMap);
//
////		List<Document<InstituteDocumentType>> instituteDocumentList = Arrays.asList(
////				new Document(
////						InstituteDocumentType.INSTITUTE_PRIMARY_LOGO,
////						"primary logo",
////						"4b403d8b-a4e4-4068-9bc8-797f5cba41e0",
////						"institute-documents/institute_id=101/document_type=INSTITUTE_PRIMARY_LOGO/4b403d8b-a4e4-4068-9bc8-797f5cba41e0.jpeg",
////						"jpeg",
////						1724418926L
////				),
////				new Document(
////						"INSTITUTE_SECONDARY_LOGO",
////						"secondary logo",
////						"a27395a0-cc73-4bec-90b0-047bcca25e6d",
////						"institute-documents/institute_id=101/document_type=INSTITUTE_SECONDARY_LOGO/a27395a0-cc73-4bec-90b0-047bcca25e6d.jpeg",
////						"jpeg",
////						1725864173L
////				),
////				new Document(
////						"INSTITUTE_WATERMARK",
////						"watermark",
////						"d0409673-6d1a-4079-94cf-9c7dd44f8d7e",
////						"institute-documents/institute_id=101/document_type=INSTITUTE_WATERMARK/d0409673-6d1a-4079-94cf-9c7dd44f8d7e.png",
////						"png",
////						1725865562L
////				)
////		);
//
//		// Create the Institute object
////		Institute institute = new Institute(
////				101,
////				UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"),
////				"SWAMI VIVEKANAND SHIKSHAN SANSTHAN",
////				null, // branchName
////				"Sample Letter Head Line 1", // letterHeadLine1
////				"Sample Letter Head Line 1", // letterHeadLine2
////				"Address Line 1",
////				"Address Line 2",
////				null, // city
////				null, // state
////				"INDIA",
////				null, // zipcode
////				null, // landmark
////				"https://lernen-documents-local-v5-1.s3.amazonaws.com/institute-documents/institute_id=101/document_type=INSTITUTE_PRIMARY_LOGO/4b403d8b-a4e4-4068-9bc8-797f5cba41e0.jpeg",
////				"https://lernen-documents-local-v5-1.s3.amazonaws.com/institute-documents/institute_id=101/document_type=INSTITUTE_SECONDARY_LOGO/a27395a0-cc73-4bec-90b0-047bcca25e6d.jpeg",
////				null, // email
////				null, // phoneNumber
////				null,
////				null,
////				null,
////				null, // instituteMetadataVariablesMap
////				"https://lernen-documents-local-v5-1.s3.amazonaws.com/"
////		);
////		Set<String> titleColorHexCodeList = new HashSet<>();
////		titleColorHexCodeList.add("#000000");
////final ReportSheetDetails reportSheetDetails = new ReportSheetDetails("StudentCourseDetailReport", true, null, null, cellIndexesList, null, reportCellDetails, true, true,institute, null ,true, false);
////		final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//		/* Institute institute = new Institute(
//				101,
//				UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"),
//				"SWAMI VIVEKANAND SHIKSHAN SANSTHAN",
//				null, // branchName
//				"Sample Letter Head Line 1", // letterHeadLine1
//				"Sample Letter Head Line 1", // letterHeadLine2
//				"Address Line 1",
//				"Address Line 2",
//				null, // city
//				null, // state
//				"INDIA",
//				null, // zipcode
//				null, // landmark
//				"https://lernen-documents-local-v5-1.s3.amazonaws.com/institute-documents/institute_id=101/document_type=INSTITUTE_PRIMARY_LOGO/4b403d8b-a4e4-4068-9bc8-797f5cba41e0.jpeg",
//				"https://lernen-documents-local-v5-1.s3.amazonaws.com/institute-documents/institute_id=101/document_type=INSTITUTE_SECONDARY_LOGO/a27395a0-cc73-4bec-90b0-047bcca25e6d.jpeg",
//				null, // email
//				null, // phoneNumber
//				null,
//				null,
//				null,
//				null, // instituteMetadataVariablesMap
//				"https://lernen-documents-local-v5-1.s3.amazonaws.com/"
//		);
//		Set<String> titleColorHexCodeList = new HashSet<>();
//		titleColorHexCodeList.add("#000000");
//final ReportSheetDetails reportSheetDetails = new ReportSheetDetails("StudentCourseDetailReport", true, null, null, cellIndexesList, null, reportCellDetails, true, true,institute, null ,true, false);
//		final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//>>>>>>> 87b65d3b97cc8f894a9acf2afc5554b29cd37409
//=======
////		 Institute institute = new Institute(
////				101,
////				UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"),
////				"SWAMI VIVEKANAND SHIKSHAN SANSTHAN",
////				null, // branchName
////				"Sample Letter Head Line 1", // letterHeadLine1
////				"Sample Letter Head Line 1", // letterHeadLine2
////				"Address Line 1",
////				"Address Line 2",
////				null, // city
////				null, // state
////				"INDIA",
////				null, // zipcode
////				null, // landmark
////				"https://lernen-documents-local-v4-1.s3.amazonaws.com/institute-documents/institute_id=101/document_type=INSTITUTE_PRIMARY_LOGO/4b403d8b-a4e4-4068-9bc8-797f5cba41e0.jpeg",
////				"https://lernen-documents-local-v4-1.s3.amazonaws.com/institute-documents/institute_id=101/document_type=INSTITUTE_SECONDARY_LOGO/a27395a0-cc73-4bec-90b0-047bcca25e6d.jpeg",
////				null, // email
////				null, // phoneNumber
////				null,
////				null,
////				null,
////				null, // instituteMetadataVariablesMap
////				"https://lernen-documents-local-v4-1.s3.amazonaws.com/"
////		);
////		Set<String> titleColorHexCodeList = new HashSet<>();
////		titleColorHexCodeList.add("#000000");
////final ReportSheetDetails reportSheetDetails = new ReportSheetDetails("StudentCourseDetailReport", true, null, null, cellIndexesList, null, reportCellDetails, true, true,institute, null ,true, false);
////		final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
////PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//>>>>>>> release
////		{\"report_type\": \"STUDENT_FEES_PAYMENT\", \"academic_session_id\": \"190\", \"required_headers\": \"\", \"multiple_academic_session_ids\": \"\", \"start_date\": \"-1\", \"end_date\": \"-1\", \"fee_due_date\": \"-1\", \"fee_ids\": \"\", \"required_standards\": \"\", \"studentStatus\": \"\", \"download_format\": None, \"fee_head_ids\": \"\", \"fee_payment_transaction_status\": \"\", \"fee_report_data_type\": \"ALL\", \"transaction_mode\": \"\", \"hide_student_with_zero_fees_assignment\": \"false\"}
////[10/Sep/2024 16:53:59] "GET /b1812075-526a-4d5c-817b-d8eb7953f6bc/fees/generate-report/STUDENT_FEES_PAYMENT?academic_session_id=190&requiredHeaders=&requiredMultiSession=&reportStartDate=-1&reportEndDate=-1&fee_ids=&requiredStandards=&feeDueDate=-1&studentStatus=&fee_head_ids=&transactionType=&feeReportDataType=ALL&transactionMode=&hideStudentWithZeroFeesAssignment=false
////		studentStatusCSV,feeHeadIds,multipleAcademicSessionIds, feeReportDataType,transactionMode, hideStudentWithZeroFeesAssignment);
//
//
////		final ReportDetails reportDetails  = feeReportDetailsGenerator.generateReport(101, 190, STUDENT_FEES_PAYMENT, -1, -1, -1, null, null, null, null, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), DownloadFormat.PDF, null, null, null, FeeReportDataType.ALL, null, false);
////
////		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		System.out.println("raw generated");
////		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////		&requiredMultiSession=&reportStartDate=-1&reportEndDate=-1&fee_ids=&requiredStandards=&feeDueDate=-1&studentStatus=&fee_head_ids=&transactionType=&feeReportDataType=ALL&transactionMode=&hideStudentWithZeroFeesAssignment=false
////		final ReportDetails reportDetails  = feeReportDetailsGenerator.generateReport(101, 190, STUDENT_FEES_HEAD_PAYMENT, -1, -1, -1, null, null, null, "sr_no,registration_no,admission_no,name,status,class,father_name,primary_contact,whatsapp_number,father_contact,mother_contact,fees_head_name,assigned_amount,amount_paid,given_discount,discount_to_be_given,due_amount", UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), DownloadFormat.PDF, null, null, null, FeeReportDataType.ALL, null, false, ReportRowColumnDataVisibility.COLUMN_WISE);
////
////		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		System.out.println("raw generated");
////		try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
//
////		CharacterCertificateDocumentHandler characterCertificateDocumentHandler = context.getBean(CharacterCertificateDocumentHandler.class);
////		IncomeExpenseReportGenerator incomeExpenseReportGenerator = context.getBean(IncomeExpenseReportGenerator.class);
////		PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//
////		85c34583-3545-4c8b-b3e0-ab041eac3559
//
////		final ReportDetails reportDetails = incomeExpenseReportGenerator.generateReport(101, NET_COLLECTION_REPORT_BY_MODE, 1711909800, 1717007400, null, UUID.fromString("85c34583-3545-4c8b-b3e0-ab041eac3559"), DownloadFormat.PDF);
////		final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////		System.out.println("raw generated");
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = Files.newOutputStream(Paths.get(DEST2))) {
////
////           byteArrayOutputStream.writeTo(outputStream);
////        }
//
////
////        documentOutput = feeInvoiceHandler.generateInvoice(10030,
////                UUID.fromString("aa7498b1-42a6-4ade-ac20-f51fb985415e"), true, "ADMIN");
//
////        final ComplainBoxManager complainBoxManager = context.getBean(ComplainBoxManager.class);
////        DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(10030,
////                UUID.fromString("2f62158e-6515-487f-8f2a-02e11826f442"), true, "ADMIN");
////
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(sourceFolder1)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
////
////        documentOutput = feeInvoiceHandler.generateInvoice(10030,
////                UUID.fromString("aa7498b1-42a6-4ade-ac20-f51fb985415e"), true, "ADMIN");
////
////        byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
//
////        List<StudentComplaintMetadataDetails> complainDetails = complainBoxManager.getComplainDetails(702,1,null);
////
////        System.out.println(complainDetails.get(0).getComplainCategory());
//
////        final ExperienceLetterDocumentHandler experienceLetterDocumentHandler = context.getBean(ExperienceLetterDocumentHandler.class);
////        final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////        DocumentOutput documentOutput = experienceLetterDocumentHandler.generateExperienceLetter(10030,
////                UUID.fromString("ef93d13e-5000-45fb-a29a-2a29ffd8ad93"));
////        final StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
////
////        int instituteId = 10030;
////        String reportTypeStr = "STAFF_MONTHLY_ATTENDANCE_SUMMARY";
//////        String reportTypeStr = "STAFF_ATTENDANCE_DETAILS_IN_A_DAY";
////        String attendanceStatusStr = null;
////        String staffCategoriesStr = null;
////        int start = 1675189800;
////        int end = 1677522600;
////        UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
////        DownloadFormat downloadFormat = DownloadFormat.PDF;
////        final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(instituteId,
////                reportTypeStr, attendanceStatusStr, staffCategoriesStr, start, end, userId, downloadFormat);
//
//
////        DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
//
//
////
////        final AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
//
////        int instituteId = 702;
////        Integer academicSessionId = 55;
////        FeesReportType feesReportType = FeesReportType.STUDENT_AGGREGATED_PAYMENT;
////        Integer start = 1700850600;
////        Integer end = 1701369000;
////        Integer feeDueDate = -1;
////        String feeIdsStr = null;
////        String requiredHeaders = "sr_no,admission_no,name,class,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount";
////        String requiredStandardsCSV = null;
////        FeePaymentTransactionStatus feePaymentTransactionStatus = null;
////        UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
////        DownloadFormat downloadFormat = null;
////        String studentStatusCSV = null;
////        String feeHeadIdsStr = null;
////        String multipleAcademicSessionIdsStr = null;
////        boolean includeDueStudentOnly = false;
////        ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId,
////                academicSessionId, "ATTENDANCE_DETAILS_IN_A_MONTH",null, null, null,
////                start, end,null, null, downloadFormat);
//
//		// DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////        final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(702,UUID.fromString("003196a1-bb80-4056-884a-f2d638e2770c"), true, null);
////        ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////        final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(101, 33,
////                UUID.fromString("3b7cf309-cb33-4196-bad8-7783c033007c"), "UNIT_TEST_II", true,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
////        BirthdayCertificateDocumentHandler birthdayCertificateDocumentHandler = context.getBean(BirthdayCertificateDocumentHandler.class);
////
////        DocumentOutput documentOutput = birthdayCertificateDocumentHandler.generateBirthdayCertificate(702, 55, UUID.fromString("ff53f4f3-441a-437f-b800-83dcc9897470"));
//
////         DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
//
////        final DocumentOutput documentOutput = admitCardHandler.generateAdmitCards(
////                10030, UUID.fromString("57355b74-6bf8-4a52-8ea3-4842aa3a5a40"),
////                null, AdmitCardType.ADMIT_CARD_WITH_DATESHEET, null);
////        DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(702, 55,
////                UUID.fromString("ff53f4f3-441a-437f-b800-83dcc9897470"), "HALF_YEARLY_265", true,
////               null);
//
////         ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////         try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////             byteArrayOutputStream.writeTo(outputStream);
////         }
//
////        SearchResultWithPagination<DiaryRemarkDetails> remarkDetails = studentDiaryManager.getRemarkDetails(702,55, null,null,null, 10, 10);
////         System.out.println(remarkDetails.getResult().size());
////         ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////         try (OutputStream outputStream = new FileOutputStream(sourceFolder)) {
////             byteArrayOutputStream.writeTo(outputStream);
////         }
//
////                final ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////                  final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(702, 55,
////                 UUID.fromString("ff53f4f3-441a-437f-b800-83dcc9897470"), "HALF_YEARLY", true,
////                 null);
//
////                documentOutput = examReportSerivceProvider.getExamReport(101, 33,
////                UUID.fromString("3b7cf309-cb33-4196-bad8-7783c033007c"), "UNIT_TEST_II", true,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
////
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////         try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////             byteArrayOutputStream.writeTo(outputStream);
////         }
//
////        StandardComplainResponse responsePayload = new StandardComplainResponse(702, UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"),"Hello it\"s me",1);
////        boolean res = complainBoxManager.deleteStandardResponse(UUID.fromString("a9c80e90-7755-4555-bd30-441dbde0e901"),702, UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"));
//////        StudentComplaintMetadata complainMetadata = new StudentComplaintMetadata(702, UUID.fromString("cc9011c7-bd39-4648-99ba-e459d120b8b3"), "Complain", "Updated Description", "C-101",UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"),null, StatusType.OPEN,1,null,UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"));
////        StudentComplaintResponses response = new StudentComplaintResponses(null, "hello",UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"), UUID.fromString("cc9011c7-bd39-4648-99ba-e459d120b8b3"), null);
////        boolean categoryList = complainBoxManager.deleteComplainResponse(UUID.fromString("7dbde522-07d4-498d-a2b0-f8b8298b081d"),UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"));
////        System.out.println(categoryList);
//
////        DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
////        final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
////
////        int instituteId = 101;
////        int academicSessionId = 33;
////        ExamReportType examReportType = ExamReportType.RESULT_SUMMARY_REPORT;
////        UUID standardId = UUID.fromString("cb352822-3b78-44da-b513-8332b900dc46");
////        Integer sectionId = null;
//////			UUID examId = UUID.fromString("5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6");
////        UUID examId = UUID.fromString("ccdc7962-b852-4906-a800-a2cab26230b");
////        UUID courseId = null;
////        CourseType courseType = null;
////        UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
////        DownloadFormat downloadFormat = DownloadFormat.EXCEL;
////        String reportCardType = null;
////        UUID staffId = null;
//////        String sectionIdSetStr = "83,84";
////        String sectionIdSetStr = null;
////        String examIdSetStr = "5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6";
//////			String examIdSetStr = "ccdc7962-b852-4906-a800-a2cab26230b3";
//////        	String courseIdSetStr = "e10d8bc8-83be-4958-979e-26e911087f4b,8d7dcdbe-8fdf-488d-a6b1-55e77d7b38bb,4be32469-6cd6-4a0a-8cad-79618f3f6ab0";
////        String courseIdSetStr = null;
//////			String compareCumulativeWithExamIdSetStr = "ccdc7962-b852-4906-a800-a2cab26230b3,5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6";
//////			String compareCumulativeWithExamIdSetStr = "5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6";
//////        String compareCumulativeWithExamIdSetStr = null;
//////        Integer rankTill = 4;
//////        ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//////                standardId, sectionId, examId, courseId, courseType, userId, downloadFormat, reportCardType, staffId,
//////                sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr, rankTill);
////        String compareCumulativeWithExamIdSetStr = null;
////        Integer rankTill = 4;
////        boolean excludeCoScholasticSubjects = false;
////        boolean showCoScholasticGrade = true;
////        boolean showScholasticGrade = true;
////        String requiredHeaders = "sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result";
////        ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
////                standardId, sectionId, examId, courseId, courseType, userId, downloadFormat, reportCardType, staffId,
////                sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr, rankTill,
////                excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders);
//
//
////        DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST3)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
//
////        final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//
////        /2.0/examination-reports/10030/exam/a1ad15d0-9eba-40f7-95e6-9bda509cfd93?section_id_str=&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&display_rank=false&show_coscholastic_grade=false&display_attendance=false&show_scholastic_grade=false&user_id=4cb09c60-eabc-4192-a9d4-e357cf773db3&download_format=PDF
////        ReportDetails reportDetails = examReportGenerator.getClassExamReport(10030,
////                UUID.fromString("a1ad15d0-9eba-40f7-95e6-9bda509cfd93"), null,
////                false, false, false, false, false,
////                false, false, DownloadFormat.PDF, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
////        /2.0/examination-reports/fa1c9752-2edd-49b1-8e88-22c02a3e288c/course/8f7c6026-61f0-41e5-9da0-e6ab92f96848?section_id=83&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&institute_id=10030&academic_session_id=32&user_id=4cb09c60-eabc-4192-a9d4-e357cf773db3&download_format=PDF
//
////        ReportDetails reportDetails = examReportGenerator.getClassCourseWiseExamReport(10030, 32,
////                UUID.fromString("fa1c9752-2edd-49b1-8e88-22c02a3e288c"), 83,
////                UUID.fromString("8f7c6026-61f0-41e5-9da0-e6ab92f96848"), false, false, false, DownloadFormat.PDF,
////                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
////        ReportDetails reportDetails = examReportGenerator.getClassExamReport(10030,
////                UUID.fromString("a1ad15d0-9eba-40f7-95e6-9bda509cfd93"), null,
////                false, false, false, false, false,
////                false, false, DownloadFormat.PDF, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
////        System.out.println(reportDetails);
////         final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////         ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////         try (OutputStream outputStream = new FileOutputStream(DEST3)) {
////             byteArrayOutputStream.writeTo(outputStream);
////         }
//
////         final IncomeExpenseManager incomeExpenseManager = context.getBean(IncomeExpenseManager.class);
////         final FeePaymentInsightManager feePaymentInsightManager = context.getBean(FeePaymentInsightManager.class);
////         final UserWalletManager userWalletManager = context.getBean(UserWalletManager.class);
////        final StudentManager studentManager = context.getBean(StudentManager.class);
////        final InventoryTransactionsManager inventoryTransactionsManager = context.getBean(InventoryTransactionsManager.class);
////        final UserManager userManager = context.getBean(UserManager.class);
////        final UserPermissionManager userPermissionManager = context.getBean(UserPermissionManager.class);
//
//
////        IncomeExpenseReportGenerator incomeExpenseReportGenerator = new IncomeExpenseReportGenerator(incomeExpenseManager,feePaymentInsightManager,userWalletManager,studentManager,inventoryTransactionsManager,userManager,userPermissionManager);
////         ReportDetails reportDetails = incomeExpenseReportGenerator.generateReport(101, IncomeExpenseReportType.USER_NET_COLLECTION_REPORT,1535740200,1695666600, IncomeExpenseTransactionType.INCOME,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"),DownloadFormat.PDF);
//
////        CourseReportGenerator courseReportGenerator =context.getBean(CourseReportGenerator.class);
////        Set<UUID> courseIdSet = new HashSet<>();
////        courseIdSet.add(UUID.fromString("82ff6af4-3eab-4e94-ad71-4f06564a7232")); eng
////        courseIdSet.add(UUID.fromString("a9e65f4b-04d4-4e9c-8305-e414016630b0")); arts
////        courseIdSet.add(UUID.fromString("9634ca62-5656-493d-a3f2-ee475c682c7a")); evs
////        courseIdSet.add(UUID.fromString("0743f81f-c88a-4b8f-9e1e-2b75bb76e0af")); hin
////        courseIdSet.add(UUID.fromString("8b0fdb7b-37ea-412b-9e93-41d176003a50")); math
////        courseIdSet.add(UUID.fromString("ea375990-5869-425d-b308-b156fe0392e5")); sup
//
//		//lkg
////        c9fd04de-3229-4050-b99f-c5a8b7ee0459 - con
////        647edaaa-0b57-4127-97b8-95dabb8a93ac - dra
////        c4515bb5-7e5f-43a4-88aa-a20a581be6d7 - eng
////        5fe312bc-a252-4b9e-8744-25a1f8c61cda - eng(o)
////        102db340-bc4e-4611-b36f-66771d3c70bd - hin
////        c8a11b26-e045-4780-9ce5-c5d8226f2250 - hin(0)
////        cf5ef859-5827-4a32-8242-36455b1c1dc6 - mat
////        76c205fa-c993-453a-bc94-162d122791a1 - opt
////        e5f9b126-d105-478c-995f-62585bc2bbfc - mat(o)
////        ReportDetails reportDetails = courseReportGenerator.generateReport(101,53,UUID.fromString("4e64f4b0-7828-4c74-8635-188498709387"),null,"a9e65f4b-04d4-4e9c-8305-e414016630b0,9634ca62-5656-493d-a3f2-ee475c682c7a,8b0fdb7b-37ea-412b-9e93-41d176003a50,ea375990-5869-425d-b308-b156fe0392e5",DownloadFormat.PDF,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"),CourseReportType.STUDENT_COURSE_DETAIL_REPORT);
////
////        ReportDetails reportDetails = courseReportGenerator.generateReport(101,53,UUID.fromString("f7324394-d821-43af-9ade-d8e6237dfbc3"),null,null,DownloadFormat.PDF,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"),CourseReportType.STUDENT_COURSE_DETAIL_REPORT);
////        ReportDetails reportDetails = courseReportGenerator.generateReport(101,53,UUID.fromString("f7324394-d821-43af-9ade-d8e6237dfbc3"),null,null,DownloadFormat.PDF,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"),CourseReportType.STUDENT_COURSE_DETAIL_REPORT);
////        ReportDetails reportDetails = courseReportGenerator.generateReport(101,53,UUID.fromString("4e64f4b0-7828-4c74-8635-188498709387"),"502", "82ff6af4-3eab-4e94-ad71-4f06564a7232,a9e65f4b-04d4-4e9c-8305-e414016630b0,ea375990-5869-425d-b308-b156fe0392e5,8b0fdb7b-37ea-412b-9e93-41d176003a50",DownloadFormat.PDF,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"), CourseReportType.STUDENT_COURSE_DETAIL_REPORT);
////        final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////
////
////        IncomeExpenseReportGenerator incomeExpenseReportGenerator = new IncomeExpenseReportGenerator(incomeExpenseManager,feePaymentInsightManager,userWalletManager,studentManager,inventoryTransactionsManager,userManager,userPermissionManager);
////         ReportDetails reportDetails = incomeExpenseReportGenerator.generateReport(101, IncomeExpenseReportType.USER_NET_COLLECTION_REPORT,1535740200,1695666600, IncomeExpenseTransactionType.INCOME,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"),DownloadFormat.PDF);
////
////        final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST7)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
//
////        final InstituteManager instituteManager = context.getBean(InstituteManager.class);
////        final StudentManager studentManager = context.getBean(StudentManager.class);
////        final ExamReportCardManager examReportCardManager = context.getBean(ExamReportCardManager.class);
////        final UserPermissionManager userPermissionManager = context.getBean(UserPermissionManager.class);
////        ExamReportSerivceProvider examReportSerivceProvider = new ExamReportSerivceProvider(examReportCardManager,
////                instituteManager, studentManager, userPermissionManager);
//
////         ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
////         DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10030, 32,
////                 UUID.fromString("76ee2928-9df3-4710-bead-5e14a66e2273"), "ANNUAL", true,
////                 UUID.fromString("14cb09c60-eabc-4192-a9d4-e357cf773db3"));
////         ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////         try (OutputStream outputStream = new FileOutputStream(DEST7)) {
////             byteArrayOutputStream.writeTo(outputStream);
////         }
//
////        final AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
////        final PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//
////        final BoardRegistrationFormHandler registrationFormHandler = context.getBean(BoardRegistrationFormHandler.class);
////        final DocumentOutput documentOutput = registrationFormHandler.generateBoardRegistrationForm(702,0, UUID.fromString("ff53f4f3-441a-437f-b800-83dcc9897470"));
////			//generate-report/ATTENDANCE_DETAILS_IN_A_DAY?academic_session_id=32&standard_id=&attendance_status=&attendance_type_id=&startDate=1672511400&endDate=1691605800&downloadReport=true&downloadFormat=PDF
//
//        {\"instituteId\": \"101\", \"academicSessionId\": 53, \"dueDate\": 1696617000, \"computeFine\": False, \"requiredStanardsCSV\": \"\", \"studentIds\": [\"6f887033-68c9-4328-904c-74e2fc6a26be\"]}
////        FeePaymentReminderPayload feePaymentReminderPayload = new FeePaymentReminderPayload();
////        feePaymentReminderPayload.setInstituteId(101);
////        feePaymentReminderPayload.setAcademicSessionId(53);
////        feePaymentReminderPayload.setDueDate(1696617000);
////        feePaymentReminderPayload.setComputeFine(false);
////        feePaymentReminderPayload.setStudentIds(Arrays.asList(UUID.fromString("6f887033-68c9-4328-904c-74e2fc6a26be")));
////        final DemandNoticeHandler demandNoticeHandler = context.getBean(DemandNoticeHandler.class);
//
//////        final DocumentOutput documentOutput =  demandNoticeHandler.generateBulkDemandNotices(101,feePaymentReminderPayload,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"));
//////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST7)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
//
////        final DocumentOutput documentOutput =  demandNoticeHandler.generateBulkDemandNotices(101,feePaymentReminderPayload,UUID.fromString("1d71b5b8-a5b2-4613-a058-b22bd9c7f626"));
//
////        int instituteId = 101;
////        int academicSessionId = 33;
////        String reportType = "ATTENDANCE_DETAILS_IN_A_MONTH";
////        String attendanceStatusStr = null;
////        String attendanceTypeIdStr = null;
////        String standardIdStr = null;
////        Integer start = 1688149800;
////        Integer end = 1690741800;
////        UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
////        DownloadFormat downloadFormat = DownloadFormat.PDF;
//
////        int instituteId = 702;
////        int academicSessionId = 55;
////        String reportType = "SINGLE_DAY_ATTENDANCE_REPORT";
////        String attendanceStatusStr = null;
////        String attendanceTypeIdStr = "24,26";
////        String standardIdStr = null;
////        Integer start = 1693765800;
////        Integer end = 1693765800;
////        UUID userId = UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30");
////        DownloadFormat downloadFormat = DownloadFormat.PDF;
////
////=======
////		final ReportDetails reportDetails = attendanceReportGenerator.generateReport(101, 190
////				, "MONTHLY_ATTENDANCE_SUMMARY_REPORT","4dbc60bd-b6ea-44a1-8a4f-3505f0390d65", "", "", "May-2024",
////				0, 0, -1, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), DownloadFormat.PDF, "attendance_sr_no,attendance_student_name,attendance_admission_no,attendance_student_class,attendance_student_roll_number,total_days,present_count,absent_count,leave_count,half_day_count,holiday_count,unmarked_count");
////		final DocumentOutput documentOutput = reportDetails;
////         ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId, academicSessionId, reportType,
////         System.out.println(reportDetails);
////         ByteArrayOutputStream byteArrayOutputStream = reportDetails.getContent();
////         try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////             byteArrayOutputStream.writeTo(outputStream);
//// =======
////         int academicSessionId = 55;
////         String attendanceStatusStr = null;
////         String attendanceTypeIdStr = null;
////         String standardIdStr = "c0e0b9dd-323b-42e7-8101-afbf22aa6f5b";
////         Integer start = 1690828200;
////         Integer end = 1690828200;
////         UUID userId = UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30");
////         DownloadFormat downloadFormat = DownloadFormat.PDF;
//
////        ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId, academicSessionId, reportType,
////                standardIdStr, attendanceStatusStr, attendanceTypeIdStr, start, end, userId, downloadFormat);
//////        System.out.println(reportDetails);
////        final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
////		final UserPermissionManager userPermissionManager = context.getBean(UserPermissionManager.class);
////		final ExaminationManager examinationManager = context.getBean(ExaminationManager.class);
////		final StudyCertificateDocumentHandler studyCertificateDocumentHandler = context.getBean(StudyCertificateDocumentHandler.class);
////		final PromotionCertificateDocumentHandler promotionCertificateDocumentHandler = context.getBean(PromotionCertificateDocumentHandler.class);
////		final ProductTransactionsManager productTransactionsManager = context.getBean(ProductTransactionsManager.class);
////		final ExamReportCardManager examReportCardManager = context.getBean(ExamReportCardManager.class);
//
////        AdmitCardHandler admitCardHandler = context.getBean(AdmitCardHandler.class);
////        List<UUID> studentUUIDs = new ArrayList<>();
////        studentUUIDs.add(UUID.fromString("050d059e-**************-3f6c96801650"));
////        studentUUIDs.add(UUID.fromString("164b27d3-0943-4778-b9c3-c9cc9de6a744"));
////        studentUUIDs.add(UUID.fromString("267a015f-2720-4440-b352-b665920e53f3"));
////        studentUUIDs.add(UUID.fromString("50e3f026-82b7-4e05-9141-1a6cf182b263"));
////        studentUUIDs.add(UUID.fromString("562e0c41-33af-44cd-8ab3-3c8667019133"));
////        studentUUIDs.add(UUID.fromString("784fc395-631c-4ec2-ab0c-fb0abfa8d345"));
////        studentUUIDs.add(UUID.fromString("8fdf390c-811d-4491-85c2-3e0d3cf82233"));
////        studentUUIDs.add(UUID.fromString("9fa9414b-638b-4aba-bd16-89397d9f7764"));
////        studentUUIDs.add(UUID.fromString("caa78a8d-f280-4ec6-85e8-e91c1e7c3eb3"));
////        studentUUIDs.add(UUID.fromString("d0dd15da-ec0d-4702-b20f-a10551ecdbef"));
////        studentUUIDs.add(UUID.fromString("d234c75f-46ba-44f5-8c67-52102034203c"));
////        studentUUIDs.add(UUID.fromString("d86f39d8-7245-48e0-aeb0-2136269ea6e9"));
////        studentUUIDs.add(UUID.fromString("ee09f9a5-a17f-41c1-9090-fccecd14e48a"));
////        DocumentOutput documentOutput = admitCardHandler.generateAdmitCards(10030,
////                UUID.fromString("97f90b49-4fab-41ea-9961-978391d94547"),
////                null, AdmitCardType.ADMIT_CARD_WITH_DATESHEET, studentUUIDs);
//
////        StudentFeesChartHandler studentFeesChartHandler = context.getBean(StudentFeesChartHandler.class);
////        Set<UUID> studentUUIDs = new HashSet<>();
////        studentUUIDs.add(UUID.fromString("27eb74cd-93ce-44ad-a503-4b74e467f989"));
////        studentUUIDs.add(UUID.fromString("3ef6c9b4-7ad2-47bb-ac4f-5234275d7493"));
////        studentUUIDs.add(UUID.fromString("85754f69-2c82-4740-8d57-0d9a98966e7c"));
////        studentUUIDs.add(UUID.fromString("8d14bbe0-18ed-418d-a4ce-2ed3b608a5f7"));
////        studentUUIDs.add(UUID.fromString("93b34555-9481-48b1-b77b-8e115ecee3f1"));
////        studentUUIDs.add(UUID.fromString("b5e5ebc6-79a9-47d1-af31-72ce5d3266d1"));
////        studentUUIDs.add(UUID.fromString("ff53f4f3-441a-437f-b800-83dcc9897470"));
//////        studentUUIDs.add(UUID.fromString("ffc2e601-0282-4e09-b696-eb915539f74d4"));
////        DocumentOutput documentOutput = studentFeesChartHandler.generateStudentFeesChart(702,55,
////                 studentUUIDs, 2,true, true, true);
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////            byteArrayOutputStream.writeTo(outputStream);
////        }
//
////        TuitionFeesCertificateDocumentHandler tuitionFeesCertificateDocumentHandler = context.getBean(TuitionFeesCertificateDocumentHandler.class);
////        final DocumentOutput documentOutput = tuitionFeesCertificateDocumentHandler.generateTuitionFeesCertificate(101,
////		UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), 256);
//
////        PromotionCertificateDocumentHandler promotionCertificateDocumentHandler = context.getBean(PromotionCertificateDocumentHandler.class);
////        final DocumentOutput documentOutput = promotionCertificateDocumentHandler.getStandardPromotionCertificatePDF(101, UUID.fromString("0229e91a-8524-4198-801f-da0010b0e225"), 53);
////        CharacterCertificateDocumentHandler characterCertificateDocumentHandler = context.getBean(CharacterCertificateDocumentHandler.class);
////        final DocumentOutput documentOutput = characterCertificateDocumentHandler.generateCharacterCertificate(101, UUID.fromString("0229e91a-8524-4198-801f-da0010b0e225"), 53);
//
////        Student student = studentManager.getStudentByAcademicSessionStudentId(10120, 97, UUID.fromString("4d716483-f3fa-4921-8fa3-62e12506d209"));
//
////        ExamReportSerivceProvider examReportSerivceProvider = new ExamReportSerivceProvider(examReportCardManager, instituteManager, studentManager, userPermissionManager);
////        GlobalTwoPageAdmissionFormGenerator globalTwoPageAdmissionFormGenerator = new GlobalTwoPageAdmissionFormGenerator();
////        DocumentOutput documentOutput = globalTwoPageAdmissionFormGenerator
////                .generateAdmissionForm(studentManager, instituteManager.getInstitute(10045), instituteManager.getAcademicSessionByAcademicSessionId(40) , false, student, "sds");
//
////        DocumentOutput documentOutput = globalTwoPageAdmissionFormGenerator
////                .generateAdmissionForm(studentManager, instituteManager.getInstitute(10045), instituteManager.getAcademicSessionByAcademicSessionId(40) ,
////                        false, null, "sds");
//
////        ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////        try (OutputStream outputStream = new FileOutputStream(DEST10)) {
////           byteArrayOutputStream.writeTo(outputStream);
////        }
////
////
//
//
////		FeePaymentInvoiceSummary feePaymentInvoiceSummary = feePaymentManager.getTransactionInvoiceSummary(10015,
////				UUID.fromString("d64a5114-a982-43ba-b979-7f6ec306d218"), true);
////		FeeInvoiceGenerator globalFeeInvoiceGenerator = new FeeInvoiceGenerator10030();
////		DocumentOutput documentOutput = globalFeeInvoiceGenerator.generateInvoice(instituteManager.getInstitute(10030),
////				feePaymentInvoiceSummary, "ABC", false);
////
//////
////		final List<StudentDueFeesData> studentDueFeesDetailsList = feePaymentManager.getDueFeesStudents(10020, 9,
////				**********, false, "");
////		System.out.println(studentDueFeesDetailsList.size());
////		GlobalDemanNoticeGenerator globalDemanNoticeGenerator = new GlobalDemanNoticeGenerator();
////		DocumentOutput documentOutput = globalDemanNoticeGenerator
////				.generateDemandNotices(instituteManager.getInstitute(10015), studentDueFeesDetailsList, "ABC");
////
////		ExamReportSerivceProvider examReportSerivceProvider = new ExamReportSerivceProvider(examReportCardManager,
////				instituteManager, studentManager, userPermissionManager);
//
//
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10030, 10,
////				UUID.fromString("e12a39b7-57e8-470b-9125-89641092074b"), "ANNUAL", true,
////				UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), true);
//
//
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10030, 10,
////				UUID.fromString("caa78a8d-f280-4ec6-85e8-e91c1e7c3eb3"), "ANNUAL", true,
////				UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), false);
//
//
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10015, 7,
////				UUID.fromString("d3d9f213-c2c2-4656-8c8d-91b0ce869882"), "TERM-I", true);
////
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10005,8,
////				UUID.fromString("7ac5f466-6b8a-42d3-bfbe-2b772f9c7293"), "ANNUAL", false, null);
//
//		// Nursery
//
////		DocumentOutput documentOutput1 = examReportSerivceProvider.getExamReport(10001,11,
////				UUID.fromString("3c0f5894-4b57-46f3-9bb2-b6d53dafe02d"), "HALF_YEARLY", false);
//
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10001, 11,
////				UUID.fromString("3c0f5894-4b57-46f3-9bb2-b6d53dafe02d"), "HALF_YEARLY", false, null);
//
//		// Sixth
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10020, 9,
////				UUID.fromString("dcd9aefa-b860-4065-aa78-b6b85f1391c2"), "HALF_YEARLY", true, null);
//
//		// Ninth
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10020, 9,
////				UUID.fromString("1c797b39-73c9-48bd-a491-51fcc4508c53"), "HALF_YEARLY", true, null);
//
////
//		// First
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10001,11,
////				UUID.fromString("b58e3a50-12ad-4975-b244-72ebd4f7462b"), "HALF_YEARLY", false, null);
//
//		// Sixth
////		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10001,11,
////				UUID.fromString("f986b691-194f-4779-9521-4d4afa291b6d"), "HALF_YEARLY", false);
////
//		// Ninth
////		DocumentOutput documentOutput3 = examReportSerivceProvider.getExamReport(10001,11,
////				UUID.fromString("9834a145-89e3-471a-944c-26f6815cb214"), "HALF_YEARLY", false);
////
////
////		// Eleventh
////		DocumentOutput documentOutput4 = examReportSerivceProvider.getExamReport(10001,11,
////				UUID.fromString("e6f2bdbf-ff5c-4986-9fd3-0b2a93c6117a"), "HALF_YEARLY", false);
////
//
////		DocumentOutput documentOutput = examReportSerivceProvider.getClassExamReport(10001, 11,
////				UUID.fromString("4cb99b40-8d62-4114-8034-7234872d272b"), "HALF_YEARLY");
//
//		//
//		// TransferCertificateGenerator transferCertificateGenerator = new
//		// GlobalTransferCertificateGenerator();
//		// Student student = studentManager.getStudentByLatestAcademicSession(
//		// UUID.fromString("c4464dfb-d906-4ef7-a232-3a11bbe885a0"),
//		// StudentStatus.RELIEVED);
//		//
//		// DocumentOutput documentOutput = transferCertificateGenerator
//		// .generateTransferCertificate(instituteManager.getInstitute(10005),
//		// student, "TC1");
//		//
//		// // FeePaymentInvoiceSummary feePaymentInvoiceSummary =
//		// // feePaymentManager.getTransactionInvoiceSummary(10015,
//		// // UUID.fromString("66e91692-f93b-4354-a4bc-fcf6d2731fdb"));
//		// // FeeInvoiceGenerator globalFeeInvoiceGenerator = new
//		// // GlobalFeeInvoiceGenerator();
//		// // DocumentOutput documentOutput =
//		// //
//		// globalFeeInvoiceGenerator.generateInvoice(instituteManager.getInstitute(10015),
//		// // feePaymentInvoiceSummary, "ABC", false);
//		//
//
//		// ByteArrayOutputStream byteArrayOutputStream =
//		// documentOutput.getContent();
//		// try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//		// byteArrayOutputStream.writeTo(outputStream);
//		// }
//		// ReportCard rc = new ReportCard();
//		// ByteArrayOutputStream byteArrayOutputStream =
//		// rc.generateReportCard(instituteManager.getInstitute(10015),
//		// studentManager.getStudentByLatestAcademicSession(
//		// UUID.fromString("ff724e1d-1b35-4186-84fd-8c2181e2c7cb"),
//		// StudentStatus.ENROLLED),
//		// "documentName").getContent();
//		// try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//		// byteArrayOutputStream.writeTo(outputStream);
//		// }
////
//
////		GlobalAdmitCardGenerator globalAdmitCardGenerator = new GlobalAdmitCardGenerator();
////		GlobalStudentIdentityCardGenerator globalStudentIdentityCardGenerator = new GlobalStudentIdentityCardGenerator();
////		List<Student> students = new ArrayList<Student>();
////		students = studentManager.getStudentsInAcademicSesison(10001, 11);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		students.add(null);
////		DocumentOutput documentOutput = globalAdmitCardGenerator.generateAdmitCard(instituteManager.getInstitute(10030),
////				students, null, "sds");
//
////		StudentIdentityCardPreferences studentIdentityCardPreferences = new StudentIdentityCardPreferences();
////		studentIdentityCardPreferences.setThemeColor("#ffd27f");
////		studentIdentityCardPreferences.setInstituteNameFontSize(10f);
////		studentIdentityCardPreferences.setInstituteLogoWidth(30f);
////		studentIdentityCardPreferences.setInstituteLogoHeight(30f);
////		DocumentOutput documentOutput = globalStudentIdentityCardGenerator.generateIdentityCards(studentManager,
////				instituteManager.getInstitute(10030), studentIdentityCardPreferences, students, "sds");
//
////		GlobalAdmissionFormGenerator globalAdmissionFormGenerator = new GlobalAdmissionFormGenerator();
////		AdmissionFormGenerator10030 admissionFormGenerator10030 = new AdmissionFormGenerator10030();
////		DocumentOutput documentOutput = admissionFormGenerator10030
////				.generateAdmissionForm(studentManager, instituteManager.getInstitute(10030), students.get(0), "sds");
//
////		UUID.fromString("186edcb3-9136-40d0-8622-3e88fe1d509c")
////		final List<StudentExamMarksDetails> studentExamMarksDetails = examinationManager.getResolvedClassMarks(10001,
////				UUID.fromString("c4ef1294-0331-4b44-a37c-cbba507ff236"),
////				null, null);
////		StudentMarksFeedDocument studentMarksFeedDocument = new StudentMarksFeedDocument();
////
////		Standard standard = instituteManager.getInstituteStandardList(10001).get(0);
////		DocumentOutput documentOutput = studentMarksFeedDocument.generateStudentMarksFeedDocument(
////				instituteManager.getInstitute(10030), standard, null, "sds",
////				studentExamMarksDetails.get(0).getExamMetaData(), studentExamMarksDetails, 5);
//
////		DocumentOutput documentOutput = studyCertificateDocumentHandler.generateStudyCertificate(10030, UUID.fromString("845db80f-7167-43b7-887d-f8b1bd466bd9"), 10);
////		DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(10020, UUID.fromString("99cbf080-f459-4d29-83b5-763563e77bf3"));
//
////		DocumentOutput documentOutput = promotionCertificateDocumentHandler
////				.getStandardPromotionCertificatePDF(10030, UUID.fromString("3d974988-2f39-403e-a0f8-fd83241a959d"), 10);
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
//
//
////		GlobalStoreInventoryInvoiceGenerator globalSalesOrderInvoiceGenerator = new GlobalStoreInventoryInvoiceGenerator();
////		DocumentOutput documentOutput = globalSalesOrderInvoiceGenerator
////				.generateInvoice(instituteManager.getInstitute(10045), productTransactionsManager.getTransactionDetails(UUID.fromString("367811da-df33-4d23-8c6a-74f3985b1e81")), "sales-invoice", true);
////		DocumentOutput documentOutput = globalSalesOrderInvoiceGenerator
////				.generateInvoice(instituteManager.getInstitute(10045), productTransactionsManager.getTransactionDetails(UUID.fromString("be37c014-373c-4c49-afb1-************")), "sales-invoice", true);
////		DocumentOutput documentOutput = globalSalesOrderInvoiceGenerator
////				.generateInvoice(instituteManager.getInstitute(10045), productTransactionsManager.getTransactionDetails(UUID.fromString("595ec161-2377-47f0-9735-6c0bf50f40fc")), "sales-invoice", true);
//
////		DocumentOutput documentOutput = globalSalesOrderInvoiceGenerator
////				.generateInvoice(instituteManager.getInstitute(10045), productTransactionsManager.getTransactionDetails(UUID.fromString("4b22bf96-9ca5-4fa4-8476-b1baf5027966")), "sales-invoice", true);
//
////		DocumentOutput documentOutput = globalSalesOrderInvoiceGenerator
////				.generateInvoice(instituteManager.getInstitute(10045), productTransactionsManager.getTransactionDetails(UUID.fromString("90bcc619-**************-d36561fe0703")), "sales-invoice", true);
//
//
////		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST2)) {
////			byteArrayOutputStream.writeTo(outputStream);
////		}
////
////		ByteArrayOutputStream byteArrayOutputStream1 = documentOutput1.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST3)) {
////			byteArrayOutputStream1.writeTo(outputStream);
////		}
////
////		ByteArrayOutputStream byteArrayOutputStream2 = documentOutput2.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST4)) {
////			byteArrayOutputStream2.writeTo(outputStream);
////		}
////		ByteArrayOutputStream byteArrayOutputStream3 = documentOutput3.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST5)) {
////			byteArrayOutputStream3.writeTo(outputStream);
////		}
////		ByteArrayOutputStream byteArrayOutputStream4 = documentOutput4.getContent();
////		try (OutputStream outputStream = new FileOutputStream(DEST6)) {
////			byteArrayOutputStream4.writeTo(outputStream);
////		}
//		//}
//
////        PdfDocument pdfDoc = new PdfDocument(reader, writer);
////        Map<String, PdfFormField> fields = form.getFormFields();
//		// fields.get("unit1").setValue("100");
//		// fields.get("unit2").setValue("10");
//		// fields.get("unitTotal").setValue("300");
//		// fields.get("final").setValue("2300");
//		// fields.get("finalTotal").setValue("23");
//
////        fields.get("TERM 1_2").setValue("124");
//		// fields.get("name").setValue("Swati Singhi");
//		// fields.get("class").setValue("XII (Science)");
//		// fields.get("rollnum").setValue("21");
//		// fields.get("session").setValue("2018-19");
//		//
//		// fields.get("Term IEnglish").setValue("100");
//		// fields.get("Term IIEnglish").setValue("10");
//		// fields.get("Term IHindi").setValue("300");
//		// fields.get("Term IMaths").setValue("2300");
//		// fields.get("Term IScience").setValue("23");
////        form.flattenFields();
////        pdfDoc.close();
////    }
////    protected void waterMark(String dest) throws Exception {
////        PdfDocument pdfDoc = new PdfDocument(new PdfWriter(dest));
////        Document doc = new Document(pdfDoc);
////        doc.add(new Paragraph("Ye wlasxsa"));
////
////        int n = pdfDoc.getNumberOfPages();
////        PdfFont font = PdfFontFactory.createFont(StandardFonts.HELVETICA);
////        Paragraph p = new Paragraph("My watermark (text)").setFont(font).setFontSize(30);
////        // image watermark
////        ImageData img = ImageDataFactory.create(IMAGE_LOGO);
////        // Implement transformation matrix usage in order to scale image
////        float w = img.getWidth();
////        float h = img.getHeight();
////        // transparency
////        PdfExtGState gs1 = new PdfExtGState();
////        gs1.setFillOpacity(0.5f);
////        // properties
////        PdfCanvas over;
////        Rectangle pagesize;
////        float x, y;
////        // loop over every page
////        for (int i = 1; i <= n; i++) {
////            PdfPage pdfPage = pdfDoc.getPage(i);
////            pagesize = pdfPage.getPageSizeWithRotation();
////            pdfPage.setIgnorePageRotationForContent(true);
////
////            x = (pagesize.getLeft() + pagesize.getRight()) / 2;
////            y = (pagesize.getTop() + pagesize.getBottom()) / 2;
////            over = new PdfCanvas(pdfDoc.getPage(i));
////            over.saveState();
////            over.setExtGState(gs1);
////            if (i % 2 == 1) {
////                doc.showTextAligned(p, x, y, i, TextAlignment.CENTER, VerticalAlignment.TOP, 0);
////            } else {
//////				over.addImage(img, w, 0, 0, h, x - (w / 2), y - (h / 2), false);
////                over.addImageAt(img, w, 0, false);
////            }
////            over.restoreState();
////        }
////        doc.close();
////    }
////
////    public void createMain(String dest) throws IOException {
////        float margin = 5f;
////        PdfDocument pdf = new PdfDocument(new PdfWriter(dest));
////        PageSize pageSize = PageSize.A4.rotate();
////        Document document = new Document(pdf, pageSize);
////        document.setMargins(10, 5, 10, 5);
////        float mainColumnWidth = pageSize.getWidth() / 2 - margin;
////
////        Image img = new Image(ImageDataFactory.create(IMAGE_LOGO));
////        img.scaleToFit(60, 60);
////        float offsetX = (100 - img.getImageScaledWidth()) / 2;
////        float offsetY = (100 - img.getImageScaledHeight()) / 2;
////        img.setFixedPosition(20, pageSize.getHeight() - 70);
////        document.add(img);
////
////        img.setFixedPosition(mainColumnWidth + 2 * margin + 20, pageSize.getHeight() - 70);
////        document.add(img);
////
////        Table table = new Table(4);
////        addRowInTable(table, mainColumnWidth, margin, "BalaJi Convent School");
////        addRowInTable(table, mainColumnWidth, margin, "(Co-Educational English Medium School)");
////        addRowInTable(table, mainColumnWidth, margin, "NH-11, Ratangarh-Churu-(Raj)");
////        addRowInTable(table, mainColumnWidth, margin, "");
////        addRowInTable(table, mainColumnWidth, margin, "");
////        addRowInTable(table, mainColumnWidth, margin, "Student Copy", "Office Copy");
////        addRowInTable(table, mainColumnWidth, margin, "");
////        addRowInTable(table, mainColumnWidth, margin, "");
////        document.add(table);
////
////        table = new Table(8);
////        table.addCell(new Cell().setWidth(mainColumnWidth / 3).setFont(bold).setBorderLeft(Border.NO_BORDER)
////                .setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph("").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(mainColumnWidth / 3).setFont(bold).setBorderLeft(Border.NO_BORDER)
////                .setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph("School Fee Receipt").setTextAlignment(TextAlignment.CENTER)));
////        table.addCell(new Cell().setWidth(mainColumnWidth / 3).setFont(bold).setBorderLeft(Border.NO_BORDER)
////                .setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph("2018-2019").setTextAlignment(TextAlignment.RIGHT)));
////
////        addPartition(table, margin);
////
////        table.addCell(new Cell().setWidth(mainColumnWidth / 3).setFont(bold).setBorderLeft(Border.NO_BORDER)
////                .setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph("").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(mainColumnWidth / 3).setFont(bold).setBorderLeft(Border.NO_BORDER)
////                .setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph("School Fee Receipt").setTextAlignment(TextAlignment.CENTER)));
////        table.addCell(new Cell().setWidth(mainColumnWidth / 3).setFont(bold).setBorderLeft(Border.NO_BORDER)
////                .setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph("2018-2019").setTextAlignment(TextAlignment.RIGHT)));
////        document.add(table);
////
////        int fontSize = 9;
////        table = new Table(8);
////
////        addStudentInfoRow(table, mainColumnWidth, margin, fontSize, "Receipt # 1", "Class I", "Date: 13-01-2019");
////        addStudentInfoRow(table, mainColumnWidth, margin, fontSize, "Reg No. 13", "", "");
////        addStudentInfoRow(table, mainColumnWidth, margin, fontSize, "Student Name : Veerendra Singh", "", "");
////        addStudentInfoRow(table, mainColumnWidth, margin, fontSize, "Father Name : Chandan Singh", "", "");
////        addStudentInfoRow(table, mainColumnWidth, margin, fontSize, "Mother Name : Vimlesh Devi", "", "");
////        document.add(table);
////
////        // Fees content
////
////        table = new Table(4);
////        addFeeName(table, mainColumnWidth, margin, fontSize, "Fees Name: Quarter 1");
////        document.add(table);
////        // Fee head details
////
////        table = new Table(10);
////        addFeeHeadRowHeader(table, mainColumnWidth, margin, fontSize);
////        addFeeHeadRow(table, mainColumnWidth, margin, fontSize, "1", "Tution Fee", "5000.0", "0.0");
////        addFeeHeadRow(table, mainColumnWidth, margin, fontSize, "2", "Transport Fee", "3900.0", "1000.0");
////        document.add(table);
////
////        table = new Table(4);
////        addFeeName(table, mainColumnWidth, margin, fontSize, "Fees Name: Quarter 2");
////        document.add(table);
////
////        // Fee head details
////
////        table = new Table(10);
////        addFeeHeadRow(table, mainColumnWidth, margin, fontSize, "1", "Tution Fee", "5000.0", "0.0");
////        addFeeHeadRow(table, mainColumnWidth, margin, fontSize, "2", "Transport Fee", "3900.0", "1000.0");
////        document.add(table);
////
////        table = new Table(4);
////        addFeeName(table, mainColumnWidth, margin, fontSize, "Fees Name: Quarter 3");
////        document.add(table);
////
////        // Fee head details
////
////        table = new Table(10);
////        addFeeHeadRow(table, mainColumnWidth, margin, fontSize, "1", "Tution Fee", "5000.0", "0.0");
////        addFeeHeadRow(table, mainColumnWidth, margin, fontSize, "2", "Transport Fee", "3900.0", "1000.0");
////        document.add(table);
////
////        table = new Table(6);
////        addTotalRow(table, mainColumnWidth, margin, fontSize, "Total Fees (INR):", "28000.0");
////        addTotalRow(table, mainColumnWidth, margin, fontSize, "Discount Amount (INR):", "0.0");
////        addTotalRow(table, mainColumnWidth, margin, fontSize, "Prev Balance (INR):", "0.0");
////        addTotalRow(table, mainColumnWidth, margin, fontSize, "Paid Amount (INR):", "27000.0");
////        addTotalRow(table, mainColumnWidth, margin, fontSize, "Due Amount (INR):", "1000.0");
////        document.add(table);
////
////        table = new Table(4);
////        addAmountInWords(table, mainColumnWidth, margin, fontSize, "(In Words): Twenty Seven Thousand Rupees Only.");
////        document.add(table);
////
////        table = new Table(4);
////        addSignature(table, mainColumnWidth, margin, fontSize);
////        document.add(table);
////
////        document.close();
////    }
////
////    private void addStudentInfoRowContent(Table table, float width, int fontSize, String val1, String val2,
////                                          String val3) {
////        table.addCell(new Cell().setWidth(width / 3).setFont(bold).setFontSize(fontSize)
////                .setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph(val1).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width / 3).setFont(bold).setFontSize(fontSize)
////                .setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER)
////                .setBorderTop(Border.NO_BORDER).add(new Paragraph(val2).setTextAlignment(TextAlignment.CENTER)));
////        table.addCell(new Cell().setWidth(width / 3).setFont(bold).setFontSize(fontSize)
////                .setBorderBottom(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER).setBorderTop(Border.NO_BORDER)
////                .add(new Paragraph(val3).setTextAlignment(TextAlignment.RIGHT)));
////    }
////
////    private void addStudentInfoRow(Table table, float width, float margin, int fontSize, String val1, String val2,
////                                   String val3) {
////        addStudentInfoRowContent(table, width, fontSize, val1, val2, val3);
////        addPartition(table, margin);
////        addStudentInfoRowContent(table, width, fontSize, val1, val2, val3);
////    }
////
////    private void addAmountInWords(Table table, float width, float margin, int fontSize, String amountInWords) {
////        float height = 35f;
////        table.addCell(new Cell().setWidth(width).setHeight(height).setFont(bold).setFontSize(fontSize)
////                .setBorderBottom(Border.NO_BORDER)
////                .add(new Paragraph(amountInWords).setTextAlignment(TextAlignment.LEFT)));
////
////        addPartition(table, margin);
////
////        table.addCell(new Cell().setWidth(width).setHeight(height).setFont(bold).setFontSize(fontSize)
////                .setBorderBottom(Border.NO_BORDER)
////                .add(new Paragraph(amountInWords).setTextAlignment(TextAlignment.LEFT)));
////    }
////
////    private void addSignature(Table table, float width, float margin, int fontSize) {
////        float height = 15f;
////        table.addCell(new Cell().setWidth(width).setHeight(height).setFont(bold).setFontSize(fontSize)
////                .setBorderTop(Border.NO_BORDER).add(new Paragraph("Signature").setTextAlignment(TextAlignment.RIGHT)));
////
////        addPartition(table, margin);
////
////        table.addCell(new Cell().setWidth(width).setHeight(height).setFont(bold).setFontSize(fontSize)
////                .setBorderTop(Border.NO_BORDER).add(new Paragraph("Signature").setTextAlignment(TextAlignment.RIGHT)));
////    }
////
////    private void addFeeName(Table table, float width, float margin, int fontSize, String feeName) {
////        table.addCell(new Cell().setWidth(width).setFont(bold).setFontSize(fontSize).setBorderBottom(Border.NO_BORDER)
////                .add(new Paragraph(feeName).setTextAlignment(TextAlignment.LEFT)));
////
////        addPartition(table, margin);
////
////        table.addCell(new Cell().setWidth(width).setFont(bold).setFontSize(fontSize).setBorderBottom(Border.NO_BORDER)
////                .add(new Paragraph(feeName).setTextAlignment(TextAlignment.LEFT)));
////    }
////
////    private void addFeeHeadRowHeader(Table table, float width, float margin, int fontSize) {
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[0]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("#").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[1]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("Particulars").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[2]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("Amount(INR)").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[3]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("Discount").setTextAlignment(TextAlignment.LEFT)));
////
////        addPartition(table, margin);
////
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[0]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("#").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[1]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("Particulars").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[2]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("Amount(INR)").setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[3]).setFont(bold).setFontSize(fontSize)
////                .add(new Paragraph("Discount").setTextAlignment(TextAlignment.LEFT)));
////
////    }
////
////    private void addFeeHeadRow(Table table, float width, float margin, int fontSize, String val1, String val2,
////                               String val3, String val4) {
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[0]).setFontSize(fontSize)
////                .add(new Paragraph(val1).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[1]).setFontSize(fontSize)
////                .add(new Paragraph(val2).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[2]).setFontSize(fontSize)
////                .add(new Paragraph(val3).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[3]).setFontSize(fontSize)
////                .add(new Paragraph(val4).setTextAlignment(TextAlignment.LEFT)));
////
////        addPartition(table, margin);
////
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[0]).setFontSize(fontSize)
////                .add(new Paragraph(val1).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[1]).setFontSize(fontSize)
////                .add(new Paragraph(val2).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[2]).setFontSize(fontSize)
////                .add(new Paragraph(val3).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width * FEE_HEAD_HEADER_WIDTH[3]).setFontSize(fontSize)
////                .add(new Paragraph(val4).setTextAlignment(TextAlignment.LEFT)));
////
////    }
////
////    private void addPartition(Table table, float margin) {
////        table.addCell(new Cell().setWidth(margin).setFont(bold).setBorder(Border.NO_BORDER)
////                .add(new Paragraph("").setTextAlignment(TextAlignment.CENTER)));
////        table.addCell(new Cell().setWidth(margin).setFont(bold).setBorder(Border.NO_BORDER)
////                .setBorderLeft(new RoundDotsBorder(1)).add(new Paragraph("").setTextAlignment(TextAlignment.CENTER)));
////    }
////
////    private void addRowInTable(Table table, float width, float margin, String content) {
////        addRowInTable(table, width, margin, content, content);
////    }
////
////    private void addRowInTable(Table table, float width, float margin, String contentLeft, String contentRight) {
////        addRowInTableContent(table, width, contentLeft);
////        addPartition(table, margin);
////        addRowInTableContent(table, width, contentRight);
////    }
////
////    private void addRowInTableContent(Table table, float width, String content) {
////        table.addCell(new Cell().setWidth(width).setFont(bold).setBorder(Border.NO_BORDER)
////                .add(new Paragraph(content).setTextAlignment(TextAlignment.CENTER)));
////    }
////
////    private void addTotalRowContent(Table table, float width, int fontSize, String key, String value) {
////        table.addCell(new Cell().setWidth(width / 2).setFont(bold).setFontSize(fontSize).setBorderTop(Border.NO_BORDER)
////                .setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER)
////                .add(new Paragraph(key).setTextAlignment(TextAlignment.LEFT)));
////        table.addCell(new Cell().setWidth(width / 2).setFont(bold).setFontSize(fontSize).setBorderTop(Border.NO_BORDER)
////                .setBorderBottom(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER)
////                .add(new Paragraph(value).setTextAlignment(TextAlignment.RIGHT)));
////
////    }
////
////    private void addTotalRow(Table table, float width, float margin, int fontSize, String key, String value) {
////        addTotalRowContent(table, width, fontSize, key, value);
////        addPartition(table, margin);
////        addTotalRowContent(table, width, fontSize, key, value);
////    }
////
////    public void createPdf(String dest) throws IOException {
////        PdfDocument pdf = new PdfDocument(new PdfWriter(dest));
////        Document document = new Document(pdf, PageSize.A4).setTextAlignment(TextAlignment.JUSTIFIED);
////        Rectangle[] columns = {new Rectangle(36, 36, 254, 770), new Rectangle(305, 36, 254, 770)};
////        document.setRenderer(new ColumnDocumentRenderer(document, columns));
////        // BufferedReader br = new BufferedReader(new FileReader("This is the
////        // text"));
////        List<String> lines = new ArrayList<>();
////        lines.add("Fiest line");
////        lines.add("This is the text");
////        // String line;
////        PdfFont normal = PdfFontFactory.createFont(StandardFonts.TIMES_ROMAN);
////        PdfFont bold = PdfFontFactory.createFont(StandardFonts.TIMES_BOLD);
////        boolean title = true;
////        for (String line : lines) {
////            document.add(new Paragraph(line).setFont(title ? bold : normal));
////            title = line.isEmpty();
////        }
////        document.close();
//
//		//
//		// Document document2 = new Document(PageSize.A4);
//		// PdfWriter writer = PdfWriter.getInstance(document, new
//		// FileOutputStream(outPutDirectory + indexID + ".pdf"));
//		// document.open();
//		// // Add some content in portrait
//		// document.setPageSize(PageSize.A4.rotate());
//		// document.newPage();
//		// // Add some content in landscape
//		// document.close();
////    }
////
////    public void createPdfCenter(String dest) throws IOException {
////        PdfDocument pdf = new PdfDocument(new PdfWriter(dest));
////        PageSize pagesize = PageSize.A4.rotate();
////        Document document = new Document(pdf, pagesize);
////        Rectangle[] columns = {new Rectangle(0, 36, 25, 70), new Rectangle(100, 36, 25, 70)};
////        document.setRenderer(new ColumnDocumentRenderer(document, columns));
////        // float w = pagesize.getWidth() - document.getLeftMargin() -
////        // document.getRightMargin();
////
////        Paragraph p = new Paragraph();
////        p.add("Text in the middle");
////        // p.setHorizontalAlignment(HorizontalAlignment.RIGHT);
////        p.setTextAlignment(TextAlignment.CENTER);
////        document.add(p);
////        document.close();
////    }
////
////    public void manipulatePdf(String dest) throws Exception {
////        PdfDocument pdfDoc = new PdfDocument(new PdfWriter(dest));
////        PageSize pagesize = PageSize.A4.rotate();
////        Document doc = new Document(pdfDoc, pagesize);
////
////        doc.add(new Paragraph("With 3 columns:"));
////        Table table = new Table(new float[]{1, 1, 8});
////        // table.setWidthPercent(100);
////        table.setMarginTop(5);
////        table.addCell("Col a");
////        table.addCell("Col b");
////        table.addCell("Col c");
////        table.addCell("Value a");
////        table.addCell("Value b");
////        table.addCell("This is a long description for column c. "
////                + "It needs much more space hence we made sure that the third column is wider.");
////        doc.add(table);
////        doc.add(new Paragraph("With 2 columns:"));
////        table = new Table(2);
////        table.setMarginTop(5);
////        table.addCell("Col a");
////        table.addCell("Col b");
////        table.addCell("Value a");
////        table.addCell("Value b");
////        table.addCell(new Cell(1, 2).add(new Paragraph("Value b")));
////        table.addCell(new Cell(1, 2).add(new Paragraph("This is a long description for column c. "
////                + "It needs much more space hence we made sure that the third column is wider.")));
////        table.addCell("Col a");
////        table.addCell("Col b");
////        table.addCell("Value a");
////        table.addCell("Value b");
////        table.addCell(new Cell(1, 2).add(new Paragraph("Value b")));
////        table.addCell(new Cell(1, 2).add(new Paragraph("This is a long description for column c. "
////                + "It needs much more space hence we made sure that the third column is wider.")));
////        doc.add(table);
////
////        doc.close();
////    }
////
////    public void createPdfUser(String dest) throws IOException {
////        UserObject rohit = new UserObject();
////        rohit.setName("Rohit");
////        rohit.setId("6633429");
////        rohit.setReputation(1);
////        rohit.setJobtitle("Copy/paste artist");
////
////        UserObject bruno = new UserObject();
////        bruno.setName("Bruno Lowagie");
////        bruno.setId("1622493");
////        bruno.setReputation(42690);
////        bruno.setJobtitle("Java Rockstar");
////
////        PdfDocument pdf = new PdfDocument(new PdfWriter(dest));
////        Document document = new Document(pdf);
////        document.add(createTable(rohit, bruno));
////        document.close();
////    }
////
////    public Table createTable(UserObject user1, UserObject user2) {
////        if (user1 == null)
////            user1 = new UserObject();
////        if (user2 == null)
////            user2 = new UserObject();
////        Table table = new Table(3);
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(bold).add(new Paragraph("Name:")));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular).add(new Paragraph(user1.getName())));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular).add(new Paragraph(user2.getName())));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(bold).add(new Paragraph("Id:")));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular).add(new Paragraph(user1.getId())));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular).add(new Paragraph(user2.getId())));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(bold).add(new Paragraph("Reputation:")));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular)
////                .add(new Paragraph(String.valueOf(user1.getReputation()))));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular)
////                .add(new Paragraph(String.valueOf(user2.getReputation()))));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(bold).add(new Paragraph("Job title:")));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular).add(new Paragraph(user1.getJobtitle())));
////        table.addCell(new Cell().setBorder(Border.NO_BORDER).setFont(regular).add(new Paragraph(user2.getJobtitle())));
////        return table;
////    }
////
////    class UserObject {
////
////        protected String name = "";
////        protected String id = "";
////        protected int reputation = 0;
////        protected String jobtitle = "";
////
////        public String getName() {
////            return name;
////        }
////
////        public void setName(String name) {
////            this.name = name;
////        }
////
////        public String getId() {
////            return id;
////        }
////
////        public void setId(String id) {
////            this.id = id;
////        }
////
////        public int getReputation() {
////            return reputation;
////        }
////
////        public void setReputation(int reputation) {
////            this.reputation = reputation;
////        }
////
////        public String getJobtitle() {
////            return jobtitle;
////        }
////
////        public void setJobtitle(String jobtitle) {
////            this.jobtitle = jobtitle;
////        }
//<<<<<<< HEAD
//
//=======
//
	}
}
//>>>>>>> e625ad59902566c92f486100a4548ea5c248ec24
