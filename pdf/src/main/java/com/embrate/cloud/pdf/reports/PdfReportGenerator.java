package com.embrate.cloud.pdf.reports;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.report.ReportUtils;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.lernen.cloud.core.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static com.lernen.cloud.core.utils.DateUtils.DEFAULT_TIMEZONE;

public class PdfReportGenerator extends PDFGenerator {
	private final AssetProvider assetProvider;

	// Track if we're dealing with a wide table for optimization
	private boolean isWideTable = false;

	public PdfReportGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
		this.assetProvider = assetProvider;
	}

	private static final Logger logger = LogManager.getLogger(PdfReportGenerator.class);

	private static final String PDF = ".pdf";
	public static final float DEFAULT_LOGO_WIDTH = 90f;
	public static final float DEFAULT_LOGO_HEIGHT = 90f;

	public DocumentOutput generateReport(ReportDetails reportDetails, Module module) throws IOException {

		PdfFont regularBoldFont = getRegularBoldFont();
		PdfFont regularFont = getRegularFont();

		if (reportDetails == null || CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList()) ||
				CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList().get(0).getReportCellDetails())) {
			DocumentOutput documentOutput = new DocumentOutput("Empty Report" + PDF, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
			generateHeading(document, documentLayoutSetup, regularBoldFont, "No Data");
			document.close();
			return documentOutput;
		}

		ReportSheetDetails firstReportSheetDetails = reportDetails.getReportSheetDetailsList().get(0);
		if (firstReportSheetDetails == null || firstReportSheetDetails.getReportCellDetails() == null
				|| CollectionUtils.isEmpty(firstReportSheetDetails.getReportCellDetails()) ||
				(firstReportSheetDetails.getReportCellDetails().size() > 2 &&
						CollectionUtils.isEmpty(firstReportSheetDetails.getReportCellDetails().get(1)))) {
			DocumentOutput documentOutput = new DocumentOutput("Empty Report" + PDF, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
			generateHeading(document, documentLayoutSetup, regularBoldFont, "No Data");
			document.close();
			return documentOutput;
		}

		DocumentOutput documentOutput = new DocumentOutput(reportDetails.getReportName() + PDF, new ByteArrayOutputStream());
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);

		if (firstReportSheetDetails.getPageSize() == null) {
			//taking 2nd row as 1st will be header row
			int totalColumns = firstReportSheetDetails.getTotalColumns() == null ?
					firstReportSheetDetails.getReportCellDetails().get(1).size() :
					firstReportSheetDetails.getTotalColumns();
			if (totalColumns > 10 && totalColumns <= 17) {
				documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
			} else if (totalColumns > 17) {
				documentLayoutSetup = initDocumentLayoutSetup(PageSize.A3.rotate());
			}
		} else {
			documentLayoutSetup = initDocumentLayoutSetup(firstReportSheetDetails.getPageSize());
		}

		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		boolean showDateAndTime = firstReportSheetDetails.isShowDateAndTime();

		boolean isShowCenterHeading = firstReportSheetDetails.isShowCenterHeading();
		generateReportInstituteHeader(document, documentLayoutSetup, regularBoldFont, firstReportSheetDetails);
		generateReportHeader(document, documentLayoutSetup, regularBoldFont, reportDetails, regularFont, showDateAndTime, isShowCenterHeading);

		if(!newFlow(module)) {
			generateReportDetails(document, documentLayoutSetup, regularBoldFont, regularFont, reportDetails, module);
		} else {
			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
			generateReportDetails(document, htmlReportTable, documentLayoutSetup);
		}

		document.close();

		return documentOutput;
	}

	public void generateReportDetails(Document document, HTMLReportTable tableData) {
		System.out.println("=== Starting generateReportDetails ===");

		// Extract table properties
		List<List<HTMLReportTableCol>> headerRows = tableData.getHeaderRows();
		List<List<HTMLReportTableCol>> dataRows = tableData.getRows();

		System.out.println("Raw data check:");
		System.out.println("- headerRows is null: " + (headerRows == null));
		System.out.println("- dataRows is null: " + (dataRows == null));
		if (headerRows != null) {
			System.out.println("- headerRows size: " + headerRows.size());
		}
		if (dataRows != null) {
			System.out.println("- dataRows size: " + dataRows.size());
		}

		// Get document layout setup to calculate proper content width
		DocumentLayoutSetup documentLayoutSetup = getCurrentDocumentLayoutSetup(document);
		float contentWidth = getContentWidth(documentLayoutSetup);

		// Determine the number of columns from the data structure
		int maxColumns = getMaxColumns(headerRows, dataRows);

		// Debug logging
		System.out.println("HTMLReportTable - Header rows: " + (headerRows != null ? headerRows.size() : 0) +
				", Data rows: " + (dataRows != null ? dataRows.size() : 0) +
				", Max columns: " + maxColumns);

		if (maxColumns == 0) {
			System.out.println("No columns found in report data - skipping table generation");
			return;
		}

		// Try the improved approach first, fallback to percentage if needed
		Table table;
		try {
			System.out.println("Attempting to create table with improved approach...");
			// Calculate column widths - use a simpler approach first
			float[] columnWidths = calculateColumnWidthsSimple(headerRows, dataRows, contentWidth, maxColumns);

			// Create the table with the calculated column widths using absolute widths
			table = new Table(columnWidths)
					.setWidth(contentWidth)
					.setFixedLayout();
			System.out.println("Table created successfully with fixed layout");
		} catch (Exception e) {
			System.out.println("Failed to create table with improved approach, using fallback: " + e.getMessage());
			// Fallback to percentage-based approach
			float[] columnWidthsPercent = new float[maxColumns];
			for (int i = 0; i < maxColumns; i++) {
				columnWidthsPercent[i] = 100.0f / maxColumns;
			}
			table = new Table(UnitValue.createPercentArray(columnWidthsPercent))
					.setWidth(UnitValue.createPercentValue(100));
			System.out.println("Fallback table created with percentage layout");
		}

		// Ensure table fits within the document
		table.setHorizontalAlignment(HorizontalAlignment.CENTER);

		// Add headers to the table
		if (headerRows != null && !headerRows.isEmpty()) {
			System.out.println("Adding " + headerRows.size() + " header rows to table");
			for (int rowIndex = 0; rowIndex < headerRows.size(); rowIndex++) {
				List<HTMLReportTableCol> headerRow = headerRows.get(rowIndex);
				System.out.println("Header row " + rowIndex + ": " + headerRow.size() + " columns");
				for (HTMLReportTableCol headerCol : headerRow) {
					table.addCell(createCell(headerCol));
				}
			}
		}

		// Add data rows to the table
		if (dataRows != null && !dataRows.isEmpty()) {
			System.out.println("Adding " + dataRows.size() + " data rows to table");
			for (int rowIndex = 0; rowIndex < Math.min(dataRows.size(), 3); rowIndex++) { // Log first 3 rows only
				List<HTMLReportTableCol> dataRow = dataRows.get(rowIndex);
				System.out.println("Data row " + rowIndex + ": " + dataRow.size() + " columns");
			}
			for (List<HTMLReportTableCol> dataRow : dataRows) {
				for (HTMLReportTableCol dataCol : dataRow) {
					table.addCell(createCell(dataCol));
				}
			}
		}

		// Add the completed table to the document
		System.out.println("Adding table to document...");
		document.add(table);
		System.out.println("=== Finished generateReportDetails ===");
	}

	// Overloaded method that accepts DocumentLayoutSetup for better control
	public void generateReportDetails(Document document, HTMLReportTable tableData, DocumentLayoutSetup documentLayoutSetup) {
		System.out.println("=== Starting generateReportDetails (with DocumentLayoutSetup) ===");

		// Extract table properties
		List<List<HTMLReportTableCol>> headerRows = tableData.getHeaderRows();
		List<List<HTMLReportTableCol>> dataRows = tableData.getRows();

		System.out.println("Raw data check (overloaded method):");
		System.out.println("- headerRows is null: " + (headerRows == null));
		System.out.println("- dataRows is null: " + (dataRows == null));
		if (headerRows != null) {
			System.out.println("- headerRows size: " + headerRows.size());
		}
		if (dataRows != null) {
			System.out.println("- dataRows size: " + dataRows.size());
		}

		// Calculate content width from provided layout setup
		float contentWidth = getContentWidth(documentLayoutSetup);

		// Determine the number of columns from the data structure
		int maxColumns = getMaxColumns(headerRows, dataRows);
		int logicalColumns = getLogicalColumnCount(headerRows, dataRows);

		System.out.println("HTMLReportTable (overloaded) - Header rows: " + (headerRows != null ? headerRows.size() : 0) +
				", Data rows: " + (dataRows != null ? dataRows.size() : 0) +
				", Max physical columns: " + maxColumns + ", Logical columns: " + logicalColumns);

		// Use logical columns for table structure
		int tableColumns = logicalColumns;

		if (tableColumns == 0) {
			System.out.println("No columns found in report data - skipping table generation (overloaded method)");
			return;
		}

		// Check if we need more space for wide tables
		if (tableColumns > 15) {
			System.out.println("Wide table detected (" + tableColumns + " columns). Current content width: " + contentWidth);
			// For very wide tables, we might need to recalculate with landscape orientation
			// But for now, let's proceed with the current layout
		}

		// Try the improved approach first, fallback to percentage if needed
		Table table;
		// For very wide tables, use a more sophisticated approach
		if (tableColumns > 20) {
			this.isWideTable = true;
			System.out.println("Using optimized layout for very wide table (" + tableColumns + " columns)");

			// Check if we should automatically switch to landscape orientation
			PageSize currentPageSize = documentLayoutSetup.getPageSize();
			boolean isLandscape = currentPageSize.getWidth() > currentPageSize.getHeight();

			if (!isLandscape && tableColumns > 15) {
				System.out.println("Wide table detected - optimizing for current page size");
				// Since we can't change page orientation after document creation,
				// we'll optimize the table to fit better in portrait mode
				System.out.println("Recommendation: Use landscape orientation for better results");
			}

			// Use absolute widths for better control
			float[] absoluteWidths = calculateAbsoluteColumnWidths(headerRows, dataRows, tableColumns, contentWidth);

			table = new Table(absoluteWidths)
					.setWidth(contentWidth) // Use exact content width
					.setFixedLayout() // Force fixed layout for precise control
					.setMarginLeft(0f)
					.setMarginRight(0f)
					.setPadding(0f); // Remove all padding to maximize space

			System.out.println("Table created with optimized layout (overloaded method) - " + tableColumns + " columns");
		} else {
			try {
				System.out.println("Attempting to create table with improved approach (overloaded method)...");
				// Calculate column widths - use the logical column count
				float[] columnWidths = calculateColumnWidthsSimple(headerRows, dataRows, contentWidth, tableColumns);

				// Create the table with the calculated column widths using absolute widths
				table = new Table(columnWidths)
						.setWidth(contentWidth)
						.setFixedLayout();
				System.out.println("Table created successfully with fixed layout (overloaded method) - " + tableColumns + " columns");
			} catch (Exception e) {
				System.out.println("Failed to create table with improved approach (overloaded method), using fallback: " + e.getMessage());
				// Fallback to percentage-based approach
				float[] columnWidthsPercent = new float[tableColumns];
				for (int i = 0; i < tableColumns; i++) {
					columnWidthsPercent[i] = 100.0f / tableColumns;
				}
				table = new Table(UnitValue.createPercentArray(columnWidthsPercent))
						.setWidth(UnitValue.createPercentValue(100));
				System.out.println("Fallback table created with percentage layout (overloaded method) - " + tableColumns + " columns");
			}
		}

		// Ensure table fits within the document
		table.setHorizontalAlignment(HorizontalAlignment.CENTER);

		// Add headers to the table
		if (headerRows != null && !headerRows.isEmpty()) {
			System.out.println("Adding " + headerRows.size() + " header rows to table (overloaded method)");
			for (int rowIndex = 0; rowIndex < headerRows.size(); rowIndex++) {
				List<HTMLReportTableCol> headerRow = headerRows.get(rowIndex);
				System.out.println("Header row " + rowIndex + ": " + headerRow.size() + " columns (overloaded method)");

				// Debug first row column spans
				if (rowIndex == 0) {
					for (int colIndex = 0; colIndex < headerRow.size(); colIndex++) {
						HTMLReportTableCol col = headerRow.get(colIndex);
						System.out.println("  Header row 0, col " + colIndex + ": value='" + col.getValue() +
								"', colSpan=" + col.getColSpan() + ", rowSpan=" + col.getRowSpan());
					}
				}

				for (HTMLReportTableCol headerCol : headerRow) {
					table.addCell(createCell(headerCol));
				}
			}
		} else {
			System.out.println("No header rows to add (overloaded method)");
		}

		// Add data rows to the table
		if (dataRows != null && !dataRows.isEmpty()) {
			System.out.println("Adding " + dataRows.size() + " data rows to table (overloaded method)");
			for (int rowIndex = 0; rowIndex < Math.min(dataRows.size(), 3); rowIndex++) { // Log first 3 rows only
				List<HTMLReportTableCol> dataRow = dataRows.get(rowIndex);
				System.out.println("Data row " + rowIndex + ": " + dataRow.size() + " columns (overloaded method)");
			}
			for (List<HTMLReportTableCol> dataRow : dataRows) {
				for (HTMLReportTableCol dataCol : dataRow) {
					table.addCell(createCell(dataCol));
				}
			}
		} else {
			System.out.println("No data rows to add (overloaded method)");
		}

		// Add the completed table to the document
		System.out.println("Adding table to document (overloaded method)...");
		document.add(table);
		System.out.println("=== Finished generateReportDetails (overloaded method) ===");
	}

	private float[] calculateColumnWidthsImproved(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows, float contentWidth) {
		// Step 1: Determine the maximum number of columns
		int maxColumns = getMaxColumns(headerRows, dataRows);

		if (maxColumns == 0) {
			return new float[0];
		}

		// Step 2: Calculate estimated text widths considering font size
		double[] columnMaxWidths = new double[maxColumns];

		// Process header rows
		processRowsForWidth(headerRows, columnMaxWidths, true);

		// Process data rows
		processRowsForWidth(dataRows, columnMaxWidths, false);

		// Step 3: Apply minimum width constraints and normalize
		return normalizeColumnWidths(columnMaxWidths, contentWidth, maxColumns);
	}

	private int getMaxColumns(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows) {
		int maxColumns = 0;

		// Check header rows - count actual physical columns (not column spans)
		if (headerRows != null) {
			for (List<HTMLReportTableCol> headerRow : headerRows) {
				maxColumns = Math.max(maxColumns, headerRow.size());
			}
		}

		// Check data rows - count actual physical columns (not column spans)
		if (dataRows != null) {
			for (List<HTMLReportTableCol> dataRow : dataRows) {
				maxColumns = Math.max(maxColumns, dataRow.size());
			}
		}

		System.out.println("getMaxColumns result: " + maxColumns);
		return maxColumns;
	}

	// New method to get the logical column count (considering column spans)
	private int getLogicalColumnCount(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows) {
		int maxLogicalColumns = 0;

		// Check header rows - count logical columns (including column spans)
		if (headerRows != null) {
			for (List<HTMLReportTableCol> headerRow : headerRows) {
				int logicalColumns = 0;
				for (HTMLReportTableCol col : headerRow) {
					logicalColumns += (col.getColSpan() == null ? 1 : col.getColSpan());
				}
				maxLogicalColumns = Math.max(maxLogicalColumns, logicalColumns);
			}
		}

		// Check data rows - count logical columns (including column spans)
		if (dataRows != null) {
			for (List<HTMLReportTableCol> dataRow : dataRows) {
				int logicalColumns = 0;
				for (HTMLReportTableCol col : dataRow) {
					logicalColumns += (col.getColSpan() == null ? 1 : col.getColSpan());
				}
				maxLogicalColumns = Math.max(maxLogicalColumns, logicalColumns);
			}
		}

		System.out.println("getLogicalColumnCount result: " + maxLogicalColumns);
		return maxLogicalColumns;
	}

	private void processRowsForWidth(List<List<HTMLReportTableCol>> rows, double[] columnMaxWidths, boolean isHeader) {
		if (rows == null) return;

		for (List<HTMLReportTableCol> row : rows) {
			for (int colIndex = 0; colIndex < row.size() && colIndex < columnMaxWidths.length; colIndex++) {
				HTMLReportTableCol col = row.get(colIndex);

				// Calculate estimated text width considering font size
				double estimatedWidth = calculateEstimatedTextWidth(col, isHeader);

				// For cells with column span, we need to consider the total width they need
				// but we'll distribute it later in the normalization phase
				int colSpan = (col.getColSpan() == null ? 1 : col.getColSpan());

				if (colSpan == 1) {
					// Simple case: no column span
					columnMaxWidths[colIndex] = Math.max(columnMaxWidths[colIndex], estimatedWidth);
				} else {
					// For column spans, distribute the width across the spanned columns
					double widthPerColumn = estimatedWidth / colSpan;
					for (int i = 0; i < colSpan && (colIndex + i) < columnMaxWidths.length; i++) {
						columnMaxWidths[colIndex + i] = Math.max(columnMaxWidths[colIndex + i], widthPerColumn);
					}
				}
			}
		}
	}

	private double calculateEstimatedTextWidth(HTMLReportTableCol col, boolean isHeader) {
		if (col.getValue() == null || col.getValue().isEmpty()) {
			return 30.0; // Minimum width for empty cells
		}

		// Base character width estimation (approximate)
		double baseCharWidth = 6.5; // Average character width in points (slightly increased)

		// Adjust for font size
		int fontSize = col.getSize() > 0 ? col.getSize() : (isHeader ? 10 : 9);
		double fontMultiplier = fontSize / 9.0; // Base on 9pt font

		// Adjust for bold text (slightly wider)
		double boldMultiplier = col.isBold() ? 1.15 : 1.0;

		// Calculate estimated width with some buffer for wider characters
		String value = col.getValue();
		double textWidth = value.length() * baseCharWidth * fontMultiplier * boldMultiplier;

		// Add extra width for numbers and special characters that tend to be wider
		if (value.matches(".*[0-9%$€£¥₹].*")) {
			textWidth *= 1.1;
		}

		// Add padding (cell padding + border)
		double padding = 12.0; // Left + right padding + border space

		return Math.max(textWidth + padding, 30.0); // Ensure minimum width
	}

	private float[] normalizeColumnWidths(double[] columnMaxWidths, float contentWidth, int maxColumns) {
		// Calculate total estimated width
		double totalEstimatedWidth = 0;
		for (double width : columnMaxWidths) {
			totalEstimatedWidth += width;
		}

		// Apply minimum width constraints based on number of columns
		double minColumnWidth;
		if (maxColumns <= 5) {
			minColumnWidth = Math.max(50.0, contentWidth * 0.10); // Larger minimum for fewer columns
		} else if (maxColumns <= 10) {
			minColumnWidth = Math.max(40.0, contentWidth * 0.08); // Medium minimum
		} else if (maxColumns <= 15) {
			minColumnWidth = Math.max(30.0, contentWidth * 0.05); // Smaller minimum for many columns
		} else {
			// For very wide tables (>15 columns), we need to be more aggressive
			minColumnWidth = Math.max(20.0, contentWidth * 0.025); // Very small minimum but still readable
			System.out.println("Warning: Very wide table with " + maxColumns + " columns. Using minimum width: " + minColumnWidth);
		}

		float[] normalizedWidths = new float[maxColumns];

		if (totalEstimatedWidth <= contentWidth * 0.95) { // Leave 5% buffer
			// If estimated width fits comfortably, use it with minimum constraints
			for (int i = 0; i < maxColumns; i++) {
				normalizedWidths[i] = (float) Math.max(columnMaxWidths[i], minColumnWidth);
			}

			// Redistribute any extra space proportionally
			float totalUsed = 0;
			for (float width : normalizedWidths) {
				totalUsed += width;
			}

			if (totalUsed < contentWidth) {
				float extraSpace = contentWidth - totalUsed;
				for (int i = 0; i < maxColumns; i++) {
					normalizedWidths[i] += extraSpace * (normalizedWidths[i] / totalUsed);
				}
			}
		} else {
			// If estimated width exceeds content width, scale down intelligently
			double availableWidth = contentWidth - (maxColumns * minColumnWidth);
			double excessWidth = totalEstimatedWidth - (maxColumns * minColumnWidth);

			if (availableWidth > 0 && excessWidth > 0) {
				double scaleFactor = availableWidth / excessWidth;
				scaleFactor = Math.max(0.2, scaleFactor); // Ensure minimum scale factor

				for (int i = 0; i < maxColumns; i++) {
					double scaledWidth = minColumnWidth + (columnMaxWidths[i] - minColumnWidth) * scaleFactor;
					normalizedWidths[i] = (float) Math.max(scaledWidth, minColumnWidth);
				}
			} else {
				// Fallback: distribute width equally
				float equalWidth = contentWidth / maxColumns;
				for (int i = 0; i < maxColumns; i++) {
					normalizedWidths[i] = Math.max(equalWidth, (float) minColumnWidth);
				}
			}
		}

		// Final adjustment to ensure total width equals content width
		float totalWidth = 0;
		for (float width : normalizedWidths) {
			totalWidth += width;
		}

		if (Math.abs(totalWidth - contentWidth) > 1.0f) { // Allow 1pt tolerance
			float adjustmentFactor = contentWidth / totalWidth;
			for (int i = 0; i < maxColumns; i++) {
				normalizedWidths[i] *= adjustmentFactor;
				// Ensure no column becomes too small after adjustment
				normalizedWidths[i] = Math.max(normalizedWidths[i], (float) (minColumnWidth * 0.8));
			}
		}

		return normalizedWidths;
	}

	private float[] calculateColumnWidthsSimple(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows, float contentWidth, int maxColumns) {
		System.out.println("=== calculateColumnWidthsSimple called ===");
		System.out.println("Calculating column widths - Content width: " + contentWidth + ", Max columns: " + maxColumns);
		System.out.println("Stack trace to see who called this method:");
		StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
		for (int i = 1; i <= Math.min(5, stackTrace.length - 1); i++) {
			System.out.println("  " + i + ": " + stackTrace[i].getClassName() + "." + stackTrace[i].getMethodName() + ":" + stackTrace[i].getLineNumber());
		}

		// Start with equal distribution
		float[] columnWidths = new float[maxColumns];
		float equalWidth = contentWidth / maxColumns;

		// Initialize with equal widths
		for (int i = 0; i < maxColumns; i++) {
			columnWidths[i] = equalWidth;
		}

		// Calculate text-based width requirements
		double[] textWidthRequirements = new double[maxColumns];

		// Process header rows
		if (headerRows != null) {
			for (List<HTMLReportTableCol> row : headerRows) {
				for (int i = 0; i < row.size() && i < maxColumns; i++) {
					HTMLReportTableCol col = row.get(i);
					double textWidth = calculateEstimatedTextWidth(col, true);
					textWidthRequirements[i] = Math.max(textWidthRequirements[i], textWidth);
				}
			}
		}

		// Process data rows
		if (dataRows != null) {
			for (List<HTMLReportTableCol> row : dataRows) {
				for (int i = 0; i < row.size() && i < maxColumns; i++) {
					HTMLReportTableCol col = row.get(i);
					double textWidth = calculateEstimatedTextWidth(col, false);
					textWidthRequirements[i] = Math.max(textWidthRequirements[i], textWidth);
				}
			}
		}

		// Calculate total required width
		double totalRequired = 0;
		for (double width : textWidthRequirements) {
			totalRequired += width;
		}

		// If total required width fits within content width, use proportional distribution
		if (totalRequired > 0 && totalRequired <= contentWidth) {
			for (int i = 0; i < maxColumns; i++) {
				columnWidths[i] = (float) (textWidthRequirements[i] * (contentWidth / totalRequired));
			}
		} else if (totalRequired > contentWidth) {
			// Scale down proportionally but ensure minimum widths
			double minWidth = Math.max(25.0, contentWidth * 0.03); // Minimum 3% or 25 points
			double availableWidth = contentWidth - (maxColumns * minWidth);
			double excessWidth = totalRequired - (maxColumns * minWidth);

			if (availableWidth > 0 && excessWidth > 0) {
				double scaleFactor = availableWidth / excessWidth;
				for (int i = 0; i < maxColumns; i++) {
					double scaledWidth = minWidth + (textWidthRequirements[i] - minWidth) * scaleFactor;
					columnWidths[i] = (float) Math.max(scaledWidth, minWidth);
				}
			} else {
				// Fallback to equal distribution
				for (int i = 0; i < maxColumns; i++) {
					columnWidths[i] = equalWidth;
				}
			}
		}

		// Log final column widths
		StringBuilder widthsLog = new StringBuilder("Final column widths: [");
		for (int i = 0; i < columnWidths.length; i++) {
			if (i > 0) widthsLog.append(", ");
			widthsLog.append(String.format("%.2f", columnWidths[i]));
		}
		widthsLog.append("]");
		System.out.println(widthsLog.toString());

		return columnWidths;
	}

	private float[] calculateIntelligentColumnWidths(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows, int tableColumns) {
		System.out.println("Calculating intelligent column widths for " + tableColumns + " columns");

		float[] columnWidths = new float[tableColumns];

		// Analyze content to determine relative importance of columns
		double[] contentScores = new double[tableColumns];

		// Score based on header content length and importance
		if (headerRows != null) {
			for (List<HTMLReportTableCol> row : headerRows) {
				for (int i = 0; i < row.size() && i < tableColumns; i++) {
					HTMLReportTableCol col = row.get(i);
					if (col.getValue() != null) {
						// First column (usually labels) gets higher weight
						double weight = (i == 0) ? 2.0 : 1.0;
						contentScores[i] += col.getValue().length() * weight;
					}
				}
			}
		}

		// Score based on data content
		if (dataRows != null) {
			for (List<HTMLReportTableCol> row : dataRows) {
				for (int i = 0; i < row.size() && i < tableColumns; i++) {
					HTMLReportTableCol col = row.get(i);
					if (col.getValue() != null) {
						// First column (usually labels) gets higher weight
						double weight = (i == 0) ? 2.0 : 1.0;
						contentScores[i] += col.getValue().length() * weight;
					}
				}
			}
		}

		// Calculate total score
		double totalScore = 0;
		for (double score : contentScores) {
			totalScore += Math.max(score, 1.0); // Minimum score of 1 for empty columns
		}

		// Distribute widths based on content scores with constraints
		for (int i = 0; i < tableColumns; i++) {
			double normalizedScore = Math.max(contentScores[i], 1.0) / totalScore;

			// Apply constraints: minimum 2%, maximum 15% per column
			float minWidth = 2.0f;
			float maxWidth = 15.0f;

			// Special handling for first column (usually wider for labels)
			if (i == 0) {
				minWidth = 8.0f;
				maxWidth = 25.0f;
			}

			float calculatedWidth = (float) (normalizedScore * 100.0);
			columnWidths[i] = Math.max(minWidth, Math.min(maxWidth, calculatedWidth));
		}

		// Normalize to ensure total is 100%
		float totalWidth = 0;
		for (float width : columnWidths) {
			totalWidth += width;
		}

		for (int i = 0; i < tableColumns; i++) {
			columnWidths[i] = (columnWidths[i] / totalWidth) * 100.0f;
		}

		// Log the calculated widths
		System.out.println("Intelligent column widths calculated:");
		for (int i = 0; i < Math.min(5, tableColumns); i++) {
			System.out.println("  Column " + i + ": " + String.format("%.2f", columnWidths[i]) + "%");
		}
		if (tableColumns > 5) {
			System.out.println("  ... and " + (tableColumns - 5) + " more columns");
		}

		return columnWidths;
	}

	private float[] calculateAbsoluteColumnWidths(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows, int tableColumns, float contentWidth) {
		System.out.println("Calculating absolute column widths for " + tableColumns + " columns with content width: " + contentWidth);

		// For very wide tables, use a more aggressive approach
		float[] columnWidths = new float[tableColumns];

		// Calculate minimum width needed for each column based on content
		double[] minWidths = new double[tableColumns];

		// Analyze content to determine minimum required widths
		if (headerRows != null) {
			for (List<HTMLReportTableCol> row : headerRows) {
				for (int i = 0; i < row.size() && i < tableColumns; i++) {
					HTMLReportTableCol col = row.get(i);
					if (col.getValue() != null) {
						// Estimate minimum width needed (very conservative)
						double estimatedWidth = col.getValue().length() * 2.5; // 2.5 points per character for 5pt font
						minWidths[i] = Math.max(minWidths[i], estimatedWidth);
					}
				}
			}
		}

		// Check data rows too
		if (dataRows != null) {
			for (List<HTMLReportTableCol> row : dataRows) {
				for (int i = 0; i < row.size() && i < tableColumns; i++) {
					HTMLReportTableCol col = row.get(i);
					if (col.getValue() != null) {
						double estimatedWidth = col.getValue().length() * 2.5;
						minWidths[i] = Math.max(minWidths[i], estimatedWidth);
					}
				}
			}
		}

		// Set absolute minimum widths
		for (int i = 0; i < tableColumns; i++) {
			minWidths[i] = Math.max(minWidths[i], 15.0); // Absolute minimum 15 points
			if (i == 0) {
				minWidths[i] = Math.max(minWidths[i], 60.0); // First column needs more space
			}
		}

		// Calculate total minimum width needed
		double totalMinWidth = 0;
		for (double width : minWidths) {
			totalMinWidth += width;
		}

		System.out.println("Total minimum width needed: " + totalMinWidth + ", Available: " + contentWidth);

		if (totalMinWidth <= contentWidth) {
			// We can fit with minimum widths, distribute extra space proportionally
			double extraSpace = contentWidth - totalMinWidth;
			for (int i = 0; i < tableColumns; i++) {
				double proportion = minWidths[i] / totalMinWidth;
				columnWidths[i] = (float) (minWidths[i] + (extraSpace * proportion));
			}
		} else {
			// We need to scale down - use proportional scaling
			double scaleFactor = contentWidth / totalMinWidth;
			System.out.println("Scaling down by factor: " + scaleFactor);
			for (int i = 0; i < tableColumns; i++) {
				columnWidths[i] = (float) (minWidths[i] * scaleFactor);
			}
		}

		// Log the final widths
		System.out.println("Absolute column widths:");
		float totalWidth = 0;
		for (int i = 0; i < Math.min(5, tableColumns); i++) {
			System.out.println("  Column " + i + ": " + String.format("%.2f", columnWidths[i]) + " points");
			totalWidth += columnWidths[i];
		}
		if (tableColumns > 5) {
			for (int i = 5; i < tableColumns; i++) {
				totalWidth += columnWidths[i];
			}
			System.out.println("  ... and " + (tableColumns - 5) + " more columns");
		}
		System.out.println("Total table width: " + String.format("%.2f", totalWidth) + " points");

		return columnWidths;
	}

	private DocumentLayoutSetup getCurrentDocumentLayoutSetup(Document document) {
		// Since we don't have direct access to the document layout setup from the document,
		// we'll create a default one based on the page size
		// This should ideally be passed as a parameter or stored as an instance variable
		PageSize pageSize = document.getPdfDocument().getDefaultPageSize();
		return initDocumentLayoutSetup(pageSize);
	}

	// Keep the old method for backward compatibility
	private float[] calculateColumnWidths(List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> dataRows) {
		// Step 1: Determine the maximum number of columns
		int maxColumns = 0;

		for (List<HTMLReportTableCol> headerRow : headerRows) {
			int colCount = 0;
			for (HTMLReportTableCol col : headerRow) {
				colCount += (col.getColSpan() == null ? 1 : col.getColSpan());
			}
			maxColumns = Math.max(maxColumns, colCount);
		}

		for (List<HTMLReportTableCol> dataRow : dataRows) {
			int colCount = 0;
			for (HTMLReportTableCol col : dataRow) {
				colCount += (col.getColSpan() == null ? 1 : col.getColSpan());
			}
			maxColumns = Math.max(maxColumns, colCount);
		}

		// Step 2: Initialize column widths
		double[] columnSums = new double[maxColumns];
		int[] columnCounts = new int[maxColumns];

		// Step 3: Process header rows
		for (List<HTMLReportTableCol> headerRow : headerRows) {
			int colIndex = 0;
			for (HTMLReportTableCol col : headerRow) {
				int colSpan = (col.getColSpan() == null ? 1 : col.getColSpan());
				double valueLength = (col.getValue() != null ? col.getValue().length() : 0);

				for (int i = 0; i < colSpan; i++) {
					columnSums[colIndex] += valueLength;
					columnCounts[colIndex]++;
					colIndex++;
				}
			}
		}

		// Step 4: Process data rows
		for (List<HTMLReportTableCol> dataRow : dataRows) {
			int colIndex = 0;
			for (HTMLReportTableCol col : dataRow) {
				int colSpan = (col.getColSpan() == null ? 1 : col.getColSpan());
				double valueLength = (col.getValue() != null ? col.getValue().length() : 0);

				for (int i = 0; i < colSpan; i++) {
					columnSums[colIndex] += valueLength;
					columnCounts[colIndex]++;
					colIndex++;
				}
			}
		}

		// Step 5: Calculate average width for each column
		float[] columnWidths = new float[maxColumns];
		for (int i = 0; i < maxColumns; i++) {
			columnWidths[i] = (float) (columnCounts[i] > 0 ? columnSums[i] / columnCounts[i] : 1);
		}

		return columnWidths;
	}

	private Cell createCell(HTMLReportTableCol colData) {
		// Create a new cell with specified row span and column span
		int rowSpan = colData.getRowSpan() == null ? 1 : colData.getRowSpan();
		int colSpan = colData.getColSpan() == null ? 1 : colData.getColSpan();

		System.out.println("Creating cell: value='" + colData.getValue() + "', rowSpan=" + rowSpan + ", colSpan=" + colSpan);

		Cell cell = new Cell(rowSpan, colSpan);

		// Adjust padding based on table width - minimal padding for wide tables
		if (this.isWideTable) {
			cell.setPadding(0.5f);
			cell.setPaddingLeft(1f);
			cell.setPaddingRight(1f);
		} else {
			cell.setPadding(3f);
			cell.setPaddingLeft(5f);
			cell.setPaddingRight(5f);
		}

		// Add content to the cell
		Paragraph paragraph = new Paragraph(colData.getValue() == null ? "" : colData.getValue());

		// Set font size with a reasonable default - much smaller for wide tables
		int fontSize = colData.getSize() > 0 ? colData.getSize() : (this.isWideTable ? 5 : 9);
		paragraph.setFontSize(fontSize);

		// Set line height for better readability
		paragraph.setMultipliedLeading(1.2f);

		if (colData.isBold()) {
			paragraph.setBold();
		}

		// Set text alignment
		TextAlignment horizontalAlignment = TextAlignment.LEFT;
		if (colData.getReportTextHorizontalAlignment() != null) {
			switch (colData.getReportTextHorizontalAlignment()) {
				case CENTER:
					horizontalAlignment = TextAlignment.CENTER;
					break;
				case RIGHT:
					horizontalAlignment = TextAlignment.RIGHT;
					break;
				case LEFT:
				default:
					horizontalAlignment = TextAlignment.LEFT;
					break;
			}
		}
		paragraph.setTextAlignment(horizontalAlignment);

		// Set vertical alignment
		VerticalAlignment verticalAlignment = VerticalAlignment.MIDDLE;
		if (colData.getReportTextVerticalAlignment() != null) {
			switch (colData.getReportTextVerticalAlignment()) {
				case TOP:
					verticalAlignment = VerticalAlignment.TOP;
					break;
				case BOTTOM:
					verticalAlignment = VerticalAlignment.BOTTOM;
					break;
				case MIDDLE:
				default:
					verticalAlignment = VerticalAlignment.MIDDLE;
					break;
			}
		}
		cell.setVerticalAlignment(verticalAlignment);

		// Set text color
		if (colData.getTextColor() != null) {
			List<Integer> rgb = EColorUtils.hex2Rgb(colData.getTextColor());
			Color textColor = new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2));
			paragraph.setFontColor(textColor);
		}

		// Set cell background color
		if (colData.getCellColor() != null) {
			List<Integer> rgb = EColorUtils.hex2Rgb(colData.getCellColor());
			Color cellColor = new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2));
			cell.setBackgroundColor(cellColor);
		}

		// Add the paragraph to the cell
		cell.add(paragraph);

		// Ensure the cell content doesn't overflow
		cell.setKeepTogether(true);

		return cell;
	}

	private void generateReportInstituteHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
											   PdfFont regularBoldFont, ReportSheetDetails reportSheetDetails) {
		try {
			if (reportSheetDetails.isShowInstituteName() || reportSheetDetails.isShowInstituteLetterHead()) {
				float contentFontSize = 14f;
				int singleContentColumn = 1;
				Table table = new Table(singleContentColumn);
				CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
				float contentWidth = getContentWidth(documentLayoutSetup);
				cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(regularBoldFont)
						.setFontSize(contentFontSize).setTextAlignment(TextAlignment.CENTER);

				Institute institute = reportSheetDetails.getInstitute();
				DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null, null,
						null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);
				generateDynamicImageProvider(documentLayoutData, 20f, documentLayoutSetup.getPageSize().getHeight() - 98f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

				generateDynamicImageProvider(documentLayoutData, documentLayoutSetup.getPageSize().getWidth() - 20f - 90f,
						documentLayoutSetup.getPageSize().getHeight() - 98f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);

				String instituteName = institute.getInstituteName();
				String letterHead1 = institute.getLetterHeadLine1();
				String letterHead2 = institute.getLetterHeadLine2();
				if (reportSheetDetails.isShowInstituteName()) {
					addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(instituteName)),
							cellLayoutSetup.copy().setFontSize(16f));
				}
				if (reportSheetDetails.isShowInstituteLetterHead()) {
					addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(letterHead1)),
							cellLayoutSetup.copy().setFontSize(10f));
					addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(letterHead2)),
							cellLayoutSetup.copy().setFontSize(10f));
				}
				document.add(table);
				addBlankLine(document, false, 1);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating pdf report", e);
		}
	}

	private void generateReportHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
									  PdfFont regularBoldFont, ReportDetails reportDetails, PdfFont regularFont,
									  boolean showDateAndTime, boolean isShowCenterHeading) {
		try {
			if (isShowCenterHeading) {
				generateHeading(document, documentLayoutSetup, regularBoldFont, reportDetails.getReportName());
			}
			if (showDateAndTime) {
				generateGeneratedDateAndTime(document, documentLayoutSetup, regularBoldFont);
			}
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Error while generating {} report ", reportDetails.getReportName());
		}
	}

	private void generateGeneratedDateAndTime(Document document, DocumentLayoutSetup documentLayoutSetup,
											  PdfFont regularBoldFont) {
		float contentFontSize = 12f;
		int singleContentColumn = 1;
		Table table4 = new Table(singleContentColumn);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		float contentWidth = getContentWidth(documentLayoutSetup);
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(regularBoldFont)
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.RIGHT);
		addRow(table4, documentLayoutSetup, Arrays.asList(getParagraph(
				"Generated At : " + DateUtils.getFormattedDate(DateUtils.now(),
						DEFAULT_DATE_TIME_FORMAT, DEFAULT_TIMEZONE)).setUnderline()), cellLayoutSetup.setFontSize(12f));
		document.add(table4);
		addBlankLine(document, false, 1);
	}

	private void generateHeading(Document document, DocumentLayoutSetup documentLayoutSetup,
								 PdfFont regularBoldFont, String heading) {
		float contentFontSize = 14f;
		int singleContentColumn = 1;
		Table table4 = new Table(singleContentColumn);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		float contentWidth = getContentWidth(documentLayoutSetup);
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(regularBoldFont)
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.CENTER);
		addRow(table4, documentLayoutSetup, Arrays.asList(getParagraph(heading.toUpperCase())
						.setCharacterSpacing(1f).setUnderline()),
				cellLayoutSetup.setFontSize(12f));
		document.add(table4);
		addBlankLine(document, false, 1);
	}

	private void generateReportDetails(Document document, DocumentLayoutSetup documentLayoutSetup,
									   PdfFont regularBoldFont, PdfFont regularFont, ReportDetails reportDetails, Module module) {
		try {
			if (reportDetails == null || CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList())) {
				return;
			}
			for (ReportSheetDetails reportSheetDetails : reportDetails.getReportSheetDetailsList()) {
				if (CollectionUtils.isEmpty(reportSheetDetails.getReportCellDetails())) {
					continue;
				}
				float[] contentColWidths = getTableWidths(reportSheetDetails.getHeaderReportCellDetails(), reportSheetDetails.getReportCellDetails());

				/**
				 * Starting from row 1 as 0th row is for header (report name & session details)
				 */
				int row = -1;
				if (!CollectionUtils.isEmpty( reportSheetDetails.getHeaderReportCellDetails())) {
					row = generateReportRow(documentLayoutSetup, document, row, reportSheetDetails.getHeaderReportCellDetails(),
							CollectionUtils.isEmpty(reportSheetDetails.getHeaderReportCellDetails()) ?
									reportSheetDetails.getReportCellDetails().get(0).size() : reportSheetDetails.getHeaderReportCellDetails().size(),
							reportSheetDetails.getHeaderMergeCellIndexesList(), regularFont, regularBoldFont, contentColWidths, module);
				}

				row = generateReportRow(documentLayoutSetup, document, row, reportSheetDetails.getReportCellDetails(),
						reportSheetDetails.getTotalColumns() == null ?
								reportSheetDetails.getReportCellDetails().get(0).size() : reportSheetDetails.getTotalColumns(),
						reportSheetDetails.getMergeCellIndexesList(), regularFont, regularBoldFont, contentColWidths, module);

			}
		} catch (final Exception e) {
			logger.error("Error while generating report", e);
		}
	}

	private float[] getTableWidths(List<List<ReportCellDetails>> headers, List<List<ReportCellDetails>> reportCellDetailsList) {
		if (reportCellDetailsList == null || reportCellDetailsList.isEmpty()) {
			return new float[0];
		}
		int maxHeaderColCount = 0;
		for (List<ReportCellDetails> headerRow : headers) {
			maxHeaderColCount = Math.max(maxHeaderColCount, headerRow.size());
		}

		// Determine the maximum number of columns
		int maxColumns = reportCellDetailsList.get(0).size();

		for (List<ReportCellDetails> row : reportCellDetailsList) {
			maxColumns = Math.max(maxColumns, row.size());
		}

		// Initialize arrays to store column sums and counts
		double[] columnSums = new double[maxColumns];
		int[] columnCounts = new int[maxColumns];

		for (List<ReportCellDetails> headerRow : headers) {
			if (headerRow.size() == 1) {
				continue;
			}
			int i = 0;
			for (ReportCellDetails headerCol : headerRow) {
				boolean emptyCell = headerCol.getValue() == null || String.valueOf(headerCol.getValue()).length() == 0;
				double currentValue = columnSums[i];
				if (!emptyCell && Double.compare(currentValue, 0d) > 0) {
					columnSums[i] = Math.min(columnSums[i], headerCol.getValue() == null ? 0 : String.valueOf(headerCol.getValue()).length());
				} else {
					columnSums[i] = Math.max(columnSums[i], headerCol.getValue() == null ? 0 : String.valueOf(headerCol.getValue()).length());
				}
				i++;
			}
		}
		for (List<ReportCellDetails> row : reportCellDetailsList) {
			int i = 0;
			for (ReportCellDetails col : row) {
				columnSums[i] = Math.max(columnSums[i], col.getValue() == null ? 0 : String.valueOf(col.getValue()).length());
				i++;
			}
		}
		float totalSum = 0;
		for (int col = 0; col < maxColumns; col++) {
			totalSum += (float) (columnSums[col]);
		}

		// Normalize to ensure the sum of widths is 1
		float[] columnWidths = new float[maxColumns];
		for (int col = 0; col < maxColumns; col++) {
			columnWidths[col] = (float) (columnSums[col] / totalSum);
		}

		return columnWidths;
	}

	private float[] getTableWidths(List<List<ReportCellDetails>> reportCellDetailsList) {
		if (reportCellDetailsList == null || reportCellDetailsList.isEmpty()) {
			return new float[0];
		}
		// Determine the maximum number of columns
		int maxColumns = reportCellDetailsList.get(0).size();

		for (List<ReportCellDetails> row : reportCellDetailsList) {
			maxColumns = Math.max(maxColumns, row.size());
		}

		// Initialize arrays to store column sums and counts
		double[] columnSums = new double[maxColumns];
		int[] columnCounts = new int[maxColumns];

		// Iterate over rows to calculate total size and count for each column
		for (List<ReportCellDetails> row : reportCellDetailsList) {
			for (int col = 0; col < row.size(); col++) {
				ReportCellDetails cell = row.get(col);
				Object value = cell != null ? cell.getValue() : null;
				columnSums[col] += value != null ? String.valueOf(value).length() : 0;
				columnCounts[col]++;
			}
		}

		// Calculate average sizes for each column
		double totalAverageSize = 0;
		for (int col = 0; col < maxColumns; col++) {
			if (columnCounts[col] > 0) {
				columnSums[col] /= columnCounts[col]; // Calculate average size
			}
			totalAverageSize += columnSums[col];
		}

		// Normalize to ensure the sum of widths is 1
		float[] columnWidths = new float[maxColumns];
		for (int col = 0; col < maxColumns; col++) {
			columnWidths[col] = totalAverageSize > 0 ? (float) (columnSums[col] / totalAverageSize) : 0;
		}

		return columnWidths;
	}

	private float[] getTableWidthsMax(List<List<ReportCellDetails>> reportCellDetailsList) {
		if (reportCellDetailsList == null || reportCellDetailsList.isEmpty()) {
			return new float[0];
		}

		// Determine the maximum number of columns
		int maxColumns = reportCellDetailsList.get(0).size();

		for (List<ReportCellDetails> row : reportCellDetailsList) {
			maxColumns = Math.max(maxColumns, row.size());
		}

		// Initialize arrays to store column sums and counts
		double[] columnSums = new double[maxColumns];
		int[] columnCounts = new int[maxColumns];

		// Iterate over rows to calculate total size and count for each column
		for (List<ReportCellDetails> row : reportCellDetailsList) {
			for (int col = 0; col < row.size(); col++) {
				ReportCellDetails cell = row.get(col);
				Object value = cell != null ? cell.getValue() : null;
				columnSums[col] = Math.max(columnSums[col], value != null ? String.valueOf(value).length() : 0);
				columnCounts[col]++;
			}
		}

		// Calculate average sizes for each column
		double totalAverageSize = 0;
		for (int col = 0; col < maxColumns; col++) {
			if (columnCounts[col] > 0) {
				columnSums[col] /= columnCounts[col]; // Calculate average size
			}
			totalAverageSize += columnSums[col];
		}

		// Normalize to ensure the sum of widths is 1
		float[] columnWidths = new float[maxColumns];
		for (int col = 0; col < maxColumns; col++) {
			columnWidths[col] = (float) (columnSums[col] / maxColumns);
		}

		return columnWidths;
	}

	private boolean newFlow(Module module){
		if(module == null){
			return false;
		}
		return (module == Module.EXAMINATION || module == Module.FEES || module == Module.HOMEWORK_MANAGEMENT || module == Module.STUDENT_FINANCE);
	}

	private int generateReportRow(DocumentLayoutSetup documentLayoutSetup, Document document, int row,
								  List<List<ReportCellDetails>> reportCellDetailsList, int noOfColumns,
								  List<CellIndexes> mergeCellIndexesList, PdfFont regularFont, PdfFont boldFont, float[] contentColumnWiths, Module module) {

		Table table = getPDFTable(documentLayoutSetup, noOfColumns)
				.setHorizontalAlignment(HorizontalAlignment.CENTER);
		if(newFlow(module)){
			if (contentColumnWiths.length == noOfColumns) {
				table = getPDFTable(documentLayoutSetup, contentColumnWiths)
						.setHorizontalAlignment(HorizontalAlignment.CENTER);
			}
		}

		int rowCount = 0;
		for (List<ReportCellDetails> reportCellDetailsRow : reportCellDetailsList) {
			if (CollectionUtils.isEmpty(reportCellDetailsRow)) {
				continue;
			}

			row++;
			int col = -1;
			PdfRowProperties pdfRowProperties = getPdfRowProperties(row, mergeCellIndexesList);
			if (!pdfRowProperties.isHasRowSpan() || pdfRowProperties.isFirstRow()) {
				table = getPDFTable(documentLayoutSetup,  reportCellDetailsRow.size())
						.setHorizontalAlignment(HorizontalAlignment.CENTER);
				if(newFlow(module)){
					if (contentColumnWiths.length == reportCellDetailsRow.size()) {
						table = getPDFTable(documentLayoutSetup, contentColumnWiths)
								.setHorizontalAlignment(HorizontalAlignment.CENTER);
					}
				}
			}
			for (ReportCellDetails reportCellDetails : reportCellDetailsRow) {
				col++;
				String value = StringHelper.convertObjectToStringValue(reportCellDetails.getValue(),
						reportCellDetails.getDataType());
				CellDetails cellDetails = getCellDetails(row, col, mergeCellIndexesList);
				if (!cellDetails.isAddCell()) {
					continue;
				}

				TextAlignment horizontalTextAlignment = TextAlignment.LEFT;
				switch (reportCellDetails.getReportTextHorizontalAlignment()) {
					case CENTER:
						horizontalTextAlignment = TextAlignment.CENTER;
						break;
					case RIGHT:
						horizontalTextAlignment = TextAlignment.RIGHT;
						break;
					case LEFT:
						horizontalTextAlignment = TextAlignment.LEFT;
						break;
				}
				VerticalAlignment verticalTextAlignment = VerticalAlignment.MIDDLE;
				switch (reportCellDetails.getReportTextVerticalAlignment()) {
					case MIDDLE:
						verticalTextAlignment = VerticalAlignment.MIDDLE;
						break;
					case TOP:
						verticalTextAlignment = VerticalAlignment.TOP;
						break;
					case BOTTOM:
						verticalTextAlignment = VerticalAlignment.BOTTOM;
						break;
				}

				String textColorString = reportCellDetails.getTextColor();
				List<Integer> textHexColorRGB = EColorUtils.hex2Rgb(textColorString);
				Color textColor = Color.convertRgbToCmyk(new DeviceRgb(textHexColorRGB.get(0), textHexColorRGB.get(1), textHexColorRGB.get(2)));

				String cellColorString = reportCellDetails.getCellColor();
				List<Integer> cellHexColorRGB = EColorUtils.hex2Rgb(cellColorString);
				Color cellColor = Color.convertRgbToCmyk(new DeviceRgb(cellHexColorRGB.get(0), cellHexColorRGB.get(1), cellHexColorRGB.get(2)));

				Cell cell = new Cell(cellDetails.getRowSpan(), cellDetails.getColSpan());
				cell.add(getParagraph(value == null ? "" : value).setMultipliedLeading(0.83f).setFont(reportCellDetails.isBold() ? boldFont : regularFont)
								.setFontSize(reportCellDetails.getSize())).setTextAlignment(horizontalTextAlignment).setVerticalAlignment(verticalTextAlignment)
						.setFontColor(textColor).setBackgroundColor(cellColor);

				if (reportCellDetails.getCellHeight() > 0) {
					cell.setHeight(reportCellDetails.getCellHeight());
				}

				table.addCell(cell);
			}
			if (!pdfRowProperties.isHasRowSpan() || (pdfRowProperties.isLastRow() && !pdfRowProperties.isFirstRow())) {
				document.add(table);
			}
		}
		return row;
	}


	private CellDetails getCellDetails(int row, int col, List<CellIndexes> mergeCellIndexesList) {
		int rowSpan = 1;
		int colSpan = 1;
		boolean addCell = true;

		if (!CollectionUtils.isEmpty(mergeCellIndexesList)) {
			PdfCellProperties pdfCellProperties = getPdfCellProperties(row, col, mergeCellIndexesList);
			if (pdfCellProperties.isMergeRegion()) {
				CellIndexes cellIndexes = pdfCellProperties.getCellIndexes();
				if (cellIndexes != null) {
					if (addCellData(row, col, cellIndexes)) {
						int xxIndex = cellIndexes.getIndexXX();
						int xyIndex = cellIndexes.getIndexXY();
						int yxIndex = cellIndexes.getIndexYX();
						int yyIndex = cellIndexes.getIndexYY();
						rowSpan = xyIndex - xxIndex + 1;
						colSpan = yyIndex - yxIndex + 1;
						addCell = true;
					} else {
						addCell = false;
					}
				}
			}
		}
		return new CellDetails(rowSpan, colSpan, addCell);
	}

	private boolean addCellData(int row, int col, CellIndexes cellIndexes) {
		int xxIndex = cellIndexes.getIndexXX();
		int yxIndex = cellIndexes.getIndexYX();
		if (row == xxIndex && col == yxIndex) {
			return true;
		}
		return false;
	}

	private PdfCellProperties getPdfCellProperties(int row, int col, List<CellIndexes> mergeCellIndexesList) {
		CellIndexes currentCellIndexes = null;
		boolean isMergeRegion = false;
		for (CellIndexes cellIndexes : mergeCellIndexesList) {
			int xxIndex = cellIndexes.getIndexXX();
			int xyIndex = cellIndexes.getIndexXY();
			int yxIndex = cellIndexes.getIndexYX();
			int yyIndex = cellIndexes.getIndexYY();
			if (row >= xxIndex && row <= xyIndex && col >= yxIndex && col <= yyIndex) {
				currentCellIndexes = cellIndexes;
				isMergeRegion = true;
				break;
			}
		}
		return new PdfCellProperties(isMergeRegion, currentCellIndexes);
	}

	private PdfRowProperties getPdfRowProperties(int row, List<CellIndexes> mergeCellIndexesList) {

		boolean hasRowSpan = false;
		boolean isFirstRow = false;
		boolean isLastRow = false;
		if (!CollectionUtils.isEmpty(mergeCellIndexesList)) {

			for (CellIndexes cellIndexes : mergeCellIndexesList) {
				int xxIndex = cellIndexes.getIndexXX();
				int xyIndex = cellIndexes.getIndexXY();
				int rowSpan = xyIndex - xxIndex + 1;
				if (row >= xxIndex && row <= xyIndex && rowSpan > 1) {
					hasRowSpan = true;
				}
				if (row == xyIndex) {
					isLastRow = true;
				}
				if (row == xxIndex) {
					isFirstRow = true;
				}
			}
		}

		return new PdfRowProperties(hasRowSpan, isFirstRow, isLastRow);
	}

	class PdfRowProperties {

		private boolean hasRowSpan;

		private boolean isFirstRow;

		private boolean isLastRow;

		public PdfRowProperties(boolean hasRowSpan, boolean isFirstRow, boolean isLastRow) {
			this.hasRowSpan = hasRowSpan;
			this.isFirstRow = isFirstRow;
			this.isLastRow = isLastRow;
		}

		public boolean isHasRowSpan() {
			return hasRowSpan;
		}

		public void setHasRowSpan(boolean hasRowSpan) {
			this.hasRowSpan = hasRowSpan;
		}

		public boolean isFirstRow() {
			return isFirstRow;
		}

		public void setFirstRow(boolean firstRow) {
			isFirstRow = firstRow;
		}

		public boolean isLastRow() {
			return isLastRow;
		}

		public void setLastRow(boolean lastRow) {
			isLastRow = lastRow;
		}

		@Override
		public String toString() {
			return "PdfRowProperties{" +
					"hasRowSpan=" + hasRowSpan +
					", isFirstRow=" + isFirstRow +
					", isLastRow=" + isLastRow +
					'}';
		}
	}

	class PdfCellProperties {
		private boolean isMergeRegion;

		private CellIndexes cellIndexes;

		public PdfCellProperties(boolean isMergeRegion, CellIndexes cellIndexes) {
			this.isMergeRegion = isMergeRegion;
			this.cellIndexes = cellIndexes;
		}

		public boolean isMergeRegion() {
			return isMergeRegion;
		}

		public void setMergeRegion(boolean mergeRegion) {
			isMergeRegion = mergeRegion;
		}

		public CellIndexes getCellIndexes() {
			return cellIndexes;
		}

		public void setCellIndexes(CellIndexes cellIndexes) {
			this.cellIndexes = cellIndexes;
		}

		@Override
		public String toString() {
			return "PdfCellProperties{" +
					"isMergeRegion=" + isMergeRegion +
					", cellIndexes=" + cellIndexes +
					'}';
		}
	}

	class CellDetails {

		private int rowSpan;

		private int colSpan;

		private boolean addCell;

		public CellDetails(int rowSpan, int colSpan, boolean addCell) {
			this.rowSpan = rowSpan;
			this.colSpan = colSpan;
			this.addCell = addCell;
		}

		public int getRowSpan() {
			return rowSpan;
		}

		public void setRowSpan(int rowSpan) {
			this.rowSpan = rowSpan;
		}

		public int getColSpan() {
			return colSpan;
		}

		public void setColSpan(int colSpan) {
			this.colSpan = colSpan;
		}

		public boolean isAddCell() {
			return addCell;
		}

		public void setAddCell(boolean addCell) {
			this.addCell = addCell;
		}

		@Override
		public String toString() {
			return "CellDetails{" +
					"rowSpan=" + rowSpan +
					", colSpan=" + colSpan +
					", addCell=" + addCell +
					'}';
		}
	}
}
