package com.embrate.cloud.pdf.reports;

import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Calculates optimal column widths based on content analysis and available page space.
 * 
 * This class implements intelligent column width calculation that:
 * - Analyzes actual content to determine natural column widths
 * - Prevents content overflow by ensuring minimum readable widths
 * - Distributes available space proportionally
 * - Handles edge cases like very long content or many columns
 * 
 * Follows Single Responsibility Principle by focusing only on width calculations.
 * 
 * <AUTHOR> Assistant
 */
public class ColumnWidthCalculator {
    
    private static final Logger logger = LogManager.getLogger(ColumnWidthCalculator.class);
    
    // Constants for width calculation
    private static final double MIN_COLUMN_WIDTH_RATIO = 0.05; // 5% minimum width per column
    private static final double MAX_COLUMN_WIDTH_RATIO = 0.40; // 40% maximum width per column
    private static final int SAMPLE_ROWS_FOR_ANALYSIS = 10; // Number of rows to analyze for width calculation
    private static final double FONT_SIZE_WIDTH_FACTOR = 0.6; // Approximate character width factor
    
    /**
     * Calculates optimal column widths as percentage values that sum to 100%
     * 
     * @param sheetDetails The report sheet containing data
     * @param pageSize The page size being used
     * @return Array of column width percentages
     */
    public float[] calculateColumnWidths(ReportSheetDetails sheetDetails, PageSize pageSize) {
        
        if (CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            return new float[]{100f}; // Single column fallback
        }
        
        // Determine number of columns
        int columnCount = getColumnCount(sheetDetails);
        
        if (columnCount <= 0) {
            return new float[]{100f};
        }
        
        // Analyze content to get natural widths
        double[] naturalWidths = analyzeContentWidths(sheetDetails, columnCount);
        
        // Calculate available width considering page margins
        double availableWidth = calculateAvailableWidth(pageSize);
        
        // Convert to optimal percentages
        float[] columnWidths = calculateOptimalPercentages(naturalWidths, availableWidth, columnCount);
        
        logger.debug("Calculated column widths for {} columns: {}", columnCount, columnWidths);
        
        return columnWidths;
    }
    
    /**
     * Determines the number of columns in the report
     */
    private int getColumnCount(ReportSheetDetails sheetDetails) {
        
        // Use explicit total columns if set
        if (sheetDetails.getTotalColumns() != null) {
            return sheetDetails.getTotalColumns();
        }
        
        // Check header columns
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            return sheetDetails.getHeaderReportCellDetails().get(0).size();
        }
        
        // Check data columns (skip first row if it might be a title row)
        List<List<ReportCellDetails>> reportCells = sheetDetails.getReportCellDetails();
        if (reportCells.size() > 1) {
            return reportCells.get(1).size();
        } else if (!reportCells.isEmpty()) {
            return reportCells.get(0).size();
        }
        
        return 1; // Fallback
    }
    
    /**
     * Analyzes content to determine natural column widths based on content length
     */
    private double[] analyzeContentWidths(ReportSheetDetails sheetDetails, int columnCount) {
        
        double[] maxWidths = new double[columnCount];
        double[] avgWidths = new double[columnCount];
        int[] cellCounts = new int[columnCount];
        
        // Analyze header content
        analyzeRowsForWidths(sheetDetails.getHeaderReportCellDetails(), maxWidths, avgWidths, cellCounts);
        
        // Analyze data content (sample first N rows for performance)
        List<List<ReportCellDetails>> dataRows = sheetDetails.getReportCellDetails();
        int rowsToAnalyze = Math.min(SAMPLE_ROWS_FOR_ANALYSIS, dataRows.size());
        
        for (int i = 0; i < rowsToAnalyze; i++) {
            List<ReportCellDetails> row = dataRows.get(i);
            if (!CollectionUtils.isEmpty(row)) {
                for (int j = 0; j < Math.min(row.size(), columnCount); j++) {
                    ReportCellDetails cell = row.get(j);
                    if (cell != null && cell.getValue() != null) {
                        double contentWidth = calculateContentWidth(cell);
                        maxWidths[j] = Math.max(maxWidths[j], contentWidth);
                        avgWidths[j] += contentWidth;
                        cellCounts[j]++;
                    }
                }
            }
        }
        
        // Calculate final natural widths (weighted average of max and average)
        double[] naturalWidths = new double[columnCount];
        for (int i = 0; i < columnCount; i++) {
            if (cellCounts[i] > 0) {
                avgWidths[i] /= cellCounts[i];
                // Use 70% max width + 30% average width for balanced sizing
                naturalWidths[i] = (maxWidths[i] * 0.7) + (avgWidths[i] * 0.3);
            } else {
                naturalWidths[i] = 50; // Default width for empty columns
            }
        }
        
        return naturalWidths;
    }
    
    /**
     * Analyzes a set of rows for width calculation
     */
    private void analyzeRowsForWidths(List<List<ReportCellDetails>> rows, double[] maxWidths, 
                                    double[] avgWidths, int[] cellCounts) {
        
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        
        for (List<ReportCellDetails> row : rows) {
            if (!CollectionUtils.isEmpty(row)) {
                for (int j = 0; j < Math.min(row.size(), maxWidths.length); j++) {
                    ReportCellDetails cell = row.get(j);
                    if (cell != null && cell.getValue() != null) {
                        double contentWidth = calculateContentWidth(cell);
                        maxWidths[j] = Math.max(maxWidths[j], contentWidth);
                        avgWidths[j] += contentWidth;
                        cellCounts[j]++;
                    }
                }
            }
        }
    }
    
    /**
     * Calculates the display width needed for a cell's content
     */
    private double calculateContentWidth(ReportCellDetails cell) {
        
        String content = String.valueOf(cell.getValue());
        int fontSize = cell.getSize();
        
        // Estimate width based on content length and font size
        double baseWidth = content.length() * FONT_SIZE_WIDTH_FACTOR;
        double fontFactor = fontSize / 12.0; // Normalize to 12pt font
        
        return baseWidth * fontFactor;
    }
    
    /**
     * Calculates available width on the page considering margins
     */
    private double calculateAvailableWidth(PageSize pageSize) {
        
        // Standard margins (approximate)
        double leftMargin = 40;
        double rightMargin = 40;
        
        return pageSize.getWidth() - leftMargin - rightMargin;
    }
    
    /**
     * Converts natural widths to optimal percentage values
     */
    private float[] calculateOptimalPercentages(double[] naturalWidths, double availableWidth, int columnCount) {
        
        // Calculate total natural width
        double totalNaturalWidth = 0;
        for (double width : naturalWidths) {
            totalNaturalWidth += width;
        }
        
        float[] percentages = new float[columnCount];
        
        if (totalNaturalWidth <= availableWidth) {
            // Content fits naturally, distribute proportionally
            for (int i = 0; i < columnCount; i++) {
                percentages[i] = (float) ((naturalWidths[i] / totalNaturalWidth) * 100);
            }
        } else {
            // Content is too wide, apply constraints and redistribute
            percentages = applyWidthConstraints(naturalWidths, columnCount);
        }
        
        // Ensure minimum and maximum width constraints
        percentages = enforceWidthLimits(percentages, columnCount);
        
        // Normalize to ensure sum equals 100%
        percentages = normalizePercentages(percentages);
        
        return percentages;
    }
    
    /**
     * Applies width constraints when content is too wide for the page
     */
    private float[] applyWidthConstraints(double[] naturalWidths, int columnCount) {
        
        float[] percentages = new float[columnCount];
        double totalWeight = 0;
        
        // Calculate weights based on natural widths but with constraints
        for (int i = 0; i < columnCount; i++) {
            // Apply logarithmic scaling to prevent extremely wide columns
            double constrainedWidth = Math.log(naturalWidths[i] + 1) * 10;
            percentages[i] = (float) constrainedWidth;
            totalWeight += constrainedWidth;
        }
        
        // Convert to percentages
        if (totalWeight > 0) {
            for (int i = 0; i < columnCount; i++) {
                percentages[i] = (percentages[i] / (float) totalWeight) * 100;
            }
        } else {
            // Equal distribution fallback
            float equalWidth = 100f / columnCount;
            for (int i = 0; i < columnCount; i++) {
                percentages[i] = equalWidth;
            }
        }
        
        return percentages;
    }
    
    /**
     * Enforces minimum and maximum width limits
     */
    private float[] enforceWidthLimits(float[] percentages, int columnCount) {
        
        float minWidth = (float) (MIN_COLUMN_WIDTH_RATIO * 100);
        float maxWidth = (float) (MAX_COLUMN_WIDTH_RATIO * 100);
        
        // Apply minimum width constraint
        for (int i = 0; i < columnCount; i++) {
            if (percentages[i] < minWidth) {
                percentages[i] = minWidth;
            }
        }
        
        // Apply maximum width constraint and redistribute excess
        float totalExcess = 0;
        int columnsAtMax = 0;
        
        for (int i = 0; i < columnCount; i++) {
            if (percentages[i] > maxWidth) {
                totalExcess += percentages[i] - maxWidth;
                percentages[i] = maxWidth;
                columnsAtMax++;
            }
        }
        
        // Redistribute excess to columns not at maximum
        if (totalExcess > 0 && columnsAtMax < columnCount) {
            float redistributionPerColumn = totalExcess / (columnCount - columnsAtMax);
            for (int i = 0; i < columnCount; i++) {
                if (percentages[i] < maxWidth) {
                    percentages[i] += redistributionPerColumn;
                    // Ensure we don't exceed max after redistribution
                    if (percentages[i] > maxWidth) {
                        percentages[i] = maxWidth;
                    }
                }
            }
        }
        
        return percentages;
    }
    
    /**
     * Normalizes percentages to ensure they sum to 100%
     */
    private float[] normalizePercentages(float[] percentages) {
        
        float total = 0;
        for (float percentage : percentages) {
            total += percentage;
        }
        
        if (total > 0 && Math.abs(total - 100) > 0.01) {
            float factor = 100f / total;
            for (int i = 0; i < percentages.length; i++) {
                percentages[i] *= factor;
            }
        }
        
        return percentages;
    }
}
