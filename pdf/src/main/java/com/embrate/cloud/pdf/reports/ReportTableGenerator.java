package com.embrate.cloud.pdf.reports;

import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.report.ReportUtils;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Generates report tables with intelligent column width management and content handling.
 * 
 * This class follows the Single Responsibility Principle by focusing
 * solely on table generation logic.
 * 
 * <AUTHOR> Assistant
 */
public class ReportTableGenerator {
    
    private static final Logger logger = LogManager.getLogger(ReportTableGenerator.class);
    
    /**
     * Generates the main report table with optimal formatting
     * 
     * @param document The PDF document
     * @param documentLayoutSetup Document layout configuration
     * @param sheetDetails Report sheet data and configuration
     * @param module Module context for flow determination
     * @param columnWidthCalculator Calculator for column widths
     * @param fontSizeCalculator Calculator for font sizes
     */
    public void generateReportTable(Document document, DocumentLayoutSetup documentLayoutSetup,
                                  ReportSheetDetails sheetDetails, Module module,
                                  ColumnWidthCalculator columnWidthCalculator,
                                  FontSizeCalculator fontSizeCalculator) {
        
        try {
            if (shouldUseNewFlow(module)) {
                generateTableUsingNewFlow(document, documentLayoutSetup, sheetDetails, 
                                        columnWidthCalculator, fontSizeCalculator);
            } else {
                generateTableUsingLegacyFlow(document, documentLayoutSetup, sheetDetails,
                                           columnWidthCalculator, fontSizeCalculator);
            }
        } catch (Exception e) {
            logger.error("Error generating report table", e);
        }
    }
    
    /**
     * Generates table using the new flow with HTMLReportTable conversion
     */
    private void generateTableUsingNewFlow(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         ReportSheetDetails sheetDetails,
                                         ColumnWidthCalculator columnWidthCalculator,
                                         FontSizeCalculator fontSizeCalculator) {
        
        // Convert to HTML table format
        ReportDetails tempReportDetails = new ReportDetails("temp", List.of(sheetDetails));
        HTMLReportTable htmlTable = ReportUtils.convertToHTMLTable(tempReportDetails);
        
        if (htmlTable == null) {
            logger.warn("Failed to convert report to HTML table format");
            return;
        }
        
        // Calculate optimal column widths
        float[] columnWidths = calculateColumnWidthsFromHTML(htmlTable, documentLayoutSetup.getPageSize(),
                                                           columnWidthCalculator);
        
        // Calculate optimal font sizes
        float headerFontSize = fontSizeCalculator.calculateHeaderFontSize(sheetDetails, 
                                                                         documentLayoutSetup.getPageSize());
        float dataFontSize = fontSizeCalculator.calculateDataFontSize(sheetDetails, 
                                                                     documentLayoutSetup.getPageSize());
        
        // Create and populate table
        Table table = createOptimalTable(columnWidths);
        
        // Add header rows
        addHeaderRowsToTable(table, htmlTable.getHeaderRows(), headerFontSize);
        
        // Add data rows
        addDataRowsToTable(table, htmlTable.getRows(), dataFontSize);
        
        document.add(table);
    }
    
    /**
     * Generates table using the legacy flow with ReportCellDetails
     */
    private void generateTableUsingLegacyFlow(Document document, DocumentLayoutSetup documentLayoutSetup,
                                            ReportSheetDetails sheetDetails,
                                            ColumnWidthCalculator columnWidthCalculator,
                                            FontSizeCalculator fontSizeCalculator) {
        
        // Calculate optimal column widths
        float[] columnWidths = columnWidthCalculator.calculateColumnWidths(sheetDetails, 
                                                                         documentLayoutSetup.getPageSize());
        
        // Calculate optimal font sizes
        float headerFontSize = fontSizeCalculator.calculateHeaderFontSize(sheetDetails, 
                                                                         documentLayoutSetup.getPageSize());
        float dataFontSize = fontSizeCalculator.calculateDataFontSize(sheetDetails, 
                                                                     documentLayoutSetup.getPageSize());
        
        // Create and populate table
        Table table = createOptimalTable(columnWidths);
        
        // Add header rows if present
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            addLegacyHeaderRows(table, sheetDetails.getHeaderReportCellDetails(), 
                              sheetDetails.getHeaderMergeCellIndexesList(), headerFontSize);
        }
        
        // Add data rows
        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            addLegacyDataRows(table, sheetDetails.getReportCellDetails(), 
                            sheetDetails.getMergeCellIndexesList(), dataFontSize);
        }
        
        document.add(table);
    }
    
    /**
     * Calculates column widths from HTML table data
     */
    private float[] calculateColumnWidthsFromHTML(HTMLReportTable htmlTable, 
                                                com.itextpdf.kernel.geom.PageSize pageSize,
                                                ColumnWidthCalculator columnWidthCalculator) {
        
        List<List<HTMLReportTableCol>> allRows = htmlTable.getHeaderRows();
        allRows.addAll(htmlTable.getRows());
        
        if (allRows.isEmpty()) {
            return new float[]{100f};
        }
        
        // Determine maximum columns
        int maxColumns = allRows.stream()
                               .mapToInt(row -> row.stream()
                                              .mapToInt(col -> col.getColSpan() == null ? 1 : col.getColSpan())
                                              .sum())
                               .max()
                               .orElse(1);
        
        // Analyze content for width calculation
        double[] contentWidths = new double[maxColumns];
        
        for (List<HTMLReportTableCol> row : allRows) {
            int colIndex = 0;
            for (HTMLReportTableCol col : row) {
                if (colIndex < maxColumns) {
                    double contentLength = col.getValue() != null ? col.getValue().length() : 0;
                    double fontFactor = col.getSize() / 12.0; // Normalize to 12pt
                    contentWidths[colIndex] = Math.max(contentWidths[colIndex], contentLength * fontFactor);
                    
                    // Handle column span
                    int colSpan = col.getColSpan() == null ? 1 : col.getColSpan();
                    colIndex += colSpan;
                }
            }
        }
        
        // Convert to percentages
        return normalizeWidths(contentWidths);
    }
    
    /**
     * Creates an optimally configured table
     */
    private Table createOptimalTable(float[] columnWidths) {
        Table table = new Table(UnitValue.createPercentArray(columnWidths))
                           .setWidth(UnitValue.createPercentValue(100))
                           .setHorizontalAlignment(HorizontalAlignment.CENTER);
        
        return table;
    }
    
    /**
     * Adds header rows to table from HTML format
     */
    private void addHeaderRowsToTable(Table table, List<List<HTMLReportTableCol>> headerRows, 
                                    float defaultFontSize) {
        
        if (CollectionUtils.isEmpty(headerRows)) {
            return;
        }
        
        for (List<HTMLReportTableCol> row : headerRows) {
            for (HTMLReportTableCol col : row) {
                Cell cell = createCellFromHTMLCol(col, defaultFontSize, true);
                table.addCell(cell);
            }
        }
    }
    
    /**
     * Adds data rows to table from HTML format
     */
    private void addDataRowsToTable(Table table, List<List<HTMLReportTableCol>> dataRows, 
                                  float defaultFontSize) {
        
        if (CollectionUtils.isEmpty(dataRows)) {
            return;
        }
        
        for (List<HTMLReportTableCol> row : dataRows) {
            for (HTMLReportTableCol col : row) {
                Cell cell = createCellFromHTMLCol(col, defaultFontSize, false);
                table.addCell(cell);
            }
        }
    }
    
    /**
     * Creates a PDF cell from HTML table column data
     */
    private Cell createCellFromHTMLCol(HTMLReportTableCol col, float defaultFontSize, boolean isHeader) {
        
        int rowSpan = col.getRowSpan() == null ? 1 : col.getRowSpan();
        int colSpan = col.getColSpan() == null ? 1 : col.getColSpan();
        
        Cell cell = new Cell(rowSpan, colSpan);
        
        // Create paragraph with content
        String content = col.getValue() == null ? "" : col.getValue();
        Paragraph paragraph = new Paragraph(content);
        
        // Set font size
        float fontSize = col.getSize() > 0 ? col.getSize() : defaultFontSize;
        paragraph.setFontSize(fontSize);
        
        // Set bold if required
        if (col.isBold()) {
            paragraph.setBold();
        }
        
        // Set text alignment
        TextAlignment textAlignment = convertHorizontalAlignment(col.getReportTextHorizontalAlignment());
        paragraph.setTextAlignment(textAlignment);
        
        // Set vertical alignment
        VerticalAlignment verticalAlignment = convertVerticalAlignment(col.getReportTextVerticalAlignment());
        cell.setVerticalAlignment(verticalAlignment);
        
        // Set colors
        if (col.getTextColor() != null) {
            Color textColor = createColorFromHex(col.getTextColor());
            paragraph.setFontColor(textColor);
        }
        
        if (col.getCellColor() != null) {
            Color cellColor = createColorFromHex(col.getCellColor());
            cell.setBackgroundColor(cellColor);
        }
        
        cell.add(paragraph);
        return cell;
    }
    
    /**
     * Adds legacy header rows to table
     */
    private void addLegacyHeaderRows(Table table, List<List<ReportCellDetails>> headerRows,
                                   List<CellIndexes> mergeCellIndexes, float defaultFontSize) {
        
        // Implementation for legacy header rows
        // This would follow similar pattern to the original PdfReportGenerator
        // but with improved width and font handling
        
        for (List<ReportCellDetails> row : headerRows) {
            for (ReportCellDetails cellDetails : row) {
                Cell cell = createCellFromReportCellDetails(cellDetails, defaultFontSize, true);
                table.addCell(cell);
            }
        }
    }
    
    /**
     * Adds legacy data rows to table
     */
    private void addLegacyDataRows(Table table, List<List<ReportCellDetails>> dataRows,
                                 List<CellIndexes> mergeCellIndexes, float defaultFontSize) {
        
        // Implementation for legacy data rows
        // This would follow similar pattern to the original PdfReportGenerator
        // but with improved width and font handling
        
        for (List<ReportCellDetails> row : dataRows) {
            for (ReportCellDetails cellDetails : row) {
                Cell cell = createCellFromReportCellDetails(cellDetails, defaultFontSize, false);
                table.addCell(cell);
            }
        }
    }
    
    /**
     * Creates a PDF cell from ReportCellDetails
     */
    private Cell createCellFromReportCellDetails(ReportCellDetails cellDetails, float defaultFontSize, 
                                               boolean isHeader) {
        
        Cell cell = new Cell();
        
        // Create content
        String value = StringHelper.convertObjectToStringValue(cellDetails.getValue(), 
                                                              cellDetails.getDataType());
        Paragraph paragraph = new Paragraph(value == null ? "" : value);
        
        // Set font size
        float fontSize = cellDetails.getSize() > 0 ? cellDetails.getSize() : defaultFontSize;
        paragraph.setFontSize(fontSize);
        
        // Set bold
        if (cellDetails.isBold()) {
            paragraph.setBold();
        }
        
        // Set alignments
        TextAlignment textAlignment = convertHorizontalAlignment(cellDetails.getReportTextHorizontalAlignment());
        paragraph.setTextAlignment(textAlignment);
        
        VerticalAlignment verticalAlignment = convertVerticalAlignment(cellDetails.getReportTextVerticalAlignment());
        cell.setVerticalAlignment(verticalAlignment);
        
        // Set colors
        if (cellDetails.getTextColor() != null) {
            Color textColor = createColorFromHex(cellDetails.getTextColor());
            paragraph.setFontColor(textColor);
        }
        
        if (cellDetails.getCellColor() != null) {
            Color cellColor = createColorFromHex(cellDetails.getCellColor());
            cell.setBackgroundColor(cellColor);
        }
        
        // Set cell height if specified
        if (cellDetails.getCellHeight() > 0) {
            cell.setHeight(cellDetails.getCellHeight());
        }
        
        cell.add(paragraph);
        return cell;
    }
    
    /**
     * Converts horizontal alignment enum to iText TextAlignment
     */
    private TextAlignment convertHorizontalAlignment(ReportHorizontalTextAlignment alignment) {
        if (alignment == null) {
            return TextAlignment.LEFT;
        }
        
        switch (alignment) {
            case CENTER:
                return TextAlignment.CENTER;
            case RIGHT:
                return TextAlignment.RIGHT;
            case LEFT:
            default:
                return TextAlignment.LEFT;
        }
    }
    
    /**
     * Converts vertical alignment enum to iText VerticalAlignment
     */
    private VerticalAlignment convertVerticalAlignment(ReportVerticalTextAlignment alignment) {
        if (alignment == null) {
            return VerticalAlignment.MIDDLE;
        }
        
        switch (alignment) {
            case TOP:
                return VerticalAlignment.TOP;
            case BOTTOM:
                return VerticalAlignment.BOTTOM;
            case MIDDLE:
            default:
                return VerticalAlignment.MIDDLE;
        }
    }
    
    /**
     * Creates iText Color from hex string
     */
    private Color createColorFromHex(String hexColor) {
        try {
            List<Integer> rgb = EColorUtils.hex2Rgb(hexColor);
            return new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2));
        } catch (Exception e) {
            logger.warn("Failed to parse color: {}", hexColor);
            return new DeviceRgb(0, 0, 0); // Default to black
        }
    }
    
    /**
     * Normalizes width array to percentages that sum to 100
     */
    private float[] normalizeWidths(double[] widths) {
        double total = 0;
        for (double width : widths) {
            total += width;
        }
        
        if (total <= 0) {
            // Equal distribution fallback
            float equalWidth = 100f / widths.length;
            float[] result = new float[widths.length];
            for (int i = 0; i < widths.length; i++) {
                result[i] = equalWidth;
            }
            return result;
        }
        
        float[] result = new float[widths.length];
        for (int i = 0; i < widths.length; i++) {
            result[i] = (float) ((widths[i] / total) * 100);
        }
        
        return result;
    }
    
    /**
     * Determines if new flow should be used based on module
     */
    private boolean shouldUseNewFlow(Module module) {
        if (module == null) {
            return false;
        }
        return module == Module.EXAMINATION || 
               module == Module.FEES || 
               module == Module.HOMEWORK_MANAGEMENT || 
               module == Module.STUDENT_FINANCE;
    }
}
