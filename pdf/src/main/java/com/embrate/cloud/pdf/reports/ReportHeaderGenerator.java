package com.embrate.cloud.pdf.reports;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;

import static com.lernen.cloud.core.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static com.lernen.cloud.core.utils.DateUtils.DEFAULT_TIMEZONE;

/**
 * Generates report headers including institute information, logos, and date/time.
 * 
 * This class follows the Single Responsibility Principle by focusing
 * solely on header generation logic.
 * 
 * <AUTHOR> Assistant
 */
public class ReportHeaderGenerator extends PDFGenerator {
    
    private static final Logger logger = LogManager.getLogger(ReportHeaderGenerator.class);
    
    public static final float DEFAULT_LOGO_WIDTH = 90f;
    public static final float DEFAULT_LOGO_HEIGHT = 90f;
    
    public ReportHeaderGenerator(AssetProvider assetProvider) {
        super(assetProvider);
    }
    
    /**
     * Generates institute header with logos and institute information
     * 
     * @param document The PDF document
     * @param documentLayoutSetup Document layout configuration
     * @param reportSheetDetails Report sheet configuration
     */
    public void generateInstituteHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                                      ReportSheetDetails reportSheetDetails) {
        try {
            if (!reportSheetDetails.isShowInstituteName() && !reportSheetDetails.isShowInstituteLetterHead()) {
                return;
            }
            
            PdfFont regularBoldFont = getRegularBoldFont();
            float contentFontSize = 14f;
            int singleContentColumn = 1;
            Table table = new Table(singleContentColumn);
            CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
            float contentWidth = getContentWidth(documentLayoutSetup);
            
            cellLayoutSetup.setWidth(contentWidth / singleContentColumn)
                          .setPdfFont(regularBoldFont)
                          .setFontSize(contentFontSize)
                          .setTextAlignment(TextAlignment.CENTER);
            
            Institute institute = reportSheetDetails.getInstitute();
            if (institute != null) {
                // Generate logos
                generateInstituteLogos(document, documentLayoutSetup, institute);
                
                // Generate institute name and letterhead
                generateInstituteInfo(document, table, cellLayoutSetup, reportSheetDetails, institute);
            }
            
            document.add(table);
            addBlankLine(document, false, 1);
            
        } catch (Exception e) {
            logger.error("Error generating institute header", e);
        }
    }
    
    /**
     * Generates the main report title
     * 
     * @param document The PDF document
     * @param documentLayoutSetup Document layout configuration
     * @param reportName The name of the report
     */
    public void generateReportTitle(Document document, DocumentLayoutSetup documentLayoutSetup, 
                                  String reportName) {
        try {
            PdfFont regularBoldFont = getRegularBoldFont();
            float contentFontSize = 14f;
            int singleContentColumn = 1;
            Table table = new Table(singleContentColumn);
            CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
            float contentWidth = getContentWidth(documentLayoutSetup);
            
            cellLayoutSetup.setWidth(contentWidth / singleContentColumn)
                          .setPdfFont(regularBoldFont)
                          .setFontSize(contentFontSize)
                          .setTextAlignment(TextAlignment.CENTER);
            
            addRow(table, documentLayoutSetup, 
                  Arrays.asList(getParagraph(reportName.toUpperCase())
                               .setCharacterSpacing(1f)
                               .setUnderline()),
                  cellLayoutSetup.setFontSize(12f));
            
            document.add(table);
            addBlankLine(document, false, 1);
            
        } catch (Exception e) {
            logger.error("Error generating report title: {}", reportName, e);
        }
    }
    
    /**
     * Generates date and time header
     * 
     * @param document The PDF document
     * @param documentLayoutSetup Document layout configuration
     */
    public void generateDateTimeHeader(Document document, DocumentLayoutSetup documentLayoutSetup) {
        try {
            PdfFont regularBoldFont = getRegularBoldFont();
            float contentFontSize = 12f;
            int singleContentColumn = 1;
            Table table = new Table(singleContentColumn);
            CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
            float contentWidth = getContentWidth(documentLayoutSetup);
            
            cellLayoutSetup.setWidth(contentWidth / singleContentColumn)
                          .setPdfFont(regularBoldFont)
                          .setFontSize(contentFontSize)
                          .setTextAlignment(TextAlignment.RIGHT);
            
            String dateTimeText = "Generated At : " + DateUtils.getFormattedDate(
                DateUtils.now(), DEFAULT_DATE_TIME_FORMAT, DEFAULT_TIMEZONE);
            
            addRow(table, documentLayoutSetup, 
                  Arrays.asList(getParagraph(dateTimeText).setUnderline()),
                  cellLayoutSetup.setFontSize(12f));
            
            document.add(table);
            addBlankLine(document, false, 1);
            
        } catch (Exception e) {
            logger.error("Error generating date/time header", e);
        }
    }
    
    /**
     * Generates institute logos (primary and secondary)
     */
    private void generateInstituteLogos(Document document, DocumentLayoutSetup documentLayoutSetup, 
                                      Institute institute) {
        try {
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(
                document, documentLayoutSetup, null, null, null, null, 
                DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, 
                LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), 
                null, null);
            
            // Primary logo (left side)
            generateDynamicImageProvider(documentLayoutData, 20f, 
                documentLayoutSetup.getPageSize().getHeight() - 98f, 
                institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
            
            // Secondary logo (right side)
            generateDynamicImageProvider(documentLayoutData, 
                documentLayoutSetup.getPageSize().getWidth() - 20f - 90f,
                documentLayoutSetup.getPageSize().getHeight() - 98f, 
                institute.getInstituteId(), InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);
                
        } catch (Exception e) {
            logger.error("Error generating institute logos", e);
        }
    }
    
    /**
     * Generates institute name and letterhead information
     */
    private void generateInstituteInfo(Document document, Table table, CellLayoutSetup cellLayoutSetup,
                                     ReportSheetDetails reportSheetDetails, Institute institute) {
        try {
            if (reportSheetDetails.isShowInstituteName()) {
                String instituteName = institute.getInstituteName();
                addRow(table, null, 
                      Arrays.asList(getParagraph(instituteName)),
                      cellLayoutSetup.copy().setFontSize(16f));
            }
            
            if (reportSheetDetails.isShowInstituteLetterHead()) {
                String letterHead1 = institute.getLetterHeadLine1();
                String letterHead2 = institute.getLetterHeadLine2();
                
                if (letterHead1 != null && !letterHead1.trim().isEmpty()) {
                    addRow(table, null,
                          Arrays.asList(getParagraph(letterHead1)),
                          cellLayoutSetup.copy().setFontSize(10f));
                }
                
                if (letterHead2 != null && !letterHead2.trim().isEmpty()) {
                    addRow(table, null,
                          Arrays.asList(getParagraph(letterHead2)),
                          cellLayoutSetup.copy().setFontSize(10f));
                }
            }
            
        } catch (Exception e) {
            logger.error("Error generating institute info", e);
        }
    }
}
