package com.embrate.cloud.pdf.reports;

import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

/**
 * Calculates optimal page size based on report content analysis.
 *
 * This class follows the Single Responsibility Principle by focusing
 * solely on page size determination logic.
 *
 * <AUTHOR> Assistant
 */
public class PageSizeCalculator {

    private static final Logger logger = LogManager.getLogger(PageSizeCalculator.class);

    // Column count thresholds for different page sizes
    private static final int PORTRAIT_A4_MAX_COLUMNS = 10;
    private static final int LANDSCAPE_A4_MAX_COLUMNS = 17;
    private static final int LANDSCAPE_A3_MAX_COLUMNS = 25;

    // Content width thresholds (estimated character count per column)
    private static final int HIGH_CONTENT_THRESHOLD = 15;
    private static final int MEDIUM_CONTENT_THRESHOLD = 8;

    /**
     * Calculates the optimal page size based on report sheet details
     *
     * @param sheetDetails The report sheet containing data and configuration
     * @return Optimal PageSize for the report
     */
    public PageSize calculateOptimalPageSize(ReportSheetDetails sheetDetails) {

        // If page size is explicitly set, use it
        if (sheetDetails.getPageSize() != null) {
            logger.debug("Using explicitly set page size: {}", sheetDetails.getPageSize());
            return sheetDetails.getPageSize();
        }

        // Calculate column count
        int totalColumns = calculateTotalColumns(sheetDetails);

        // Analyze content complexity
        ContentComplexity complexity = analyzeContentComplexity(sheetDetails);

        // Determine optimal page size
        PageSize optimalSize = determinePageSize(totalColumns, complexity);

        logger.debug("Calculated optimal page size: {} for {} columns with {} complexity",
                    optimalSize, totalColumns, complexity);

        return optimalSize;
    }

    /**
     * Calculates the total number of columns in the report
     */
    private int calculateTotalColumns(ReportSheetDetails sheetDetails) {

        // Use explicitly set total columns if available
        if (sheetDetails.getTotalColumns() != null) {
            return sheetDetails.getTotalColumns();
        }

        // Calculate from header if available
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            return sheetDetails.getHeaderReportCellDetails().get(0).size();
        }

        // Calculate from data rows
        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails()) &&
            sheetDetails.getReportCellDetails().size() > 1) {
            return sheetDetails.getReportCellDetails().get(1).size();
        }

        // Default fallback
        return 5;
    }

    /**
     * Analyzes the complexity of content in the report
     */
    private ContentComplexity analyzeContentComplexity(ReportSheetDetails sheetDetails) {

        if (CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            return ContentComplexity.LOW;
        }

        double totalContentLength = 0;
        int cellCount = 0;

        // Analyze first few data rows to determine content complexity
        int rowsToAnalyze = Math.min(5, sheetDetails.getReportCellDetails().size());

        for (int i = 0; i < rowsToAnalyze; i++) {
            List<ReportCellDetails> row = sheetDetails.getReportCellDetails().get(i);
            if (!CollectionUtils.isEmpty(row)) {
                for (ReportCellDetails cell : row) {
                    if (cell != null && cell.getValue() != null) {
                        totalContentLength += String.valueOf(cell.getValue()).length();
                        cellCount++;
                    }
                }
            }
        }

        if (cellCount == 0) {
            return ContentComplexity.LOW;
        }

        double averageContentLength = totalContentLength / cellCount;

        if (averageContentLength > HIGH_CONTENT_THRESHOLD) {
            return ContentComplexity.HIGH;
        } else if (averageContentLength > MEDIUM_CONTENT_THRESHOLD) {
            return ContentComplexity.MEDIUM;
        } else {
            return ContentComplexity.LOW;
        }
    }

    /**
     * Determines the appropriate page size based on columns and content complexity
     */
    private PageSize determinePageSize(int totalColumns, ContentComplexity complexity) {

        // For high content complexity, prefer larger page sizes
        if (complexity == ContentComplexity.HIGH) {
            if (totalColumns <= PORTRAIT_A4_MAX_COLUMNS) {
                return PageSize.A4.rotate(); // Landscape A4 for more width
            } else if (totalColumns <= LANDSCAPE_A4_MAX_COLUMNS) {
                return PageSize.A3.rotate(); // Landscape A3
            } else {
                return PageSize.A3.rotate(); // Maximum size
            }
        }

        // For medium content complexity
        if (complexity == ContentComplexity.MEDIUM) {
            if (totalColumns <= PORTRAIT_A4_MAX_COLUMNS) {
                return PageSize.A4; // Portrait A4
            } else if (totalColumns <= LANDSCAPE_A4_MAX_COLUMNS) {
                return PageSize.A4.rotate(); // Landscape A4
            } else {
                return PageSize.A3.rotate(); // Landscape A3
            }
        }

        // For low content complexity (default logic)
        if (totalColumns <= PORTRAIT_A4_MAX_COLUMNS) {
            return PageSize.A4; // Portrait A4
        } else if (totalColumns <= LANDSCAPE_A4_MAX_COLUMNS) {
            return PageSize.A4.rotate(); // Landscape A4
        } else {
            return PageSize.A3.rotate(); // Landscape A3
        }
    }

    /**
     * Enum representing content complexity levels
     */
    private enum ContentComplexity {
        LOW, MEDIUM, HIGH
    }
}
