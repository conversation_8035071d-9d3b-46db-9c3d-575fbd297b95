package com.embrate.cloud.pdf.reports;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Enhanced PDF Report Generator V2 with unified logic for all report types.
 *
 * This generator provides:
 * - Intelligent column width calculation based on content analysis
 * - Dynamic font sizing based on column count and content density
 * - Automatic page size selection for optimal layout
 * - Unified processing for all modules and report types
 * - Prevention of content cutoff and overflow issues
 *
 * <AUTHOR> Assistant
 */
public class PDFReportGeneratorV2 extends PDFGenerator {

    private static final Logger logger = LogManager.getLogger(PDFReportGeneratorV2.class);
    private static final String PDF_EXTENSION = ".pdf";

    // Constants for intelligent sizing - more conservative to prevent cutoff
    private static final int PORTRAIT_A4_MAX_COLUMNS = 6;  // Reduced from 8
    private static final int LANDSCAPE_A4_MAX_COLUMNS = 12; // Reduced from 15
    private static final int LANDSCAPE_A3_MAX_COLUMNS = 20; // Reduced from 25
    private static final float MIN_FONT_SIZE = 6f;
    private static final float MAX_FONT_SIZE = 16f;
    private static final float DEFAULT_HEADER_FONT_SIZE = 11f; // Reduced from 12f
    private static final float DEFAULT_DATA_FONT_SIZE = 9f;   // Reduced from 10f
    private static final double MIN_COLUMN_WIDTH_RATIO = 0.06; // 6% minimum (increased)
    private static final double MAX_COLUMN_WIDTH_RATIO = 0.30; // 30% maximum (reduced)
    private static final double CONTENT_SAFETY_FACTOR = 0.85; // Use only 85% of available width

    public PDFReportGeneratorV2(AssetProvider assetProvider) {
        super(assetProvider);
    }

    /**
     * Main entry point for generating PDF reports from ReportDetails.
     * Uses unified logic for all report types and modules.
     *
     * @param reportDetails The report data structure containing all configuration and data
     * @param module The module context (used for logging, not flow determination)
     * @return DocumentOutput containing the generated PDF
     * @throws IOException if PDF generation fails
     */
    public DocumentOutput generateReport(ReportDetails reportDetails, Module module) throws IOException {

        // Validate input
        if (!isValidReportDetails(reportDetails)) {
            return generateEmptyReport("Empty Report");
        }

        ReportSheetDetails firstSheet = reportDetails.getReportSheetDetailsList().get(0);

        // Validate sheet data
        if (!isValidSheetDetails(firstSheet)) {
            return generateEmptyReport("No Data");
        }

        try {
            logger.debug("Generating PDF report: {} for module: {}", reportDetails.getReportName(), module);

            // Create document output
            DocumentOutput documentOutput = new DocumentOutput(
                reportDetails.getReportName() + PDF_EXTENSION,
                new ByteArrayOutputStream()
            );

            // Calculate optimal page size based on content analysis
            PageSize optimalPageSize = calculateOptimalPageSize(firstSheet);

            // Initialize document layout
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(optimalPageSize);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            // Generate complete report using unified logic
            generateUnifiedReportContent(document, documentLayoutSetup, reportDetails, firstSheet);

            document.close();
            logger.debug("Successfully generated PDF report: {}", reportDetails.getReportName());
            return documentOutput;

        } catch (Exception e) {
            logger.error("Error generating PDF report: {}", reportDetails.getReportName(), e);
            throw new IOException("Failed to generate PDF report", e);
        }
    }

    /**
     * Generates complete report content using unified logic for all report types
     */
    private void generateUnifiedReportContent(Document document, DocumentLayoutSetup documentLayoutSetup,
                                            ReportDetails reportDetails, ReportSheetDetails sheetDetails) throws IOException {

        // Generate institute header if required
        if (sheetDetails.isShowInstituteName() || sheetDetails.isShowInstituteLetterHead()) {
            generateInstituteHeader(document, documentLayoutSetup, sheetDetails);
        }

        // Generate report title if required
        if (sheetDetails.isShowCenterHeading()) {
            generateReportTitle(document, documentLayoutSetup, reportDetails.getReportName());
        }

        // Generate date/time header if required
        if (sheetDetails.isShowDateAndTime()) {
            generateDateTimeHeader(document, documentLayoutSetup);
        }

        // Generate main report table with intelligent formatting
        generateIntelligentReportTable(document, documentLayoutSetup, sheetDetails);
    }

    /**
     * Generates the main report table with intelligent column width and font sizing
     */
    private void generateIntelligentReportTable(Document document, DocumentLayoutSetup documentLayoutSetup,
                                              ReportSheetDetails sheetDetails) throws IOException {

        // Calculate intelligent column widths based on content
        float[] columnWidths = calculateIntelligentColumnWidths(sheetDetails, documentLayoutSetup.getPageSize());

        // Calculate optimal font sizes
        float headerFontSize = calculateOptimalFontSize(sheetDetails, documentLayoutSetup.getPageSize(), true);
        float dataFontSize = calculateOptimalFontSize(sheetDetails, documentLayoutSetup.getPageSize(), false);

        // Create optimized table
        Table table = createOptimizedTable(columnWidths);

        // Add header rows if present
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            addHeaderRows(table, sheetDetails.getHeaderReportCellDetails(),
                         sheetDetails.getHeaderMergeCellIndexesList(), headerFontSize);
        }

        // Add data rows
        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            addDataRows(table, sheetDetails.getReportCellDetails(),
                       sheetDetails.getMergeCellIndexesList(), dataFontSize);
        }

        document.add(table);
    }

    /**
     * Calculates optimal page size based on content analysis
     */
    private PageSize calculateOptimalPageSize(ReportSheetDetails sheetDetails) {

        // Use explicitly set page size if available
        if (sheetDetails.getPageSize() != null) {
            logger.debug("Using explicitly set page size: {}", sheetDetails.getPageSize());
            return sheetDetails.getPageSize();
        }

        // Calculate total columns
        int totalColumns = getTotalColumns(sheetDetails);

        // Analyze content complexity
        double avgContentLength = analyzeContentComplexity(sheetDetails);

        // Determine optimal page size - more aggressive sizing to prevent cutoff
        PageSize optimalSize;

        // Calculate total content width estimate
        double totalContentWidth = estimateTotalContentWidth(sheetDetails, totalColumns);

        logger.debug("Content analysis: {} columns, avg length: {}, total width estimate: {}",
                    totalColumns, avgContentLength, totalContentWidth);

        if (totalColumns > LANDSCAPE_A3_MAX_COLUMNS || totalContentWidth > 1000 || avgContentLength > 20) {
            // Very high complexity - use A3 landscape
            optimalSize = PageSize.A3.rotate();
        } else if (totalColumns > LANDSCAPE_A4_MAX_COLUMNS || totalContentWidth > 600 || avgContentLength > 12) {
            // High complexity - use A3 landscape (more aggressive)
            optimalSize = PageSize.A3.rotate();
        } else if (totalColumns > PORTRAIT_A4_MAX_COLUMNS || totalContentWidth > 300 || avgContentLength > 8) {
            // Medium complexity - use A4 landscape
            optimalSize = PageSize.A4.rotate();
        } else {
            // Low complexity - use A4 portrait
            optimalSize = PageSize.A4;
        }

        logger.debug("Calculated optimal page size: {} for {} columns (content width: {})",
                    optimalSize, totalColumns, totalContentWidth);
        return optimalSize;
    }

    /**
     * Calculates intelligent column widths based on content analysis
     */
    private float[] calculateIntelligentColumnWidths(ReportSheetDetails sheetDetails, PageSize pageSize) {

        int columnCount = getTotalColumns(sheetDetails);
        if (columnCount <= 0) {
            return new float[]{100f};
        }

        // Analyze content to determine natural widths
        double[] naturalWidths = analyzeContentWidths(sheetDetails, columnCount);

        // Convert to optimal percentages with constraints
        float[] columnWidths = convertToOptimalPercentages(naturalWidths, columnCount);

        logger.debug("Calculated column widths for {} columns: {}", columnCount, columnWidths);
        return columnWidths;
    }

    /**
     * Calculates optimal font size based on column count and content density - more aggressive reduction
     */
    private float calculateOptimalFontSize(ReportSheetDetails sheetDetails, PageSize pageSize, boolean isHeader) {

        int columnCount = getTotalColumns(sheetDetails);
        double contentDensity = analyzeContentComplexity(sheetDetails);
        double totalContentWidth = estimateTotalContentWidth(sheetDetails, columnCount);

        float baseSize = isHeader ? DEFAULT_HEADER_FONT_SIZE : DEFAULT_DATA_FONT_SIZE;

        // More aggressive column count adjustment
        if (columnCount > LANDSCAPE_A3_MAX_COLUMNS) {
            baseSize -= 3f;
        } else if (columnCount > LANDSCAPE_A4_MAX_COLUMNS) {
            baseSize -= 2.5f;
        } else if (columnCount > PORTRAIT_A4_MAX_COLUMNS) {
            baseSize -= 1.5f;
        } else if (columnCount > 4) {
            baseSize -= 0.5f;
        }

        // More aggressive content density adjustment
        if (contentDensity > 20) {
            baseSize -= 2f;
        } else if (contentDensity > 15) {
            baseSize -= 1.5f;
        } else if (contentDensity > 10) {
            baseSize -= 1f;
        } else if (contentDensity > 8) {
            baseSize -= 0.5f;
        }

        // Adjust based on total content width
        if (totalContentWidth > 1000) {
            baseSize -= 1.5f;
        } else if (totalContentWidth > 600) {
            baseSize -= 1f;
        } else if (totalContentWidth > 300) {
            baseSize -= 0.5f;
        }

        // Smaller adjustment for page size (less aggressive boost)
        if (pageSize.getWidth() > PageSize.A3.getWidth()) {
            baseSize += 0.5f;
        } else if (pageSize.getWidth() > PageSize.A4.getWidth()) {
            baseSize += 0.25f;
        }

        float finalSize = Math.max(MIN_FONT_SIZE, Math.min(MAX_FONT_SIZE, baseSize));
        logger.debug("Calculated font size: {} for {} columns, density: {}, width: {} (isHeader: {})",
                    finalSize, columnCount, contentDensity, totalContentWidth, isHeader);

        return finalSize;
    }

    /**
     * Helper methods for content analysis and table creation
     */

    private int getTotalColumns(ReportSheetDetails sheetDetails) {
        if (sheetDetails.getTotalColumns() != null) {
            return sheetDetails.getTotalColumns();
        }

        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            return sheetDetails.getHeaderReportCellDetails().get(0).size();
        }

        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails()) &&
            sheetDetails.getReportCellDetails().size() > 1) {
            return sheetDetails.getReportCellDetails().get(1).size();
        }

        return 5; // Default fallback
    }

    private double analyzeContentComplexity(ReportSheetDetails sheetDetails) {
        if (CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            return 5.0; // Default low complexity
        }

        double totalLength = 0;
        int cellCount = 0;
        int rowsToAnalyze = Math.min(5, sheetDetails.getReportCellDetails().size());

        for (int i = 0; i < rowsToAnalyze; i++) {
            List<ReportCellDetails> row = sheetDetails.getReportCellDetails().get(i);
            if (!CollectionUtils.isEmpty(row)) {
                for (ReportCellDetails cell : row) {
                    if (cell != null && cell.getValue() != null) {
                        totalLength += String.valueOf(cell.getValue()).length();
                        cellCount++;
                    }
                }
            }
        }

        return cellCount > 0 ? totalLength / cellCount : 5.0;
    }

    private double estimateTotalContentWidth(ReportSheetDetails sheetDetails, int columnCount) {
        double totalWidth = 0;

        // Analyze header content
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            for (List<ReportCellDetails> row : sheetDetails.getHeaderReportCellDetails()) {
                if (!CollectionUtils.isEmpty(row)) {
                    for (ReportCellDetails cell : row) {
                        if (cell != null && cell.getValue() != null) {
                            double cellWidth = String.valueOf(cell.getValue()).length() *
                                             (cell.getSize() > 0 ? cell.getSize() / 12.0 : 1.0);
                            totalWidth += cellWidth;
                        }
                    }
                    break; // Only analyze first header row for estimate
                }
            }
        }

        // Analyze data content (sample first 3 rows)
        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            int rowsToAnalyze = Math.min(3, sheetDetails.getReportCellDetails().size());
            double maxRowWidth = 0;

            for (int i = 0; i < rowsToAnalyze; i++) {
                List<ReportCellDetails> row = sheetDetails.getReportCellDetails().get(i);
                if (!CollectionUtils.isEmpty(row)) {
                    double rowWidth = 0;
                    for (ReportCellDetails cell : row) {
                        if (cell != null && cell.getValue() != null) {
                            double cellWidth = String.valueOf(cell.getValue()).length() *
                                             (cell.getSize() > 0 ? cell.getSize() / 12.0 : 1.0);
                            rowWidth += cellWidth;
                        }
                    }
                    maxRowWidth = Math.max(maxRowWidth, rowWidth);
                }
            }
            totalWidth = Math.max(totalWidth, maxRowWidth);
        }

        return totalWidth;
    }

    private double[] analyzeContentWidths(ReportSheetDetails sheetDetails, int columnCount) {
        double[] maxWidths = new double[columnCount];

        // Analyze header content
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            for (List<ReportCellDetails> row : sheetDetails.getHeaderReportCellDetails()) {
                analyzeRowWidths(row, maxWidths);
            }
        }

        // Analyze data content (sample first 10 rows)
        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            int rowsToAnalyze = Math.min(10, sheetDetails.getReportCellDetails().size());
            for (int i = 0; i < rowsToAnalyze; i++) {
                analyzeRowWidths(sheetDetails.getReportCellDetails().get(i), maxWidths);
            }
        }

        // Ensure minimum width for empty columns
        for (int i = 0; i < columnCount; i++) {
            if (maxWidths[i] == 0) {
                maxWidths[i] = 5; // Minimum default width
            }
        }

        return maxWidths;
    }

    private void analyzeRowWidths(List<ReportCellDetails> row, double[] maxWidths) {
        if (CollectionUtils.isEmpty(row)) {
            return;
        }

        for (int j = 0; j < Math.min(row.size(), maxWidths.length); j++) {
            ReportCellDetails cell = row.get(j);
            if (cell != null && cell.getValue() != null) {
                String content = String.valueOf(cell.getValue());
                double fontSize = cell.getSize() > 0 ? cell.getSize() : DEFAULT_DATA_FONT_SIZE;

                // More accurate width calculation with character width estimation
                double characterWidth = fontSize * 0.6; // Approximate character width
                double contentWidth = content.length() * characterWidth;

                // Add padding for cell borders and spacing
                contentWidth += 10; // 10 points padding

                maxWidths[j] = Math.max(maxWidths[j], contentWidth);
            }
        }
    }

    private float[] convertToOptimalPercentages(double[] naturalWidths, int columnCount) {
        // Calculate total width
        double totalWidth = 0;
        for (double width : naturalWidths) {
            totalWidth += width;
        }

        float[] percentages = new float[columnCount];

        if (totalWidth > 0) {
            // Check if content is too wide - if so, apply logarithmic scaling
            boolean contentTooWide = totalWidth > (600 * columnCount / 5.0); // Threshold based on column count

            if (contentTooWide) {
                logger.debug("Content too wide ({}), applying logarithmic scaling", totalWidth);
                // Apply logarithmic scaling to compress wide content
                double[] scaledWidths = new double[columnCount];
                double scaledTotal = 0;

                for (int i = 0; i < columnCount; i++) {
                    // Use log scaling to compress very wide columns
                    scaledWidths[i] = Math.log(naturalWidths[i] + 1) * 15; // Scale factor
                    scaledTotal += scaledWidths[i];
                }

                // Convert scaled widths to percentages
                for (int i = 0; i < columnCount; i++) {
                    percentages[i] = (float) ((scaledWidths[i] / scaledTotal) * 100);
                }
            } else {
                // Normal conversion to percentages
                for (int i = 0; i < columnCount; i++) {
                    percentages[i] = (float) ((naturalWidths[i] / totalWidth) * 100);
                }
            }
        } else {
            // Equal distribution fallback
            float equalWidth = 100f / columnCount;
            for (int i = 0; i < columnCount; i++) {
                percentages[i] = equalWidth;
            }
        }

        // Apply constraints and normalize
        return applyWidthConstraints(percentages);
    }

    private float[] applyWidthConstraints(float[] percentages) {
        float minWidth = (float) (MIN_COLUMN_WIDTH_RATIO * 100);
        float maxWidth = (float) (MAX_COLUMN_WIDTH_RATIO * 100);

        // Apply minimum width
        for (int i = 0; i < percentages.length; i++) {
            if (percentages[i] < minWidth) {
                percentages[i] = minWidth;
            }
        }

        // Apply maximum width and redistribute excess
        float totalExcess = 0;
        int constrainedColumns = 0;

        for (int i = 0; i < percentages.length; i++) {
            if (percentages[i] > maxWidth) {
                totalExcess += percentages[i] - maxWidth;
                percentages[i] = maxWidth;
                constrainedColumns++;
            }
        }

        // Redistribute excess to unconstrained columns
        if (totalExcess > 0 && constrainedColumns < percentages.length) {
            float redistributionPerColumn = totalExcess / (percentages.length - constrainedColumns);
            for (int i = 0; i < percentages.length; i++) {
                if (percentages[i] < maxWidth) {
                    percentages[i] = Math.min(percentages[i] + redistributionPerColumn, maxWidth);
                }
            }
        }

        // Normalize to ensure sum equals 100%
        return normalizePercentages(percentages);
    }

    private float[] normalizePercentages(float[] percentages) {
        float total = 0;
        for (float percentage : percentages) {
            total += percentage;
        }

        if (total > 0 && Math.abs(total - 100) > 0.01) {
            float factor = 100f / total;
            for (int i = 0; i < percentages.length; i++) {
                percentages[i] *= factor;
            }
        }

        return percentages;
    }

    /**
     * Table creation and content generation methods
     */

    private Table createOptimizedTable(float[] columnWidths) {
        // Apply safety factor to prevent overflow
        float safetyPercentage = (float) (CONTENT_SAFETY_FACTOR * 100);

        Table table = new Table(UnitValue.createPercentArray(columnWidths))
                .setWidth(UnitValue.createPercentValue(safetyPercentage))
                .setHorizontalAlignment(HorizontalAlignment.CENTER);

        // Set table to break across pages if needed
        table.setKeepTogether(false);

        // Add some margin to prevent edge cutoff
        table.setMarginLeft(10f);
        table.setMarginRight(10f);

        logger.debug("Created table with {}% width and {} columns", safetyPercentage, columnWidths.length);

        return table;
    }

    private void addHeaderRows(Table table, List<List<ReportCellDetails>> headerRows,
                              List<CellIndexes> mergeCellIndexes, float defaultFontSize) {

        if (CollectionUtils.isEmpty(headerRows)) {
            return;
        }

        for (List<ReportCellDetails> row : headerRows) {
            for (ReportCellDetails cellDetails : row) {
                Cell cell = createOptimizedCell(cellDetails, defaultFontSize, true);
                table.addCell(cell);
            }
        }
    }

    private void addDataRows(Table table, List<List<ReportCellDetails>> dataRows,
                            List<CellIndexes> mergeCellIndexes, float defaultFontSize) {

        if (CollectionUtils.isEmpty(dataRows)) {
            return;
        }

        for (List<ReportCellDetails> row : dataRows) {
            for (ReportCellDetails cellDetails : row) {
                Cell cell = createOptimizedCell(cellDetails, defaultFontSize, false);
                table.addCell(cell);
            }
        }
    }

    private Cell createOptimizedCell(ReportCellDetails cellDetails, float defaultFontSize, boolean isHeader) {

        Cell cell = new Cell();

        // Create content with truncation if too long
        String value = StringHelper.convertObjectToStringValue(cellDetails.getValue(), cellDetails.getDataType());
        if (value == null) {
            value = "";
        }

        // Truncate very long content to prevent overflow
        if (value.length() > 50) {
            value = value.substring(0, 47) + "...";
        }

        Paragraph paragraph = new Paragraph(value);

        // Set font size - use cell's size if valid, otherwise use calculated default
        float fontSize = cellDetails.getSize() > 0 && cellDetails.getSize() >= MIN_FONT_SIZE &&
                        cellDetails.getSize() <= MAX_FONT_SIZE ? cellDetails.getSize() : defaultFontSize;
        paragraph.setFontSize(fontSize);

        // Set bold formatting
        if (cellDetails.isBold()) {
            paragraph.setBold();
        }

        // Set text alignment
        TextAlignment textAlignment = convertHorizontalAlignment(cellDetails.getReportTextHorizontalAlignment());
        paragraph.setTextAlignment(textAlignment);

        // Set vertical alignment
        VerticalAlignment verticalAlignment = convertVerticalAlignment(cellDetails.getReportTextVerticalAlignment());
        cell.setVerticalAlignment(verticalAlignment);

        // Set colors
        if (cellDetails.getTextColor() != null) {
            Color textColor = createColorFromHex(cellDetails.getTextColor());
            paragraph.setFontColor(textColor);
        }

        if (cellDetails.getCellColor() != null) {
            Color cellColor = createColorFromHex(cellDetails.getCellColor());
            cell.setBackgroundColor(cellColor);
        }

        // Set cell height if specified
        if (cellDetails.getCellHeight() > 0) {
            cell.setHeight(cellDetails.getCellHeight());
        }

        // Enable text wrapping and prevent overflow
        cell.setKeepTogether(false);
        paragraph.setKeepTogether(false);

        // Add padding to prevent text from touching cell borders
        cell.setPadding(2f);

        cell.add(paragraph);
        return cell;
    }

    /**
     * Utility methods for alignment and color conversion
     */

    private TextAlignment convertHorizontalAlignment(ReportHorizontalTextAlignment alignment) {
        if (alignment == null) {
            return TextAlignment.LEFT;
        }

        switch (alignment) {
            case CENTER:
                return TextAlignment.CENTER;
            case RIGHT:
                return TextAlignment.RIGHT;
            case LEFT:
            default:
                return TextAlignment.LEFT;
        }
    }

    private VerticalAlignment convertVerticalAlignment(ReportVerticalTextAlignment alignment) {
        if (alignment == null) {
            return VerticalAlignment.MIDDLE;
        }

        switch (alignment) {
            case TOP:
                return VerticalAlignment.TOP;
            case BOTTOM:
                return VerticalAlignment.BOTTOM;
            case MIDDLE:
            default:
                return VerticalAlignment.MIDDLE;
        }
    }

    private Color createColorFromHex(String hexColor) {
        try {
            List<Integer> rgb = EColorUtils.hex2Rgb(hexColor);
            return new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2));
        } catch (Exception e) {
            logger.warn("Failed to parse color: {}, using black", hexColor);
            return new DeviceRgb(0, 0, 0); // Default to black
        }
    }

    /**
     * Header generation methods
     */

    private void generateInstituteHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                                       ReportSheetDetails reportSheetDetails) {
        try {
            ReportHeaderGenerator headerGenerator = new ReportHeaderGenerator(assetProvider);
            headerGenerator.generateInstituteHeader(document, documentLayoutSetup, reportSheetDetails);
        } catch (Exception e) {
            logger.error("Error generating institute header", e);
        }
    }

    private void generateReportTitle(Document document, DocumentLayoutSetup documentLayoutSetup, String reportName) {
        try {
            ReportHeaderGenerator headerGenerator = new ReportHeaderGenerator(assetProvider);
            headerGenerator.generateReportTitle(document, documentLayoutSetup, reportName);
        } catch (Exception e) {
            logger.error("Error generating report title: {}", reportName, e);
        }
    }

    private void generateDateTimeHeader(Document document, DocumentLayoutSetup documentLayoutSetup) {
        try {
            ReportHeaderGenerator headerGenerator = new ReportHeaderGenerator(assetProvider);
            headerGenerator.generateDateTimeHeader(document, documentLayoutSetup);
        } catch (Exception e) {
            logger.error("Error generating date/time header", e);
        }
    }

    /**
     * Validation methods
     */

    private boolean isValidReportDetails(ReportDetails reportDetails) {
        return reportDetails != null &&
               !CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList()) &&
               !CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList().get(0).getReportCellDetails());
    }

    private boolean isValidSheetDetails(ReportSheetDetails sheetDetails) {
        if (sheetDetails == null || sheetDetails.getReportCellDetails() == null) {
            return false;
        }

        // Check if we have valid data rows (more than just header)
        if (sheetDetails.getReportCellDetails().size() <= 1) {
            return false;
        }

        // Check if second row (first data row) has content
        return !CollectionUtils.isEmpty(sheetDetails.getReportCellDetails().get(1));
    }

    private DocumentOutput generateEmptyReport(String message) throws IOException {
        DocumentOutput documentOutput = new DocumentOutput(message + PDF_EXTENSION, new ByteArrayOutputStream());
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        // Add simple message using header generator
        try {
            ReportHeaderGenerator headerGenerator = new ReportHeaderGenerator(assetProvider);
            headerGenerator.generateReportTitle(document, documentLayoutSetup, message);
        } catch (Exception e) {
            logger.error("Error generating empty report message: {}", message, e);
        }

        document.close();
        return documentOutput;
    }
}
