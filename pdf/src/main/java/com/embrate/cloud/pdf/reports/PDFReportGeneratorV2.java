package com.embrate.cloud.pdf.reports;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Enhanced PDF Report Generator V2 that handles dynamic column widths,
 * font sizing, and page sizing based on content analysis.
 * 
 * Follows SOLID principles with separated concerns:
 * - Single Responsibility: Focuses only on PDF generation orchestration
 * - Open/Closed: Extensible through strategy pattern for different report types
 * - Liskov Substitution: Can replace original generator
 * - Interface Segregation: Uses focused interfaces
 * - Dependency Inversion: Depends on abstractions
 * 
 * <AUTHOR> Assistant
 */
public class PDFReportGeneratorV2 extends PDFGenerator {
    
    private static final Logger logger = LogManager.getLogger(PDFReportGeneratorV2.class);
    private static final String PDF_EXTENSION = ".pdf";
    
    // Strategy components following SOLID principles
    private final PageSizeCalculator pageSizeCalculator;
    private final ColumnWidthCalculator columnWidthCalculator;
    private final FontSizeCalculator fontSizeCalculator;
    private final ReportHeaderGenerator headerGenerator;
    private final ReportTableGenerator tableGenerator;
    
    public PDFReportGeneratorV2(AssetProvider assetProvider) {
        super(assetProvider);
        
        // Initialize strategy components
        this.pageSizeCalculator = new PageSizeCalculator();
        this.columnWidthCalculator = new ColumnWidthCalculator();
        this.fontSizeCalculator = new FontSizeCalculator();
        this.headerGenerator = new ReportHeaderGenerator(assetProvider);
        this.tableGenerator = new ReportTableGenerator();
    }
    
    /**
     * Main entry point for generating PDF reports from ReportDetails
     * 
     * @param reportDetails The report data structure containing all configuration and data
     * @param module The module context for determining processing flow
     * @return DocumentOutput containing the generated PDF
     * @throws IOException if PDF generation fails
     */
    public DocumentOutput generateReport(ReportDetails reportDetails, Module module) throws IOException {
        
        // Validate input
        if (!isValidReportDetails(reportDetails)) {
            return generateEmptyReport("Empty Report");
        }
        
        ReportSheetDetails firstSheet = reportDetails.getReportSheetDetailsList().get(0);
        
        // Validate sheet data
        if (!isValidSheetDetails(firstSheet)) {
            return generateEmptyReport("No Data");
        }
        
        try {
            // Create document output
            DocumentOutput documentOutput = new DocumentOutput(
                reportDetails.getReportName() + PDF_EXTENSION, 
                new ByteArrayOutputStream()
            );
            
            // Calculate optimal page size based on content
            PageSize optimalPageSize = pageSizeCalculator.calculateOptimalPageSize(firstSheet);
            
            // Initialize document layout
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(optimalPageSize);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
            
            // Generate report content
            generateReportContent(document, documentLayoutSetup, reportDetails, firstSheet, module);
            
            document.close();
            return documentOutput;
            
        } catch (Exception e) {
            logger.error("Error generating PDF report: {}", reportDetails.getReportName(), e);
            throw new IOException("Failed to generate PDF report", e);
        }
    }
    
    /**
     * Validates ReportDetails structure
     */
    private boolean isValidReportDetails(ReportDetails reportDetails) {
        return reportDetails != null && 
               !CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList()) &&
               !CollectionUtils.isEmpty(reportDetails.getReportSheetDetailsList().get(0).getReportCellDetails());
    }
    
    /**
     * Validates ReportSheetDetails structure
     */
    private boolean isValidSheetDetails(ReportSheetDetails sheetDetails) {
        if (sheetDetails == null || sheetDetails.getReportCellDetails() == null) {
            return false;
        }
        
        // Check if we have valid data rows (more than just header)
        if (sheetDetails.getReportCellDetails().size() <= 1) {
            return false;
        }
        
        // Check if second row (first data row) has content
        return !CollectionUtils.isEmpty(sheetDetails.getReportCellDetails().get(1));
    }
    
    /**
     * Generates an empty report for error cases
     */
    private DocumentOutput generateEmptyReport(String message) throws IOException {
        DocumentOutput documentOutput = new DocumentOutput(message + PDF_EXTENSION, new ByteArrayOutputStream());
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        
        // Add simple message
        generateHeading(document, documentLayoutSetup, getRegularBoldFont(), message);
        
        document.close();
        return documentOutput;
    }
    
    /**
     * Generates the complete report content including headers and tables
     */
    private void generateReportContent(Document document, DocumentLayoutSetup documentLayoutSetup, 
                                     ReportDetails reportDetails, ReportSheetDetails sheetDetails, 
                                     Module module) throws IOException {
        
        // Generate institute header if required
        if (sheetDetails.isShowInstituteName() || sheetDetails.isShowInstituteLetterHead()) {
            headerGenerator.generateInstituteHeader(document, documentLayoutSetup, sheetDetails);
        }
        
        // Generate report header if required
        if (sheetDetails.isShowCenterHeading()) {
            headerGenerator.generateReportTitle(document, documentLayoutSetup, reportDetails.getReportName());
        }
        
        // Generate date/time if required
        if (sheetDetails.isShowDateAndTime()) {
            headerGenerator.generateDateTimeHeader(document, documentLayoutSetup);
        }
        
        // Generate main report table
        tableGenerator.generateReportTable(document, documentLayoutSetup, sheetDetails, module, 
                                         columnWidthCalculator, fontSizeCalculator);
    }
    
    /**
     * Determines if new flow should be used based on module
     */
    private boolean shouldUseNewFlow(Module module) {
        if (module == null) {
            return false;
        }
        return module == Module.EXAMINATION || 
               module == Module.FEES || 
               module == Module.HOMEWORK_MANAGEMENT || 
               module == Module.STUDENT_FINANCE;
    }
}
