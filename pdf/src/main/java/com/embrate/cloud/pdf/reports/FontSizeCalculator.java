package com.embrate.cloud.pdf.reports;

import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Calculates optimal font sizes based on content density, column count, and page size.
 * 
 * This class implements intelligent font sizing that:
 * - Adjusts font size based on number of columns to ensure readability
 * - Considers content density and length
 * - Maintains minimum readable font sizes
 * - Provides different sizes for headers vs data
 * 
 * Follows Single Responsibility Principle by focusing only on font size calculations.
 * 
 * <AUTHOR> Assistant
 */
public class FontSizeCalculator {
    
    private static final Logger logger = LogManager.getLogger(FontSizeCalculator.class);
    
    // Font size constants
    private static final float DEFAULT_HEADER_FONT_SIZE = 12f;
    private static final float DEFAULT_DATA_FONT_SIZE = 10f;
    private static final float MIN_FONT_SIZE = 6f;
    private static final float MAX_FONT_SIZE = 16f;
    
    // Column count thresholds for font size adjustment
    private static final int LOW_COLUMN_THRESHOLD = 5;
    private static final int MEDIUM_COLUMN_THRESHOLD = 10;
    private static final int HIGH_COLUMN_THRESHOLD = 15;
    
    // Content density thresholds
    private static final int HIGH_CONTENT_DENSITY_THRESHOLD = 20;
    private static final int MEDIUM_CONTENT_DENSITY_THRESHOLD = 10;
    
    /**
     * Calculates optimal font size for header content
     * 
     * @param sheetDetails The report sheet containing data
     * @param pageSize The page size being used
     * @return Optimal header font size
     */
    public float calculateHeaderFontSize(ReportSheetDetails sheetDetails, PageSize pageSize) {
        
        int columnCount = getColumnCount(sheetDetails);
        ContentDensity density = analyzeHeaderContentDensity(sheetDetails);
        
        float baseFontSize = calculateBaseFontSize(columnCount, pageSize, true);
        float adjustedSize = adjustForContentDensity(baseFontSize, density);
        
        float finalSize = Math.max(MIN_FONT_SIZE, Math.min(MAX_FONT_SIZE, adjustedSize));
        
        logger.debug("Calculated header font size: {} for {} columns with {} density", 
                    finalSize, columnCount, density);
        
        return finalSize;
    }
    
    /**
     * Calculates optimal font size for data content
     * 
     * @param sheetDetails The report sheet containing data
     * @param pageSize The page size being used
     * @return Optimal data font size
     */
    public float calculateDataFontSize(ReportSheetDetails sheetDetails, PageSize pageSize) {
        
        int columnCount = getColumnCount(sheetDetails);
        ContentDensity density = analyzeDataContentDensity(sheetDetails);
        
        float baseFontSize = calculateBaseFontSize(columnCount, pageSize, false);
        float adjustedSize = adjustForContentDensity(baseFontSize, density);
        
        float finalSize = Math.max(MIN_FONT_SIZE, Math.min(MAX_FONT_SIZE, adjustedSize));
        
        logger.debug("Calculated data font size: {} for {} columns with {} density", 
                    finalSize, columnCount, density);
        
        return finalSize;
    }
    
    /**
     * Gets the font size for a specific cell, considering its individual properties
     * 
     * @param cell The cell to get font size for
     * @param defaultSize The default size calculated for the report
     * @return Font size for the specific cell
     */
    public float getCellFontSize(ReportCellDetails cell, float defaultSize) {
        
        if (cell == null) {
            return defaultSize;
        }
        
        // Use cell's specific size if it's reasonable
        int cellSize = cell.getSize();
        if (cellSize > 0 && cellSize >= MIN_FONT_SIZE && cellSize <= MAX_FONT_SIZE) {
            return cellSize;
        }
        
        return defaultSize;
    }
    
    /**
     * Determines the number of columns in the report
     */
    private int getColumnCount(ReportSheetDetails sheetDetails) {
        
        if (sheetDetails.getTotalColumns() != null) {
            return sheetDetails.getTotalColumns();
        }
        
        if (!CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            return sheetDetails.getHeaderReportCellDetails().get(0).size();
        }
        
        if (!CollectionUtils.isEmpty(sheetDetails.getReportCellDetails()) && 
            sheetDetails.getReportCellDetails().size() > 1) {
            return sheetDetails.getReportCellDetails().get(1).size();
        }
        
        return 5; // Default fallback
    }
    
    /**
     * Calculates base font size based on column count and page size
     */
    private float calculateBaseFontSize(int columnCount, PageSize pageSize, boolean isHeader) {
        
        float baseSize = isHeader ? DEFAULT_HEADER_FONT_SIZE : DEFAULT_DATA_FONT_SIZE;
        
        // Adjust based on column count
        if (columnCount <= LOW_COLUMN_THRESHOLD) {
            // Few columns - can use larger font
            baseSize += 1f;
        } else if (columnCount <= MEDIUM_COLUMN_THRESHOLD) {
            // Medium columns - use default
            // No adjustment
        } else if (columnCount <= HIGH_COLUMN_THRESHOLD) {
            // Many columns - reduce font size
            baseSize -= 1f;
        } else {
            // Very many columns - significantly reduce font size
            baseSize -= 2f;
        }
        
        // Adjust based on page size
        if (pageSize.getWidth() > PageSize.A4.getWidth()) {
            // Larger page - can afford slightly larger font
            baseSize += 0.5f;
        }
        
        return baseSize;
    }
    
    /**
     * Adjusts font size based on content density
     */
    private float adjustForContentDensity(float baseSize, ContentDensity density) {
        
        switch (density) {
            case HIGH:
                // High density content needs smaller font to fit
                return baseSize - 1f;
            case MEDIUM:
                // Medium density - slight reduction
                return baseSize - 0.5f;
            case LOW:
            default:
                // Low density - can use base size or slightly larger
                return baseSize;
        }
    }
    
    /**
     * Analyzes content density in header rows
     */
    private ContentDensity analyzeHeaderContentDensity(ReportSheetDetails sheetDetails) {
        
        if (CollectionUtils.isEmpty(sheetDetails.getHeaderReportCellDetails())) {
            return ContentDensity.LOW;
        }
        
        return analyzeContentDensity(sheetDetails.getHeaderReportCellDetails());
    }
    
    /**
     * Analyzes content density in data rows
     */
    private ContentDensity analyzeDataContentDensity(ReportSheetDetails sheetDetails) {
        
        if (CollectionUtils.isEmpty(sheetDetails.getReportCellDetails())) {
            return ContentDensity.LOW;
        }
        
        // Analyze first few rows for performance
        List<List<ReportCellDetails>> rowsToAnalyze = sheetDetails.getReportCellDetails()
                .subList(0, Math.min(5, sheetDetails.getReportCellDetails().size()));
        
        return analyzeContentDensity(rowsToAnalyze);
    }
    
    /**
     * Analyzes content density for a set of rows
     */
    private ContentDensity analyzeContentDensity(List<List<ReportCellDetails>> rows) {
        
        double totalContentLength = 0;
        int cellCount = 0;
        
        for (List<ReportCellDetails> row : rows) {
            if (!CollectionUtils.isEmpty(row)) {
                for (ReportCellDetails cell : row) {
                    if (cell != null && cell.getValue() != null) {
                        totalContentLength += String.valueOf(cell.getValue()).length();
                        cellCount++;
                    }
                }
            }
        }
        
        if (cellCount == 0) {
            return ContentDensity.LOW;
        }
        
        double averageContentLength = totalContentLength / cellCount;
        
        if (averageContentLength > HIGH_CONTENT_DENSITY_THRESHOLD) {
            return ContentDensity.HIGH;
        } else if (averageContentLength > MEDIUM_CONTENT_DENSITY_THRESHOLD) {
            return ContentDensity.MEDIUM;
        } else {
            return ContentDensity.LOW;
        }
    }
    
    /**
     * Enum representing content density levels
     */
    private enum ContentDensity {
        LOW, MEDIUM, HIGH
    }
}
