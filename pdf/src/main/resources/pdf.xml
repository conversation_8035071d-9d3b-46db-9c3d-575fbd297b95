<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd" default-lazy-init="true">

	<context:annotation-config />
	<context:property-placeholder
		location="classpath*:core-lib-${lernen_env}.properties, classpath*:dao-tier-${lernen_env}.properties, classpath*:utils-${lernen_env}.properties"
		system-properties-mode="OVERRIDE" />

	<import resource="classpath:core-lib.xml" />
	<import resource="classpath:core-utils.xml" />

	<bean id="examReportSerivceProvider"
		class="com.lernen.cloud.pdf.exam.reports.ExamReportSerivceProvider">
		<constructor-arg name="examReportCardManager" ref="examReportCardManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="demandNoticeHandler"
		class="com.lernen.cloud.pdf.demand.notice.DemandNoticeHandler">
		<constructor-arg name="feePaymentManager" ref="feePaymentManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="admitCardHandler"
		class="com.lernen.cloud.pdf.admitcard.AdmitCardHandler">
		<constructor-arg name="examinationManager" ref="examinationManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="courseManager" ref="courseManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="admissionFormHandler"
		class="com.embrate.cloud.pdf.admission.form.AdmissionFormHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="studentFeesChartHandler"
		class="com.embrate.cloud.pdf.studentfeeschart.StudentFeesChartHandler">
		<constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="boardRegistrationFormHandler"
		class="com.embrate.cloud.pdf.boardregistrationform.BoardRegistrationFormHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="studentIdentityCardHandler"
		class="com.lernen.cloud.pdf.identitycard.student.StudentIdentityCardHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="staffManager" ref="staffManager" />
		<constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager"/>
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>
<!--	<constructor-arg name="pdfHandlerFunctionName" value="PdfRequestHandler-${lernen_env}" />-->

	<bean id="staffIdentityCardHandler"
		  class="com.lernen.cloud.pdf.identitycard.staff.StaffIdentityCardHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="staffManager" ref="staffManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="studentMarksFeedDocumentHandler"
		class="com.embrate.cloud.pdf.exam.document.studentmarks.StudentMarksFeedDocumentHandler">
		<constructor-arg name="examinationManager" ref="examinationManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="timetableManager" ref="timetableManager" />
	</bean>


	<bean id="studyCertificateDocumentHandler"
		class="com.lernen.cloud.pdf.certificates.study.StudyCertificateDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
	</bean>

	<bean id="bonafideCertificateDocumentHandler"
		class="com.lernen.cloud.pdf.certificates.bonafide.BonafideCertificateDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
	</bean>

	<bean id="transferCertificateHandler"
		class="com.lernen.cloud.pdf.certificates.transfer.TransferCertificateHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="promotionCertificateDocumentHandler"
		class="com.lernen.cloud.pdf.certificates.promotion.PromotionCertificateDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="tuitionFeesCertificateDocumentHandler"
		class="com.lernen.cloud.pdf.certificates.tuition.TuitionFeesCertificateDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager" />
		<constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="storeInventoryInvoiceGeneratorFactory"
		  class="com.embrate.cloud.pdf.inventory.store.StoreInventoryInvoiceGeneratorFactory">
	</bean>

	<bean id="storeInventoryInvoiceGeneratorFactoryV2"
		  class="com.embrate.cloud.pdf.inventory.store.v2.StoreInventoryInvoiceGeneratorFactory">
	</bean>

	<bean id="storeInventoryPDFInvoiceHandler"
		  class="com.embrate.cloud.pdf.inventory.store.StoreInventoryPDFInvoiceHandler">
		<constructor-arg name="productTransactionsManager" ref="productTransactionsManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="storeInventoryPDFInvoiceHandlerV2"
		  class="com.embrate.cloud.pdf.inventory.store.v2.StoreInventoryPDFInvoiceHandler">
		<constructor-arg name="inventoryTransactionsManager" ref="inventoryTransactionsManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="datesheetHandler"
		  class="com.lernen.cloud.pdf.datesheet.DatesheetHandler">
		<constructor-arg name="examinationManager" ref="examinationManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="timetableHandler"
		  class="com.lernen.cloud.pdf.timetable.TimetableHandler">
		<constructor-arg name="timetableManager" ref="timetableManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="pdfReportGenerator" class="com.embrate.cloud.pdf.reports.PDFReportGeneratorV2">
		<constructor-arg name="assetProvider" ref="assetProvider"/>
	</bean>

	<!-- Legacy generator for backward compatibility if needed -->
	<bean id="pdfReportGeneratorLegacy" class="com.embrate.cloud.pdf.reports.PdfReportGenerator">
		<constructor-arg name="assetProvider" ref="assetProvider"/>
	</bean>

	<bean id="characterCertificateDocumentHandler"
		  class="com.lernen.cloud.pdf.certificates.character.CharacterCertificateDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="birthdayCertificateDocumentHandler"
		  class="com.lernen.cloud.pdf.certificates.birthday.BirthdayCertificateDocumentHandler">
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="dynamicDocumentHandler"
		  class="com.lernen.cloud.pdf.certificates.dynamic.DynamicDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="gatePassHandler" class="com.lernen.cloud.pdf.gatepass.GatePassHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="frontDeskManager" ref="frontDeskManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="feeInvoiceHandler" class="com.lernen.cloud.pdf.invoice.fee.FeeInvoiceHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="feePaymentManager" ref="feePaymentManager" />
		<constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="experienceLetterDocumentHandler" class="com.lernen.cloud.pdf.certificates.staff.experience.ExperienceLetterDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="staffManager" ref="staffManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="bookReceiptDocumentHandler"
		  class="com.embrate.cloud.pdf.bookreceipt.BookReceiptDocumentHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="userCredentialsPDFGenerator" class="com.embrate.cloud.pdf.user.UserCredentialsPDFGenerator">
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="staffManager" ref="staffManager" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="trackingEventPDFGenerator" class="com.embrate.cloud.pdf.tracking.event.TrackingEventPDFGenerator">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="trackingEventsManager" ref="trackingEventsManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="visitorIdentityCardHandler"
		  class="com.lernen.cloud.pdf.visitor.identitycard.VisitorIdentityCardHandler">
		<constructor-arg name="visitorDetailsManager" ref="visitorDetailsManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
	</bean>

	<bean id="globalHPCGenerator"
		  class="com.embrate.cloud.pdf.hpc.GlobalHPCGenerator">
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="hpcGeneratorFactory"
		  class="com.embrate.cloud.pdf.hpc.HPCGeneratorFactory">
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>


	<bean id="hpcPdfServiceProvider"
		  class="com.embrate.cloud.pdf.hpc.HPCPdfServiceProvider">
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="hpcFormManager" ref="hpcFormManager" />
		<constructor-arg name="hpcGeneratorFactory" ref="hpcGeneratorFactory" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="staffManager" ref="staffManager" />
	</bean>


	<bean id="walletInvoiceHandler" class="com.lernen.cloud.pdf.invoice.wallet.WalletInvoiceHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userWalletManager" ref="userWalletManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<bean id="feeChallaHandler"
		  class="com.embrate.cloud.pdf.fees.FeeChallaHandler">
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="assetProvider" ref="assetProvider" />
	</bean>

	<context:component-scan base-package="com.lernen.cloud.pdf, com.embrate.cloud.pdf" />
	<!-- <aop:aspectj-autoproxy proxy-target-class="true" /> -->
</beans>
